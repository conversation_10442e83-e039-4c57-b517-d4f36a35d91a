#!/usr/bin/env python3
"""
Complete Job Processing Pipeline with LinkedIn Redirect Extraction

This script runs the complete pipeline:
1. Fetch today's data from S3
2. Process data (including all columns)
3. Extract final LinkedIn apply links
4. Save results
"""

import os
import sys
from datetime import datetime

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fetch_today_data import fetch_today_data
from data_preprocessor import main as preprocess_data
from linkedin_redirect_extractor import LinkedInRedirectExtractor
import pandas as pd

def main():
    """
    Run the complete job processing pipeline with LinkedIn redirect extraction.
    """
    print("🚀 Complete Job Processing Pipeline with LinkedIn Redirects")
    print("=" * 70)
    
    try:
        # Step 1: Fetch today's data from S3
        print("\n📥 Step 1: Fetching today's data from S3...")
        fetch_today_data()
        print("✅ Data fetching completed")
        
        # Step 2: Process the data (including all columns)
        print("\n⚙️ Step 2: Processing data with all columns...")
        preprocess_data()
        print("✅ Data processing completed")
        
        # Step 3: Load LinkedIn data and extract final apply links
        print("\n🔗 Step 3: Extracting final LinkedIn apply links...")
        
        # Check if LinkedIn combined file exists
        linkedin_file = "linkedin_combined.csv"
        if not os.path.exists(linkedin_file):
            print(f"❌ LinkedIn file not found: {linkedin_file}")
            print("Please check if data processing completed successfully")
            return
        
        # Load LinkedIn data
        print(f"📂 Loading LinkedIn data from {linkedin_file}...")
        linkedin_df = pd.read_csv(linkedin_file)
        print(f"✅ Loaded {len(linkedin_df)} LinkedIn jobs")
        
        # Extract final apply links
        extractor = LinkedInRedirectExtractor(
            delay_between_requests=2,  # 2 seconds between requests
            max_redirects=10,
            timeout=15
        )
        
        print("🔄 Following redirects to get final company apply links...")
        linkedin_with_final_links = extractor.extract_final_links(linkedin_df)
        
        # Step 4: Save results
        print("\n💾 Step 4: Saving results...")
        
        # Save LinkedIn with final links
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        linkedin_output = f"linkedin_with_final_links_{timestamp}.csv"
        linkedin_with_final_links.to_csv(linkedin_output, index=False)
        print(f"✅ LinkedIn with final links saved: {linkedin_output}")
        
        # Update the main combined file
        print("🔄 Updating main combined file...")
        
        # Load the main combined file
        combined_file = "combined_data/combined_jobs.csv"
        if os.path.exists(combined_file):
            combined_df = pd.read_csv(combined_file)
            
            # Update LinkedIn rows with final apply links
            linkedin_mask = combined_df['source'] == 'LinkedIn'
            if linkedin_mask.any():
                # Create a mapping of job titles to final apply links
                title_to_final_link = dict(zip(
                    linkedin_with_final_links['job_title'], 
                    linkedin_with_final_links['final_apply_link']
                ))
                
                # Update the combined dataframe
                combined_df.loc[linkedin_mask, 'final_apply_link'] = combined_df.loc[linkedin_mask, 'job_title'].map(title_to_final_link)
                
                # Save updated combined file
                combined_df.to_csv(combined_file, index=False)
                print(f"✅ Updated combined file: {combined_file}")
            else:
                print("⚠️ No LinkedIn data found in combined file")
        else:
            print(f"⚠️ Combined file not found: {combined_file}")
        
        # Summary
        print(f"\n🎉 Pipeline completed successfully!")
        print(f"📊 LinkedIn jobs processed: {len(linkedin_with_final_links)}")
        print(f"📁 LinkedIn output: {linkedin_output}")
        print(f"📁 Combined output: {combined_file}")
        
        # Show sample results
        print(f"\n📋 Sample LinkedIn results:")
        sample_df = linkedin_with_final_links[['job_title', 'company_name', 'apply_link', 'final_apply_link']].head(3)
        for idx, row in sample_df.iterrows():
            print(f"  {idx+1}. {row['job_title']} at {row['company_name']}")
            print(f"     Original: {str(row['apply_link'])[:60]}...")
            print(f"     Final:    {str(row['final_apply_link'])[:60]}...")
            print()
        
    except Exception as e:
        print(f"❌ Error in pipeline: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
