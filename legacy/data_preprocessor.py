#!/usr/bin/env python3
"""
Data Preprocessing Script for Job Scraper
Combines LinkedIn and Indeed job data into a unified format
"""

import os
import json
import re
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path


def load_linkedin_data(folder_path='/Users/<USER>/Documents/scrapper/src/linkedin_data'):
    """
    Load and concatenate all CSV files from the linkedin_data folder.
    Returns a pandas DataFrame.
    """
    all_dfs = []
    if not os.path.exists(folder_path):
        print(f"⚠️ Folder {folder_path} does not exist")
        return pd.DataFrame()
    
    csv_files = [f for f in os.listdir(folder_path) if f.endswith('.csv')]
    if not csv_files:
        print(f"⚠️ No CSV files found in {folder_path}")
        return pd.DataFrame()
    
    for filename in csv_files:
        file_path = os.path.join(folder_path, filename)
        try:
            df = pd.read_csv(file_path)
            all_dfs.append(df)
            print(f"✅ LinkedIn {filename}: {len(df)} rows loaded")
        except Exception as e:
            print(f"❌ Error loading {filename}: {str(e)}")
    
    if all_dfs:
        combined_df = pd.concat(all_dfs, ignore_index=True)
        print(f"📊 LinkedIn: {len(combined_df)} rows from {len(all_dfs)} files")
        return combined_df
    else:
        return pd.DataFrame()


def load_indeed_data(folder_path='/Users/<USER>/Documents/scrapper/src/indeed_data'):
    """
    Load and concatenate all CSV files from the indeed_data folder.
    Returns a pandas DataFrame.
    """
    all_dfs = []
    if not os.path.exists(folder_path):
        print(f"⚠️ Folder {folder_path} does not exist")
        return pd.DataFrame()
    
    csv_files = [f for f in os.listdir(folder_path) if f.endswith('.csv')]
    if not csv_files:
        print(f"⚠️ No CSV files found in {folder_path}")
        return pd.DataFrame()
    
    for filename in csv_files:
        file_path = os.path.join(folder_path, filename)
        try:
            df = pd.read_csv(file_path)
            all_dfs.append(df)
            print(f"✅ Indeed {filename}: {len(df)} rows loaded")
        except Exception as e:
            print(f"❌ Error loading {filename}: {str(e)}")
    
    if all_dfs:
        combined_df = pd.concat(all_dfs, ignore_index=True)
        print(f"📊 Indeed: {len(combined_df)} rows from {len(all_dfs)} files")
        return combined_df
    else:
        return pd.DataFrame()


def extract_years_of_experience(text):
    """
    Extract years of experience from job description or title
    Returns empty string if not found or > 3 years (entry-level focus)
    """
    if pd.isna(text):
        return ""
    
    text_lower = text.lower()
    
    # More specific regex patterns to avoid dates and other numbers
    # Look for experience-specific contexts with word boundaries
    experience_patterns = [
        r"(?:at least|minimum|over|more than|with|have|require[sd]?|need|must have)\s*(\d+)[\s]*(?:\+|plus)?\s*(?:[-–—to]\s*(\d+)\s*)?years?\s*(?:of\s*)?(?:experience|exp)\b",
        r"\b(\d+)[\s]*(?:\+|plus)?\s*(?:[-–—to]\s*(\d+)\s*)?years?\s*(?:of\s*)?(?:experience|exp)\b",
        r"\b(\d+)[\s]*(?:\+|plus)?\s*(?:[-–—to]\s*(\d+)\s*)?(?:yrs?|years?)\s*(?:experience|exp)\b"
    ]
    
    all_numbers = []
    
    for pattern in experience_patterns:
        matches = re.findall(pattern, text_lower, re.IGNORECASE)
        
        for match in matches:
            if match[0]:  # First number
                try:
                    num = int(match[0])
                    if 0 <= num <= 3:  # Only entry-level (0-3 years)
                        all_numbers.append(num)
                except ValueError:
                    continue
            if match[1]:  # Second number (in range)
                try:
                    num = int(match[1])
                    if 0 <= num <= 3:  # Only entry-level
                        all_numbers.append(num)
                except ValueError:
                    continue
    
    if not all_numbers:
        return ""
    
    # Always return the minimum requirement (entry-level focus)
    return str(min(all_numbers))


def filter_senior_roles_linkedin(df):
    """
    Filter out senior roles from LinkedIn data based on job_seniority column
    Keep: Associate, Entry level, Internship, Not Applicable
    Remove: Director, Mid-Senior level, Executive, Senior level
    """
    if df.empty:
        return df
    
    initial_count = len(df)
    
    # First check if we have the seniority column
    if 'job_seniority_level' in df.columns:
        seniority_col = 'job_seniority_level'
    elif 'job_seniority' in df.columns:
        seniority_col = 'job_seniority'
    else:
        print("⚠️ No seniority column found, applying title-based filtering only")
        return filter_by_job_title(df, 'LinkedIn')
    
    # Define roles to keep (junior/entry level roles)
    keep_roles = ['Associate', 'Entry level', 'Internship', 'Not Applicable']
    
    # Show current seniority distribution for debugging
    if seniority_col in df.columns:
        seniority_counts = df[seniority_col].value_counts()
        print(f"🔍 LinkedIn seniority distribution: {dict(seniority_counts)}")
    
    # Filter to keep only junior roles
    filtered_df = df[df[seniority_col].isin(keep_roles)].copy()
    
    print(f"🔍 LinkedIn seniority filtering: {initial_count} → {len(filtered_df)} jobs (removed {initial_count - len(filtered_df)} senior roles)")
    
    # Additional title-based filtering for LinkedIn
    if not filtered_df.empty:
        filtered_df = filter_by_job_title(filtered_df, 'LinkedIn')
    
    return filtered_df


def filter_senior_roles_indeed(df):
    """
    Filter out senior roles from Indeed data based on job title
    """
    if df.empty:
        return df
    
    # Apply title-based filtering
    filtered_df = filter_by_job_title(df, 'Indeed')
    
    return filtered_df


def filter_by_job_title(df, source_name):
    """
    Filter jobs based on job title keywords - comprehensive filtering
    """
    if df.empty or 'job_title' not in df.columns:
        return df
    
    # Comprehensive list of keywords to exclude based on reference
    KEYWORDS_TO_EXCLUDE = [
        'Senior', 'Lead', 'Chief', 'Director', 'Executive', 'Principal', 'Architect',
        'Supervisor', 'VP', 'Vice President', 'Contract', 'Contractor', 'Part-time',
        'mid-level', 'Clearance', 'Specialist', 'Consultant', 'Worker', 'Technician',
        'Advisor', 'Administrative', 'Clerk', 'teacher', 'tutor', 'educator',
        'instructor', 'professor', 'faculty', 'representative', 'Shift', 'Laborer',
        'Rep', 'operator', 'handler', 'inspector', 'Head', 'Apprentice',
        'lister', 'officer', 'writer', 'advisor', 'Banker', 'training', 'instruct',
        'staff', 'Facilitator', 'Appraiser', 'Auditor', 'Nurse', 'Representative',
        'Radiologist', 'Dealer', 'Sales', 'Counselor', 'Broker', 'Archaeologist',
        'agent', 'Agency', 'Floral', 'Interior', 'nails', 'part-time', 'therapist',
        'contract', 'certified', 'inn', 'restaurant', 'Sr.', 'Sr', 'coordinator', 'Buyer',
        'Bartender', 'cleaner', 'Cook', 'Software Engineer III', 'Data Engineer III'
    ]
    
    # PM role keywords - these should be kept even if they contain excluded keywords
    # BUT only if they don't have senior prefixes
    PM_KEYWORDS = ['product manager', 'project manager', 'program manager']
    
    def should_keep_job(row):
        job_title = str(row.get('job_title', '')).lower()
        
        # Check if it's a PM role (exception) BUT not senior
        for pm_keyword in PM_KEYWORDS:
            if pm_keyword in job_title:
                # Check if it has senior prefixes - if so, exclude it
                senior_prefixes = ['senior', 'sr.', 'sr ', 'lead', 'principal', 'director']
                has_senior_prefix = any(prefix in job_title for prefix in senior_prefixes)
                if not has_senior_prefix:
                    return True
                else:
                    return False  # It's a senior PM role, exclude it
        
        # Check if it contains any excluded keywords
        for keyword in KEYWORDS_TO_EXCLUDE:
            if keyword.lower() in job_title:
                return False
        
        return True
    
    # Apply filtering
    initial_count = len(df)
    mask = df.apply(should_keep_job, axis=1)
    filtered_df = df[mask].copy()
    
    print(f"🔍 {source_name} title filtering: {initial_count} → {len(filtered_df)} jobs (removed {initial_count - len(filtered_df)} excluded roles)")
    
    return filtered_df


def process_linkedin_jobs(df):
    """
    Processes LinkedIn job DataFrame to extract and rename required columns,
    fill missing apply_link with url, and add work_authorization and work_mode columns using regex.
    """
    if df.empty:
        return df
    
    # Create a copy to avoid modifying original
    df = df.copy()
    
    # 1. Extract keyword from discovery_input
    if 'discovery_input' in df.columns:
        df['keyword'] = df['discovery_input'].apply(
            lambda x: json.loads(x).get('keyword') if pd.notna(x) and isinstance(x, str) else None
        )
    else:
        df['keyword'] = None

    # 2. Fill missing apply_link with url
    if 'apply_link' in df.columns and 'url' in df.columns:
        df['apply_link'] = df['apply_link'].replace('', np.nan)
        df['apply_link'] = df['apply_link'].fillna(df['url'])
    elif 'url' in df.columns:
        df['apply_link'] = df['url']

    # 3. Add work_authorization column using regex
    def extract_work_auth(desc):
        if pd.isna(desc):
            return "not sure"
        desc_lower = desc.lower()
        # Negative patterns
        if re.search(r'\bno h1b\b|\bnot eligible for h1b\b|\bno work authorization\b|\bnot authorized\b', desc_lower):
            return "no"
        # Positive patterns
        if re.search(r'\bh1b\b|\bwork authorization\b|\bvisa sponsorship\b', desc_lower):
            return "yes"
        return "not sure"

    df['work_authorization'] = df['job_summary'].apply(extract_work_auth) if 'job_summary' in df.columns else "not sure"

    # 4. Add work_mode column using regex
    def extract_work_mode(desc):
        if pd.isna(desc):
            return "not sure"
        desc_lower = desc.lower()
        if re.search(r'\bremote\b', desc_lower):
            return "Remote"
        elif re.search(r'\bhybrid\b', desc_lower):
            return "Hybrid"
        elif re.search(r'\bonsite\b|\bon-site\b', desc_lower):
            return "Onsite"
        return "not sure"

    df['work_mode'] = df['job_summary'].apply(extract_work_mode) if 'job_summary' in df.columns else "not sure"

    # 5. Extract years of experience from job description
    df['years_of_experience'] = df['job_summary'].apply(extract_years_of_experience) if 'job_summary' in df.columns else ""

    # 6. Filter out senior roles
    df = filter_senior_roles_linkedin(df)

    # 7. Remove duplicates using job_posting_id
    df = remove_linkedin_duplicates(df)

    # 8. Filter jobs older than 1 day
    # df = filter_linkedin_by_date(df, max_days_old=1)

    # 9. Keep ALL columns and add source column
    # Instead of filtering columns, we'll keep everything and just add source
    processed_df = df.copy()
    
    # Add source column to identify LinkedIn data
    processed_df['source'] = 'LinkedIn'
    
    # 10. Move apply_link to the last column if it exists
    processed_df = move_apply_link_to_last_column(processed_df)

    return processed_df


def process_indeed_jobs(df):
    """
    Processes Indeed job DataFrame to extract and rename required columns,
    fill missing apply_link with url, and add work_authorization and work_mode columns using regex.
    """
    if df.empty:
        return df
    
    # Create a copy to avoid modifying original
    df = df.copy()
    
    # 1. Extract keyword from discovery_input
    if 'discovery_input' in df.columns:
        df['keyword'] = df['discovery_input'].apply(
            lambda x: json.loads(x).get('keyword_search') if pd.notna(x) and isinstance(x, str) else None
        )
    else:
        df['keyword'] = None

    # 2. Fill missing apply_link with url
    if 'apply_link' in df.columns and 'url' in df.columns:
        df['apply_link'] = df['apply_link'].replace('', np.nan)
        df['apply_link'] = df['apply_link'].fillna(df['url'])
    elif 'url' in df.columns:
        df['apply_link'] = df['url']

    # 3. Add work_authorization column using regex on description_text
    def extract_work_auth(desc):
        if pd.isna(desc):
            return "not sure"
        desc_lower = desc.lower()
        if re.search(r'\bno h1b\b|\bnot eligible for h1b\b|\bno work authorization\b|\bnot authorized\b', desc_lower):
            return "no"
        if re.search(r'\bh1b\b|\bwork authorization\b|\bvisa sponsorship\b', desc_lower):
            return "yes"
        return "not sure"

    df['work_authorization'] = df['description_text'].apply(extract_work_auth) if 'description_text' in df.columns else "not sure"

    # 4. Add work_mode column using job_location (priority) and fallback to description_text
    def extract_work_mode(row):
        # Priority: job_location
        loc = row.get('job_location', None)
        desc = row.get('description_text', None)
        
        # Check job_location first
        if pd.notna(loc):
            loc_lower = str(loc).lower()
            if re.search(r'\bremote\b', loc_lower):
                return "Remote"
            elif re.search(r'\bhybrid\b', loc_lower):
                return "Hybrid"
            elif re.search(r'\bonsite\b|\bon-site\b', loc_lower):
                return "Onsite"
        
        # Fallback to description_text
        if pd.notna(desc):
            desc_lower = str(desc).lower()
            if re.search(r'\bremote\b', desc_lower):
                return "Remote"
            elif re.search(r'\bhybrid\b', desc_lower):
                return "Hybrid"
            elif re.search(r'\bonsite\b|\bon-site\b', desc_lower):
                return "Onsite"
        
        return "not sure"

    df['work_mode'] = df.apply(extract_work_mode, axis=1)

    # 5. Extract years of experience from job description and title
    def extract_yoe_indeed(row):
        title = str(row.get('job_title', ''))
        desc = str(row.get('description_text', ''))
        combined_text = f"{title} {desc}"
        return extract_years_of_experience(combined_text)
    
    df['years_of_experience'] = df.apply(extract_yoe_indeed, axis=1)

    # 6. Filter out senior roles
    df = filter_senior_roles_indeed(df)

    # 7. Keep ALL columns and add source column
    # Instead of filtering columns, we'll keep everything and just add source
    processed_df = df.copy()
    
    # Add source column to identify Indeed data
    processed_df['source'] = 'Indeed'

    # Move apply_link to the last column if it exists
    processed_df = move_apply_link_to_last_column(processed_df)

    return processed_df


def clean_years_of_experience(df):
    """
    Clean up years of experience data to remove invalid entries
    Only keep 0-3 years for entry-level focus
    """
    if df.empty or 'years_of_experience' not in df.columns:
        return df
    
    def clean_yoe(yoe_str):
        if pd.isna(yoe_str) or yoe_str == "":
            return ""
        
        yoe_str = str(yoe_str).strip()
        
        # Comprehensive date and invalid pattern detection
        invalid_patterns = [
            r'\b(?:jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\b',
            r'\d{1,2}[-/]\w{3}',  # 08-Dec, 5-Jul pattern
            r'\d{1,2}[-/]\d{1,2}',  # date patterns like 12/25
            r'\d{4}[-/]\d{2}[-/]\d{2}',  # ISO date patterns
            r'\d{1,2}\s*(?:jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)',  # 5 Jul
            r'(?:jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\s*\d{1,2}',  # Jul 5
            r'\b(?:19|20)\d{2}\b',  # Years like 1995, 2023, 2024
            r'\b(?:monday|tuesday|wednesday|thursday|friday|saturday|sunday)\b',  # Days
            r'[a-zA-Z]',  # Any letters (except in the patterns above)
            r'[-–—].*[-–—]',  # Multiple dashes
            r'^\d{2,}$'  # Numbers with more than 1 digit (like 90, 18, etc.)
        ]
        
        # Check for invalid patterns
        for pattern in invalid_patterns:
            if re.search(pattern, yoe_str.lower()):
                return ""
        
        # Only allow single digits 0-3 (entry-level focus)
        if re.match(r'^[0-3]$', yoe_str):
            return yoe_str
        
        # Remove everything else
        return ""
    
    # Show sample before cleaning for debugging
    sample_before = df['years_of_experience'].value_counts().head(15)
    print(f"🔍 YOE before cleaning: {dict(sample_before)}")
    
    # Apply cleaning
    initial_valid = len(df[df['years_of_experience'] != ""])
    df['years_of_experience'] = df['years_of_experience'].apply(clean_yoe)
    final_valid = len(df[df['years_of_experience'] != ""])
    
    # Show sample after cleaning
    sample_after = df['years_of_experience'].value_counts().head(10)
    print(f"🔍 YOE after cleaning: {dict(sample_after)}")
    
    print(f"🧹 Years of experience cleaned: {initial_valid} → {final_valid} valid entries")
    
    return df


def combine_datasets(linkedin_df, indeed_df):
    """
    Combine LinkedIn and Indeed datasets into a unified format
    """
    combined_dfs = []
    
    if not linkedin_df.empty:
        combined_dfs.append(linkedin_df)
        print(f"📊 LinkedIn processed: {len(linkedin_df)} rows")
    
    if not indeed_df.empty:
        combined_dfs.append(indeed_df)
        print(f"📊 Indeed processed: {len(indeed_df)} rows")
    
    if combined_dfs:
        combined_df = pd.concat(combined_dfs, ignore_index=True, sort=False)
        
        # Clean years of experience data
        combined_df = clean_years_of_experience(combined_df)
        
        # Ensure apply_link is the last column
        combined_df = move_apply_link_to_last_column(combined_df)
        
        print(f"🔗 Combined dataset: {len(combined_df)} total rows")
        return combined_df
    else:
        print("⚠️ No data to combine")
        return pd.DataFrame()


def save_combined_data(df, output_folder='combined_data'):
    """
    Save the combined dataset to CSV files
    """
    if df.empty:
        print("⚠️ No data to save")
        return
    
    # Create output folder if it doesn't exist
    Path(output_folder).mkdir(exist_ok=True)
    
    # Generate timestamp for filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Save complete dataset
    complete_filename = f"combined_jobs.csv"
    complete_path = os.path.join(output_folder, complete_filename)
    df.to_csv(complete_path, index=False)
    print(f"💾 Complete dataset saved: {complete_path}")
    
    # Save by source
    if 'source' in df.columns:
        for source in df['source'].unique():
            source_df = df[df['source'] == source]
            source_filename = f"{source.lower()}_jobs_{timestamp}.csv"
            source_path = os.path.join(output_folder, source_filename)
            source_df.to_csv(source_path, index=False)
            print(f"💾 {source} dataset saved: {source_path} ({len(source_df)} rows)")
    
    # Save summary statistics
    summary_filename = f"data_summary_{timestamp}.txt"
    summary_path = os.path.join(output_folder, summary_filename)
    
    with open(summary_path, 'w') as f:
        f.write("Job Data Processing Summary\n")
        f.write("=" * 30 + "\n\n")
        f.write(f"Processing Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total Records: {len(df)}\n\n")
        
        if 'source' in df.columns:
            f.write("Records by Source:\n")
            source_counts = df['source'].value_counts()
            for source, count in source_counts.items():
                f.write(f"  {source}: {count}\n")
            f.write("\n")
        
        if 'keyword' in df.columns:
            f.write("Top Keywords:\n")
            keyword_counts = df['keyword'].value_counts().head(10)
            for keyword, count in keyword_counts.items():
                f.write(f"  {keyword}: {count}\n")
            f.write("\n")
        
        if 'work_mode' in df.columns:
            f.write("Work Mode Distribution:\n")
            work_mode_counts = df['work_mode'].value_counts()
            for mode, count in work_mode_counts.items():
                f.write(f"  {mode}: {count}\n")
            f.write("\n")
        
        if 'work_authorization' in df.columns:
            f.write("Work Authorization Distribution:\n")
            auth_counts = df['work_authorization'].value_counts()
            for auth, count in auth_counts.items():
                f.write(f"  {auth}: {count}\n")
            f.write("\n")
        
        if 'years_of_experience' in df.columns:
            f.write("Years of Experience Distribution:\n")
            yoe_counts = df['years_of_experience'].value_counts()
            # Show top 10 most common YOE values
            for yoe, count in yoe_counts.head(10).items():
                display_yoe = yoe if yoe else "Not specified"
                f.write(f"  {display_yoe}: {count}\n")
            f.write("\n")
        
        if 'job_seniority' in df.columns:
            f.write("Job Seniority Distribution (LinkedIn only):\n")
            seniority_counts = df[df['source'] == 'LinkedIn']['job_seniority'].value_counts()
            for seniority, count in seniority_counts.items():
                f.write(f"  {seniority}: {count}\n")
    
    print(f"📊 Summary saved: {summary_path}")


def move_apply_link_to_last_column(df):
    """
    Move apply_link column to the last position in the DataFrame
    """
    if df.empty or 'apply_link' not in df.columns:
        return df
    
    # Get all columns except apply_link
    columns_without_apply = [col for col in df.columns if col != 'apply_link']
    
    # Reorder columns to put apply_link at the end
    reordered_columns = columns_without_apply + ['apply_link']
    
    return df[reordered_columns]


def remove_linkedin_duplicates(df):
    """
    Remove duplicate LinkedIn jobs using job_posting_id
    Keep the first occurrence of each job_posting_id
    """
    if df.empty:
        return df
    
    initial_count = len(df)
    
    # Check if job_posting_id column exists
    if 'job_posting_id' not in df.columns:
        print("⚠️ No job_posting_id column found for LinkedIn duplicate removal")
        return df
    
    # Remove duplicates based on job_posting_id, keeping the first occurrence
    df_no_duplicates = df.drop_duplicates(subset=['job_posting_id'], keep='first')
    
    removed_count = initial_count - len(df_no_duplicates)
    print(f"🔍 LinkedIn duplicate removal: {initial_count} → {len(df_no_duplicates)} jobs (removed {removed_count} duplicates)")
    
    return df_no_duplicates


def filter_linkedin_by_date(df, max_days_old=1):
    """
    Filter LinkedIn jobs to keep only those posted within the last N days
    Default is 1 day (jobs posted today or yesterday)
    """
    if df.empty:
        return df
    
    initial_count = len(df)
    
    # Check if date column exists
    date_columns = ['job_posted_date', 'date', 'timestamp']
    date_col = None
    
    for col in date_columns:
        if col in df.columns:
            date_col = col
            break
    
    if date_col is None:
        print("⚠️ No date column found for LinkedIn date filtering")
        return df
    
    # Calculate the cutoff date (N days ago)
    cutoff_date = datetime.now() - timedelta(days=max_days_old)
    print(f"📅 Filtering LinkedIn jobs: keeping jobs posted after {cutoff_date.strftime('%Y-%m-%d')}")
    
    def parse_date(date_str):
        """Parse various date formats"""
        if pd.isna(date_str):
            return None
        
        date_str = str(date_str).strip()
        
        # Handle ISO format with Z timezone indicator (most common in APIs)
        if date_str.endswith('Z'):
            # Remove Z and try to parse as UTC
            try:
                # Handle both with and without milliseconds
                if '.' in date_str:
                    # With milliseconds: 2025-07-02T17:02:20.573Z
                    return datetime.strptime(date_str[:-1], '%Y-%m-%dT%H:%M:%S.%f')
                else:
                    # Without milliseconds: 2025-07-02T17:02:20Z
                    return datetime.strptime(date_str[:-1], '%Y-%m-%dT%H:%M:%S')
            except ValueError:
                pass
        
        # Try different date formats
        date_formats = [
            '%Y-%m-%d',
            '%Y-%m-%d %H:%M:%S',
            '%m/%d/%Y',
            '%d/%m/%Y',
            '%Y-%m-%dT%H:%M:%S',
            '%Y-%m-%dT%H:%M:%S.%f'
        ]
        
        for fmt in date_formats:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue
        
        # Try to parse relative dates like "1 day ago", "2 days ago"
        relative_patterns = [
            r'(\d+)\s+day[s]?\s+ago',
            r'(\d+)\s+hour[s]?\s+ago',
            r'(\d+)\s+minute[s]?\s+ago'
        ]
        
        for pattern in relative_patterns:
            match = re.search(pattern, date_str.lower())
            if match:
                days_ago = int(match.group(1))
                if 'day' in pattern:
                    return datetime.now() - timedelta(days=days_ago)
                elif 'hour' in pattern:
                    return datetime.now() - timedelta(hours=days_ago)
                elif 'minute' in pattern:
                    return datetime.now() - timedelta(minutes=days_ago)
        
        return None
    
    # Parse dates and filter
    df['parsed_date'] = df[date_col].apply(parse_date)
    
    # Debug: Show some sample dates and their parsing results
    sample_dates = df[[date_col, 'parsed_date']].head(10)
    print(f"🔍 Sample date parsing:")
    for idx, row in sample_dates.iterrows():
        original = row[date_col]
        parsed = row['parsed_date']
        print(f"  {original} → {parsed}")
    
    # Count how many dates were successfully parsed
    valid_dates = df['parsed_date'].notna().sum()
    total_dates = len(df)
    print(f"📅 Date parsing: {valid_dates}/{total_dates} dates successfully parsed")
    
    # Filter jobs posted after cutoff date
    df_filtered = df[df['parsed_date'] >= cutoff_date].copy()
    
    # Debug: Show some jobs that were kept vs removed
    kept_jobs = df[df['parsed_date'] >= cutoff_date][['parsed_date']].head(5)
    removed_jobs = df[df['parsed_date'] < cutoff_date][['parsed_date']].head(5)
    
    print(f"📅 Jobs kept (after {cutoff_date}):")
    for idx, row in kept_jobs.iterrows():
        print(f"  {row['parsed_date']}")
    
    print(f"📅 Jobs removed (before {cutoff_date}):")
    for idx, row in removed_jobs.iterrows():
        print(f"  {row['parsed_date']}")
    
    # Remove the temporary parsed_date column
    df_filtered = df_filtered.drop(columns=['parsed_date'])
    
    removed_count = initial_count - len(df_filtered)
    print(f"📅 LinkedIn date filtering: {initial_count} → {len(df_filtered)} jobs (removed {removed_count} old jobs)")
    
    return df_filtered


def main():
    """
    Main preprocessing function
    """
    print("🚀 Starting job data preprocessing...")
    print("=" * 50)
    
    # Load raw data
    print("\n📂 Loading raw data...")
    linkedin_raw = load_linkedin_data()
    indeed_raw = load_indeed_data()
    
    # Process data
    print("\n⚙️ Processing data...")
    linkedin_processed = process_linkedin_jobs(linkedin_raw)
    indeed_processed = process_indeed_jobs(indeed_raw)
    
    # Show some debugging info
    if not linkedin_processed.empty:
        if 'job_seniority' in linkedin_processed.columns:
            seniority_sample = linkedin_processed['job_seniority'].value_counts()
            print(f"\n🔍 LinkedIn final seniority distribution: {dict(seniority_sample)}")
        
        if 'years_of_experience' in linkedin_processed.columns:
            sample_yoe = linkedin_processed['years_of_experience'].value_counts().head(10)
            print(f"🔍 LinkedIn YOE sample: {dict(sample_yoe)}")
    
    if not indeed_processed.empty and 'years_of_experience' in indeed_processed.columns:
        sample_yoe = indeed_processed['years_of_experience'].value_counts().head(10)
        print(f"🔍 Indeed YOE sample: {dict(sample_yoe)}")
    
    # Combine datasets
    print("\n🔗 Combining datasets...")
    combined_data = combine_datasets(linkedin_processed, indeed_processed)
    
    # Save results
    print("\n💾 Saving results...")
    save_combined_data(combined_data)
    
    print("\n✅ Preprocessing completed successfully!")
    print(f"📊 Final dataset contains {len(combined_data)} job records")
    
    # Show final YOE distribution
    if not combined_data.empty and 'years_of_experience' in combined_data.columns:
        final_yoe = combined_data['years_of_experience'].value_counts().head(10)
        print(f"📈 Final YOE distribution: {dict(final_yoe)}")


if __name__ == "__main__":
    main()