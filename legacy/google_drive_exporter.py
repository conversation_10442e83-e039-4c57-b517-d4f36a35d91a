"""
Google Drive Exporter for Job Sheets.

Simple exporter that uploads job_sheets_final.xlsx to Google Drive
with the same naming format as the lambda function.
"""
import os
from datetime import datetime
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload
from google.oauth2 import service_account
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def export_job_sheets_to_drive():
    """
    Export job_sheets_final.xlsx to Google Drive with proper naming format
    
    Returns:
        str: File ID of uploaded file, None if failed
    """
    try:
        # Configuration (same as lambda function)
        service_account_file = '../lambda_function/service-account-key.json'
        folder_id = '1dKVjmvDgelKZxi9BkYIbxU3p3Mg_dXFV'
        excel_file_path = 'combined_data/job_sheets_final.xlsx'
        
        # Google API scopes
        scopes = [
            'https://www.googleapis.com/auth/drive',
            'https://www.googleapis.com/auth/spreadsheets'
        ]
        
        # Check if Excel file exists
        if not os.path.exists(excel_file_path):
            logger.error(f"Excel file not found: {excel_file_path}")
            return None
        
        # Check if service account file exists
        if not os.path.exists(service_account_file):
            logger.error(f"Service account file not found: {service_account_file}")
            return None
        
        # Initialize Google Drive service
        credentials = service_account.Credentials.from_service_account_file(
            service_account_file, scopes=scopes
        )
        drive_service = build('drive', 'v3', credentials=credentials)
        
        # Generate filename with today's date (same format as lambda)
        today = datetime.now()
        formatted_date = today.strftime("%m%d")
        filename = f"[Daily Jobs] {formatted_date} Daily Jobs for NG.xlsx"
        
        # File metadata
        file_metadata = {
            'name': filename,
            'parents': [folder_id]
        }
        
        # Create media upload
        media = MediaFileUpload(
            excel_file_path,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Upload file
        logger.info(f"Uploading file: {filename}")
        file = drive_service.files().create(
            body=file_metadata,
            media_body=media,
            fields='id,name,webViewLink'
        ).execute()
        
        # Set file permissions (anyone with link can view)
        drive_service.permissions().create(
            fileId=file['id'],
            body={
                'type': 'anyone',
                'role': 'reader'
            }
        ).execute()
        
        logger.info(f"Successfully uploaded: {filename}")
        logger.info(f"File ID: {file['id']}")
        logger.info(f"View link: {file.get('webViewLink', 'N/A')}")
        
        return file['id']
        
    except Exception as e:
        logger.error(f"Error uploading to Google Drive: {str(e)}")
        return None

if __name__ == "__main__":
    # Run the export
    file_id = export_job_sheets_to_drive()
    
    if file_id:
        print(f"✅ Successfully exported job sheets with file ID: {file_id}")
    else:
        print("❌ Failed to export job sheets")