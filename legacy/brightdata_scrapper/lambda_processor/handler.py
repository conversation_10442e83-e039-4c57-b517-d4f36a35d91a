"""
Lambda 2: Fetches S3 data, preprocesses it, and saves locally.
"""

import json

# Fix import to work both as package and standalone
try:
    from .s3_processor import S3Processor
    from ..shared.logger import logger
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
    from lambda_processor.s3_processor import S3Processor
    from shared.logger import logger


def lambda_handler(event, context):
    """
    Entrypoint for AWS Lambda. Fetches job data from S3, preprocesses, and saves locally.
    
    Args:
        event: Lambda event (can contain aws_credentials, bucket_name, save_options)
        context: Lambda context
        
    Returns:
        Dict: Processing results and status
    """
    try:
        logger.info("🚀 Starting S3 data processing Lambda")
        
        # Parse event parameters
        aws_access_key = event.get('aws_access_key')
        aws_secret_key = event.get('aws_secret_key')
        bucket_name = event.get('bucket_name', 'bright-data-api')
        save_individual = event.get('save_individual', True)
        save_combined = event.get('save_combined', True)
        download_locally = event.get('download_locally', False)
        local_dir = event.get('local_dir', 's3_data')
        
        logger.info(f"📋 Parameters: bucket={bucket_name}, save_individual={save_individual}, save_combined={save_combined}")
        
        # Create S3 processor instance
        processor = S3Processor(aws_access_key, aws_secret_key, bucket_name)
        
        # Get data summary first
        summary = processor.get_data_summary()
        logger.info(f"📊 Data Summary: {summary}")
        
        if summary['total_files'] == 0:
            logger.warning("⚠️ No files found for today. Make sure scraping has completed.")
            return {
                "status": "warning",
                "message": "No files found for today",
                "summary": summary
            }
        
        # Process and combine data
        logger.info("💡 Files found! Processing data...")
        combined_df = processor.process_and_combine_data(save_individual, save_combined)
        
        if not combined_df.empty:
            logger.info(f"🎉 SUCCESS! Processed {len(combined_df)} total job records")
            
            # Show sample data
            sample_data = combined_df.head().to_dict('records') if len(combined_df) > 0 else []
            
            response = {
                "status": "success",
                "total_records": len(combined_df),
                "summary": summary,
                "sample_data": sample_data,
                "columns": list(combined_df.columns) if not combined_df.empty else []
            }
            
            # If requested, also download files locally
            if download_locally:
                logger.info("📥 Downloading files locally...")
                local_df = processor.download_and_process_locally(local_dir)
                response["local_download"] = {
                    "local_dir": local_dir,
                    "local_records": len(local_df) if not local_df.empty else 0
                }
            
            return response
        else:
            logger.warning("⚠️ No data was processed")
            return {
                "status": "warning",
                "message": "No data was processed",
                "summary": summary
            }
        
    except Exception as e:
        logger.error(f"❌ Error in S3 processing Lambda: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "bucket_name": event.get('bucket_name', 'unknown')
        } 