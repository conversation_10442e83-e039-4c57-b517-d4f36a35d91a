"""
Handles S3 data fetching and initial processing.
"""

import pandas as pd
import json
import numpy as np
import boto3
import os
import datetime
from typing import List, Dict, Any, Optional
from io import StringIO
import warnings

# Fix import to work both as package and standalone
try:
    from .data_preprocessor import DataPreprocessor, extract_linkedin_keyword, extract_indeed_keyword
    from ..shared.logger import logger
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
    from lambda_processor.data_preprocessor import DataPreprocessor, extract_linkedin_keyword, extract_indeed_keyword
    from shared.logger import logger

# Suppress pandas warnings
warnings.filterwarnings('ignore', category=FutureWarning)


class S3Processor:
    """
    Process job data from S3 bucket for today's date.
    Loads, processes, and combines Indeed and LinkedIn data.
    """
    
    def __init__(self, aws_access_key: str = None, aws_secret_key: str = None, bucket_name: str = "bright-data-api"):
        """
        Initialize S3 data processor.
        
        Args:
            aws_access_key: AWS access key (if None, uses default credentials)
            aws_secret_key: AWS secret key (if None, uses default credentials)
            bucket_name: S3 bucket name
        """
        self.bucket_name = bucket_name
        
        # Initialize S3 client
        if aws_access_key and aws_secret_key:
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=aws_access_key,
                aws_secret_access_key=aws_secret_key
            )
        else:
            # Use default credentials (from environment, IAM role, etc.)
            self.s3_client = boto3.client('s3')
        
        # Get today's date for S3 path
        self.today = datetime.datetime.now().strftime("%Y-%m-%d")
        # self.today='2025-07-18'
        logger.info(f"📅 Processing data for date: {self.today}")
        logger.info(f"🪣 S3 Bucket: {bucket_name}")
        
        # Initialize data preprocessor
        self.preprocessor = DataPreprocessor()
    
    def process_indeed_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Process Indeed DataFrame using the actual S3 data structure.
        Note: S3 data uses LinkedIn column names for both platforms.
        
        Args:
            df: Raw Indeed DataFrame
            
        Returns:
            pd.DataFrame: Processed Indeed DataFrame
        """
        if df.empty:
            return df
        
        processed_df = df.copy()
        
        # 1. Extract keyword using LinkedIn-specific extractor (S3 data uses LinkedIn format for both platforms)
        if 'discovery_input' in processed_df.columns:
            processed_df['keyword'] = processed_df['discovery_input'].apply(extract_linkedin_keyword)

        # 2. If 'apply_link' is empty, use the 'url'
        if 'apply_link' in processed_df.columns and 'url' in processed_df.columns:
            processed_df['apply_link'] = processed_df['apply_link'].replace('', np.nan)
            processed_df['apply_link'] = processed_df['apply_link'].fillna(processed_df['url'])

        # 3. Select and rename the columns - ACTUAL S3 DATA MAPPING (LinkedIn structure for both platforms)
        final_columns = {
            'keyword': 'keyword',
            'company_name': 'company_name',
            'job_posted_date': 'date_posted',  # S3 uses 'job_posted_date'
            'job_title': 'job_title',
            'job_summary': 'description',  # S3 uses 'job_summary' for both platforms
            'job_description_formatted': 'benefits',  # Using formatted description as benefits
            'job_function': 'qualification',  # Using job_function as qualification
            'job_employment_type': 'job_type',  # S3 uses 'job_employment_type'
            'job_location': 'location',  # S3 uses 'job_location'
            'job_base_pay_range': 'salary',  # S3 uses 'job_base_pay_range'
            'country_code': 'region',  # Using country_code as region
            'apply_link': 'apply_link'
        }

        # Select the original columns that are available in the dataframe
        columns_to_select = [col for col in final_columns.keys() if col in processed_df.columns]
        
        # Create the processed dataframe from the available columns
        processed_df = processed_df[columns_to_select].rename(columns=final_columns)
        
        # Reorder columns to match expected output: keyword, company_name, date_posted, job_title, description, benefits, qualification, job_type, location, salary, region, apply_link
        desired_order = ['keyword', 'company_name', 'date_posted', 'job_title', 'description', 'benefits', 'qualification', 'job_type', 'location', 'salary', 'region', 'apply_link']
        available_ordered_columns = [col for col in desired_order if col in processed_df.columns]
        processed_df = processed_df[available_ordered_columns]

        logger.info(f"   📊 Indeed columns found: {columns_to_select}")
        logger.info(f"   📋 Indeed processed: {len(processed_df)} rows with {len(processed_df.columns)} columns")
        logger.info(f"   📋 Column order: {list(processed_df.columns)}")

        return processed_df

    def process_linkedin_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Process LinkedIn DataFrame using the actual S3 data structure.
        
        Args:
            df: Raw LinkedIn DataFrame
            
        Returns:
            pd.DataFrame: Processed LinkedIn DataFrame
        """
        if df.empty:
            return df
        
        processed_df = df.copy()
        
        # 1. Extract keyword using LinkedIn-specific extractor
        if 'discovery_input' in processed_df.columns:
            processed_df['keyword'] = processed_df['discovery_input'].apply(extract_linkedin_keyword)

        # 2. If 'apply_link' is empty, use the 'url'
        if 'apply_link' in processed_df.columns and 'url' in processed_df.columns:
            processed_df['apply_link'] = processed_df['apply_link'].replace('', np.nan)
            processed_df['apply_link'] = processed_df['apply_link'].fillna(processed_df['url'])

        # 3. Select and rename the columns - ACTUAL S3 DATA MAPPING
        final_columns = {
            'keyword': 'keyword',
            'company_name': 'company_name',
            'job_posted_date': 'date_posted',  # S3 uses 'job_posted_date'
            'job_title': 'job_title',
            'job_summary': 'description',  # S3 uses 'job_summary'
            'job_description_formatted': 'benefits',  # Using formatted description as benefits
            'job_function': 'qualification',  # Using job_function as qualification
            'job_employment_type': 'job_type',  # S3 uses 'job_employment_type'
            'job_location': 'location',  # S3 uses 'job_location'
            'job_base_pay_range': 'salary',  # S3 uses 'job_base_pay_range'
            'country_code': 'region',  # Using country_code as region
            'apply_link': 'apply_link'
        }

        # Select the original columns that are available in the dataframe
        columns_to_select = [col for col in final_columns.keys() if col in processed_df.columns]
        
        # Create the processed dataframe from the available columns
        processed_df = processed_df[columns_to_select].rename(columns=final_columns)
        
        # Reorder columns to match expected output: keyword, company_name, date_posted, job_title, description, benefits, qualification, job_type, location, salary, region, apply_link
        desired_order = ['keyword', 'company_name', 'date_posted', 'job_title', 'description', 'benefits', 'qualification', 'job_type', 'location', 'salary', 'region', 'apply_link']
        available_ordered_columns = [col for col in desired_order if col in processed_df.columns]
        processed_df = processed_df[available_ordered_columns]

        logger.info(f"   📊 LinkedIn columns found: {columns_to_select}")
        logger.info(f"   📋 LinkedIn processed: {len(processed_df)} rows with {len(processed_df.columns)} columns")
        logger.info(f"   📋 Column order: {list(processed_df.columns)}")

        return processed_df

    def list_s3_files(self, platform: str) -> List[str]:
        """
        List all CSV files in S3 for today's date and platform.
        
        Args:
            platform: 'indeed' or 'linkedin'
            
        Returns:
            List[str]: List of S3 object keys
        """
        prefix = f"{self.today}/{platform}/"
        
        try:
            response = self.s3_client.list_objects_v2(
                Bucket=self.bucket_name,
                Prefix=prefix
            )
            
            if 'Contents' not in response:
                logger.warning(f"⚠️  No files found for {platform} on {self.today}")
                return []
            
            csv_files = [
                obj['Key'] for obj in response['Contents'] 
                if obj['Key'].endswith('.csv')
            ]
            
            logger.info(f"📂 Found {len(csv_files)} CSV files for {platform}")
            return csv_files
            
        except Exception as e:
            logger.error(f"❌ Error listing S3 files for {platform}: {str(e)}")
            return []

    def load_csv_from_s3(self, s3_key: str) -> pd.DataFrame:
        """
        Load a single CSV file from S3.
        
        Args:
            s3_key: S3 object key
            
        Returns:
            pd.DataFrame: Loaded DataFrame
        """
        try:
            response = self.s3_client.get_object(Bucket=self.bucket_name, Key=s3_key)
            csv_content = response['Body'].read().decode('utf-8')
            df = pd.read_csv(StringIO(csv_content))
            
            logger.info(f"   ✅ {s3_key}: {len(df)} rows loaded")
            return df
            
        except Exception as e:
            logger.error(f"   ❌ Error loading {s3_key}: {str(e)}")
            return pd.DataFrame()

    def load_platform_data_from_s3(self, platform: str) -> pd.DataFrame:
        """
        Load all CSV files for a platform from S3 and concatenate them.
        
        Args:
            platform: 'indeed' or 'linkedin'
            
        Returns:
            pd.DataFrame: Concatenated DataFrame
        """
        logger.info(f"📂 Loading {platform.upper()} data from S3...")
        
        csv_files = self.list_s3_files(platform)
        
        if not csv_files:
            return pd.DataFrame()
        
        all_dfs = []
        for s3_key in csv_files:
            df = self.load_csv_from_s3(s3_key)
            if not df.empty:
                all_dfs.append(df)
        
        if all_dfs:
            combined_df = pd.concat(all_dfs, ignore_index=True)
            logger.info(f"📊 {platform.upper()} total: {len(combined_df)} rows from {len(all_dfs)} files")
            return combined_df
        else:
            logger.warning(f"⚠️  No valid data found for {platform}")
            return pd.DataFrame()

    def process_and_combine_data(self, save_individual: bool = True, save_combined: bool = True) -> pd.DataFrame:
        """
        Complete workflow: Load, process, and combine data from both platforms.
        
        Args:
            save_individual: Save individual processed files
            save_combined: Save combined file
            
        Returns:
            pd.DataFrame: Final combined DataFrame
        """
        logger.info("🚀 STARTING COMPLETE DATA PROCESSING WORKFLOW")
        logger.info("=" * 60)
        
        # Load raw data from S3
        indeed_raw = self.load_platform_data_from_s3('indeed')
        linkedin_raw = self.load_platform_data_from_s3('linkedin')
        
        logger.info("\n" + "-" * 40)
        
        # Process data using the new preprocessor
        logger.info("\n⚙️  PROCESSING DATA...")
        
        indeed_processed = pd.DataFrame()
        linkedin_processed = pd.DataFrame()
        
        if not indeed_raw.empty:
            indeed_processed = self.process_indeed_data(indeed_raw)
            logger.info(f"✅ Indeed processed: {len(indeed_processed)} rows")
            
            if save_individual:
                indeed_filename = f"indeed_processed_{self.today.replace('-', '')}.csv"
                indeed_processed.to_csv(indeed_filename, index=False)
                logger.info(f"💾 Indeed data saved to: {indeed_filename}")
        else:
            logger.warning("⚠️  No Indeed data to process")
        
        if not linkedin_raw.empty:
            linkedin_processed = self.process_linkedin_data(linkedin_raw)
            logger.info(f"✅ LinkedIn processed: {len(linkedin_processed)} rows")
            
            if save_individual:
                linkedin_filename = f"linkedin_processed_{self.today.replace('-', '')}.csv"
                linkedin_processed.to_csv(linkedin_filename, index=False)
                logger.info(f"💾 LinkedIn data saved to: {linkedin_filename}")
        else:
            logger.warning("⚠️  No LinkedIn data to process")
        
        logger.info("\n" + "-" * 40)
        
        # Combine data using the new preprocessor
        logger.info("\n🔗 COMBINING DATA...")
        
        # Use the new preprocessor to combine and process the data
        combined_df = self.preprocessor.preprocess_data(
            linkedin_processed, 
            indeed_processed, 
            save_output=save_combined,
            output_folder='combined_data'
        )
        
        if not combined_df.empty:
            logger.info(f"✅ Combined dataset: {len(combined_df)} rows")
            
            # Show keyword distribution
            if 'keyword' in combined_df.columns:
                keyword_counts = combined_df['keyword'].value_counts().head(5)
                logger.info(f"📋 Top keywords:")
                for keyword, count in keyword_counts.items():
                    logger.info(f"   • {keyword}: {count} jobs")
            
            # Show platform distribution by counting source files
            indeed_count = len(indeed_processed) if not indeed_processed.empty else 0
            linkedin_count = len(linkedin_processed) if not linkedin_processed.empty else 0
            logger.info(f"📊 Platform distribution:")
            if indeed_count > 0:
                logger.info(f"   • indeed: {indeed_count} jobs")
            if linkedin_count > 0:
                logger.info(f"   • linkedin: {linkedin_count} jobs")
            
            return combined_df
        else:
            logger.warning("⚠️  No data to combine")
            return pd.DataFrame()

    def get_data_summary(self) -> Dict[str, Any]:
        """
        Get summary of available data in S3 for today.
        
        Returns:
            Dict: Summary information
        """
        logger.info("📊 DATA SUMMARY FOR TODAY")
        logger.info("=" * 30)
        
        indeed_files = self.list_s3_files('indeed')
        linkedin_files = self.list_s3_files('linkedin')
        
        summary = {
            "date": self.today,
            "bucket": self.bucket_name,
            "indeed": {
                "files_count": len(indeed_files),
                "files": indeed_files
            },
            "linkedin": {
                "files_count": len(linkedin_files),
                "files": linkedin_files
            },
            "total_files": len(indeed_files) + len(linkedin_files)
        }
        
        logger.info(f"📅 Date: {self.today}")
        logger.info(f"🪣 Bucket: {self.bucket_name}")
        logger.info(f"🎯 Indeed files: {len(indeed_files)}")
        logger.info(f"🔗 LinkedIn files: {len(linkedin_files)}")
        logger.info(f"📁 Total files: {summary['total_files']}")
        
        return summary

    def download_and_process_locally(self, local_dir: str = "s3_data") -> pd.DataFrame:
        """
        Download S3 data locally and then process it.
        
        Args:
            local_dir: Local directory to save files
            
        Returns:
            pd.DataFrame: Combined processed data
        """
        logger.info(f"📥 DOWNLOADING S3 DATA TO LOCAL DIRECTORY: {local_dir}")
        logger.info("=" * 50)
        
        # Create local directories
        os.makedirs(f"{local_dir}/indeed", exist_ok=True)
        os.makedirs(f"{local_dir}/linkedin", exist_ok=True)
        
        # Download Indeed files
        indeed_files = self.list_s3_files('indeed')
        for s3_key in indeed_files:
            local_path = os.path.join(local_dir, s3_key)
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            
            try:
                self.s3_client.download_file(self.bucket_name, s3_key, local_path)
                logger.info(f"   ✅ Downloaded: {s3_key}")
            except Exception as e:
                logger.error(f"   ❌ Error downloading {s3_key}: {str(e)}")
        
        # Download LinkedIn files
        linkedin_files = self.list_s3_files('linkedin')
        for s3_key in linkedin_files:
            local_path = os.path.join(local_dir, s3_key)
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            
            try:
                self.s3_client.download_file(self.bucket_name, s3_key, local_path)
                logger.info(f"   ✅ Downloaded: {s3_key}")
            except Exception as e:
                logger.error(f"   ❌ Error downloading {s3_key}: {str(e)}")
        
        logger.info(f"\n📁 Files downloaded to: {local_dir}")
        
        # Now process the downloaded data
        return self.process_and_combine_data()


# Convenience functions
def process_todays_data(aws_access_key: str = None, aws_secret_key: str = None, 
                       bucket_name: str = "bright-data-api") -> pd.DataFrame:
    """
    One-command function to process today's job data from S3.
    
    Args:
        aws_access_key: AWS access key (optional)
        aws_secret_key: AWS secret key (optional)
        bucket_name: S3 bucket name
        
    Returns:
        pd.DataFrame: Combined processed data
    """
    processor = S3Processor(aws_access_key, aws_secret_key, bucket_name)
    return processor.process_and_combine_data()

def get_todays_data_summary(aws_access_key: str = None, aws_secret_key: str = None,
                           bucket_name: str = "bright-data-api") -> Dict[str, Any]:
    """
    Get summary of today's available data in S3.
    
    Args:
        aws_access_key: AWS access key (optional)
        aws_secret_key: AWS secret key (optional)
        bucket_name: S3 bucket name
        
    Returns:
        Dict: Summary information
    """
    processor = S3Processor(aws_access_key, aws_secret_key, bucket_name)
    return processor.get_data_summary() 