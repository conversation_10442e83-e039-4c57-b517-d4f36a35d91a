# BrightData Scrapper

This project provides a modular, production-ready pipeline for scraping job data from Bright Data and processing it using AWS Lambda functions.

## Architecture

- **Lambda 1: Scraper Trigger**
  - Triggers Bright Data API jobs for Indeed/LinkedIn using a config file.
  - Saves job metadata/results to S3 or logs.
  - Handles rate limiting and error management.

- **Lambda 2: S3 Fetch & Preprocess**
  - Fetches the latest job data from S3.
  - Preprocesses and unifies the data using custom logic.
  - Saves processed data locally (or to S3 in the future).
  - Filters for entry-level positions and cleans data.

## Folder Structure

```
brightdata_scrapper/
  ├── lambda_scraper/           # Lambda 1: Triggers Bright Data scraping jobs
  │    ├── handler.py           # Lambda entry point for scraping
  │    ├── brightdata_api.py    # Complete BrightData API integration
  │    └── config/
  │         └── scraping_inputs.json  # Job search configurations
  ├── lambda_processor/         # Lambda 2: Fetches S3 data, preprocesses, saves
  │    ├── handler.py           # Lambda entry point for processing
  │    ├── s3_processor.py     # S3 data fetching and processing
  │    ├── data_preprocessor.py # Data cleaning and unification
  │    └── utils/
  │         └── preprocessing_functions.py  # Deprecated (replaced by data_preprocessor.py)
  ├── shared/                   # Shared utilities
  │    └── logger.py           # Centralized logging
  ├── requirements.txt          # Python dependencies
  ├── README.md                # This file
  └── test_migration.py        # Test script to verify migration
```

## Features

### Lambda 1: Scraper
- **Multi-platform scraping**: Indeed and LinkedIn
- **Configurable inputs**: JSON-based job search configurations
- **Rate limiting**: Built-in delays between API calls
- **Error handling**: Comprehensive error management and logging
- **S3 integration**: Automatic data delivery to S3 bucket
- **Flexible triggering**: Support for specific platforms/categories

### Lambda 2: Processor
- **S3 data fetching**: Automatic retrieval of today's scraped data
- **Data preprocessing**: Cleaning, filtering, and unification
- **Entry-level filtering**: Focus on junior positions (0-3 years experience)
- **Multi-source combination**: Merges Indeed and LinkedIn data
- **Local storage**: Saves processed data locally
- **Comprehensive logging**: Detailed processing logs

## Setup

### Prerequisites
- AWS account with S3 access
- Bright Data API credentials
- Python 3.8+

### Installation
```bash
# Install dependencies
pip install -r requirements.txt

# Test the migration
python test_migration.py
```

### Configuration
1. **Bright Data API**: Update credentials in `lambda_scraper/brightdata_api.py`
2. **S3 Bucket**: Configure bucket name in `lambda_processor/s3_processor.py`
3. **Job Configurations**: Modify `lambda_scraper/config/scraping_inputs.json`

## Usage

### Local Testing

#### Test Migration
```bash
cd brightdata_scrapper
python test_migration.py
```

#### Manual Scraping
```python
from lambda_scraper.brightdata_api import BrightDataAPI

# Create scraper
scraper = BrightDataAPI("lambda_scraper/config/scraping_inputs.json")

# Scrape all platforms
results = scraper.scrape_all_platforms()

# Scrape specific platform
indeed_results = scraper.scrape_indeed_all_categories()
linkedin_results = scraper.scrape_linkedin_all_categories()
```

#### Manual Processing
```python
from lambda_processor.s3_processor import S3Processor

# Create processor
processor = S3Processor(bucket_name="your-bucket-name")

# Process today's data
combined_df = processor.process_and_combine_data()

# Get data summary
summary = processor.get_data_summary()
```

### Lambda Deployment

#### Lambda 1: Scraper
```python
# Event example
{
    "platform": "all",  # or "indeed", "linkedin"
    "categories": [],   # specific categories (optional)
    "delay_seconds": 5,
    "config_file": "config/scraping_inputs.json"
}
```

#### Lambda 2: Processor
```python
# Event example
{
    "aws_access_key": "your-access-key",
    "aws_secret_key": "your-secret-key", 
    "bucket_name": "bright-data-api",
    "save_individual": true,
    "save_combined": true,
    "download_locally": false
}
```

## Data Flow

1. **Scraping Phase** (Lambda 1):
   - Load job configurations from JSON
   - Trigger Bright Data API for each category
   - Save raw data to S3 with date-based organization
   - Log results and metadata

2. **Processing Phase** (Lambda 2):
   - Fetch today's data from S3
   - Clean and filter data (entry-level focus)
   - Combine Indeed and LinkedIn datasets
   - Save processed data locally
   - Generate summary statistics

## Data Processing Features

### Filtering
- **Senior role exclusion**: Removes Director, Lead, Senior positions
- **Entry-level focus**: Keeps 0-3 years experience positions
- **Keyword-based filtering**: Excludes non-relevant job titles

### Data Cleaning
- **Experience extraction**: Regex-based years of experience parsing
- **Work authorization detection**: H1B and visa sponsorship detection
- **Work mode classification**: Remote, Hybrid, Onsite detection
- **Column standardization**: Unified format across platforms

### Output Format
- **Unified schema**: Consistent columns across platforms
- **Source tracking**: Maintains platform identification
- **Metadata preservation**: Keeps original job details
- **Local storage**: CSV files with timestamps

## Migration Status

✅ **Completed Migration**:
- BrightDataScraper → `lambda_scraper/brightdata_api.py`
- S3Processor → `lambda_processor/s3_processor.py`
- DataPreprocessor → `lambda_processor/data_preprocessor.py`
- Configuration files → `lambda_scraper/config/`
- Lambda handlers → `lambda_scraper/handler.py` & `lambda_processor/handler.py`

✅ **New Features**:
- Modular architecture with clear separation
- Comprehensive error handling and logging
- Lambda-optimized code structure
- Enhanced data preprocessing pipeline
- Test suite for verification

## Dependencies

```
boto3          # AWS S3 integration
pandas         # Data processing
numpy          # Numerical operations
requests       # HTTP API calls
```

## Contributing

1. Test changes with `python test_migration.py`
2. Update documentation for new features
3. Follow the existing code structure and patterns
4. Add appropriate error handling and logging

## License

This project is for internal use. Please ensure compliance with Bright Data's terms of service and AWS usage policies. 