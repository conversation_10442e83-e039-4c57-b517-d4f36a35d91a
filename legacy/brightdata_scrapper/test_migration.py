#!/usr/bin/env python3
"""
Test script to verify the migration works correctly.
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all modules can be imported correctly."""
    try:
        # Test Lambda 1 imports
        from lambda_scraper.brightdata_api import BrightDataAPI
        from lambda_scraper.handler import lambda_handler as scraper_handler
        print("✅ Lambda 1 imports successful")
        
        # Test Lambda 2 imports
        from lambda_processor.s3_processor import S3Processor
        from lambda_processor.data_preprocessor import DataPreprocessor
        from lambda_processor.handler import lambda_handler as processor_handler
        print("✅ Lambda 2 imports successful")
        
        # Test shared imports
        from shared.logger import logger
        print("✅ Shared imports successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {str(e)}")
        return False

def test_scraper_creation():
    """Test that the scraper can be created."""
    try:
        from lambda_scraper.brightdata_api import BrightDataAPI
        
        # Test with default config
        scraper = BrightDataAPI("lambda_scraper/config/scraping_inputs.json")
        print("✅ Scraper creation successful")
        
        # Test status method
        status = scraper.get_scraping_status()
        print(f"✅ Scraper status: {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Scraper test failed: {str(e)}")
        return False

def test_processor_creation():
    """Test that the processor can be created."""
    try:
        from lambda_processor.s3_processor import S3Processor
        from lambda_processor.data_preprocessor import DataPreprocessor
        
        # Test S3 processor creation
        processor = S3Processor(bucket_name="test-bucket")
        print("✅ S3 processor creation successful")
        
        # Test data preprocessor creation
        preprocessor = DataPreprocessor()
        print("✅ Data preprocessor creation successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Processor test failed: {str(e)}")
        return False

def test_lambda_handlers():
    """Test that the Lambda handlers can be called."""
    try:
        from lambda_scraper.handler import lambda_handler as scraper_handler
        from lambda_processor.handler import lambda_handler as processor_handler
        
        # Test scraper handler with minimal event
        scraper_event = {"platform": "all"}
        scraper_result = scraper_handler(scraper_event, None)
        print("✅ Scraper handler test successful")
        
        # Test processor handler with minimal event
        processor_event = {"bucket_name": "test-bucket"}
        processor_result = processor_handler(processor_event, None)
        print("✅ Processor handler test successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Lambda handler test failed: {str(e)}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing BrightData Scrapper Migration")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Scraper Creation Test", test_scraper_creation),
        ("Processor Creation Test", test_processor_creation),
        ("Lambda Handler Test", test_lambda_handlers),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name}...")
        if test_func():
            passed += 1
            print(f"✅ {test_name} PASSED")
        else:
            print(f"❌ {test_name} FAILED")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Migration successful.")
        return 0
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    exit(main()) 