#!/usr/bin/env python3
"""
Data Processing Script
Fetches data from S3, processes it using the data preprocessor, and saves results locally.
"""

import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path

# Add the project root to the path for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from lambda_processor.s3_processor import S3Processor
    from lambda_processor.data_preprocessor import DataPreprocessor
    from shared.logger import logger
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the brightdata_scrapper directory")
    sys.exit(1)


def main():
    """Main function to process data from S3 and save locally."""
    
    # Setup logging
    logger.info("Starting data processing script")
    
    try:
        # Initialize the S3 processor
        s3_processor = S3Processor()
        logger.info("S3 processor initialized successfully")
        
        # Get S3 configuration
        logger.info(f"Using S3 bucket: {s3_processor.bucket_name}")
        
        # Fetch and process data from S3
        logger.info("Fetching data from S3...")
        combined_data = s3_processor.process_and_combine_data(save_individual=False, save_combined=False)
        
        if combined_data is None or combined_data.empty:
            logger.warning("No data retrieved from S3")
            return
        
        logger.info(f"Retrieved {len(combined_data)} records from S3")
        
        # Create output directory
        output_dir = Path("processed_data")
        output_dir.mkdir(exist_ok=True)
        
        # Generate timestamp for file naming
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save processed data locally
        output_file = output_dir / f"processed_jobs_{timestamp}.csv"
        combined_data.to_csv(output_file, index=False)
        logger.info(f"Processed data saved to: {output_file}")
        
        # Generate summary statistics
        summary_stats = {
            "total_records": len(combined_data),
            "indeed_records": len(combined_data[combined_data['source'] == 'indeed']) if 'source' in combined_data.columns else 0,
            "linkedin_records": len(combined_data[combined_data['source'] == 'linkedin']) if 'source' in combined_data.columns else 0,
            "columns": list(combined_data.columns),
            "processing_timestamp": timestamp,
            "output_file": str(output_file)
        }
        
        # Save summary to JSON
        summary_file = output_dir / f"processing_summary_{timestamp}.json"
        with open(summary_file, 'w') as f:
            json.dump(summary_stats, f, indent=2)
        logger.info(f"Processing summary saved to: {summary_file}")
        
        # Display summary
        print("\n" + "="*50)
        print("DATA PROCESSING SUMMARY")
        print("="*50)
        print(f"Total records processed: {summary_stats['total_records']}")
        print(f"Indeed records: {summary_stats['indeed_records']}")
        print(f"LinkedIn records: {summary_stats['linkedin_records']}")
        print(f"Output file: {output_file}")
        print(f"Summary file: {summary_file}")
        print("="*50)
        
        logger.info("Data processing completed successfully")
        
    except Exception as e:
        logger.error(f"Error during data processing: {e}")
        raise


if __name__ == "__main__":
    main()
