"url","job_posting_id","job_title","company_name","company_id","job_location","job_summary","applay_link","job_seniority_level","job_function","job_employment_type","job_industries","job_base_pay_range","company_url","job_posted_time","job_num_applicants","discovery_input","apply_link","country_code","title_id","company_logo","job_posted_date","job_poster","application_availability","job_description_formatted","selective_search","base_salary","salary_standards","timestamp","input","error","error_code","warning","warning_code"
"https://www.linkedin.com/jobs/view/senior-lead-ui-ux-designer-at-sproutsai-4136086127?_l=en","4136086127","Senior/Lead UI/UX Designer","SproutsAI","2520554","San Francisco, CA","Senior,Lead UIUX Designer - Update ₹6L – ₹25L 0.01% – 0.015% About The Role You will be our UIUX Designer for a Generative AI startup based out of San Francisco, CA. You will be primarily responsible for UIUX Design for the GennerativeAI platform. You will closely work with a talented team of application developers to design highly usable UIUX designs. As an early hire in a fast-growing startup, you will have a tremendous opportunity to learn latest tech including Cloud, Scalable Systems, GenerativeAI, Security and Performance optimization. This role is remote in India. Youll Get To Lead the user experience design and development for the GenAI, Voice, Chat, Web, mobile application, interaction interfaces, and overall experiences from initial unboxing to daily usage. Represent the voice of the customer with a deep understanding of user personas, pain points, aesthetic preferences, and customer journeys. Collaborate with cross-functional teams to understand customer requirements and create products that meet those needs. Communicate research findings, conceptual ideas, detailed designs, and design rationale to cross-functional teams in both verbal and visual formats. Continuously gather customer feedback through concept testing, analytics tools, A,B testing, surveys, and interviews to validate key aspects of the product and reduce risk. Utilize design thinking and agile methodologies to create and test prototypes, incorporating user feedback and making iterative improvements. Stay informed about current consumer and competitor product offerings, best-in-class user experience, and design trends in the U.S. and international markets. Were Looking For Portfolio is required with the application 3+ years experience in a UIUX Design role A Bachelor`s degree in a Design field from a recognized educational institution required Possesses exceptional design skills, with a track record of creating visually stunning and user-friendly UX, user interfaces, typography, color, layouts, and motion graphics, that are leading-edge experiences Extensive experience creating and leading UX design development, including conducting strategic analysis, creating user flows, designing architecture, developing personas, creating experience maps, ideating wireframes, and testing prototypes Hands-on expertise in GenAI technologies and interfaces are plus Strong ability to communicate and collaborate with internal stakeholders through engaging presentations and compelling storytelling Extensive experience in the development of new products, from initial concept to launch, with a focus on user experience Excellent verbal and written communication skills, analytical abilities, abstract reasoning, and idea orientation with a strong focus on driving progress A strong, informed approach to a user-centered design and the use of user testing, feedback, and insights to drive change A deep passion for Design and a desire to constantly improve and evolve one`s thinking Show more Show less",,"Not Applicable","Other","Full-time","Technology, Information and Internet",,"https://www.linkedin.com/company/aiagent?trk=public_jobs_topcard-org-name","5 months ago",200,"{""location"":""CA"",""keyword"":""UIUX"",""country"":""US"",""time_range"":"""",""job_type"":"""",""experience_level"":"""",""remote"":"""",""company"":"""",""location_radius"":""""}","https://www.linkedin.com/jobs/view/externalApply/4136086127?url=https%3A%2F%2Fapp%2Esproutsai%2Ecom%2Fjob-post%2Fdetails%2F668fb0070164e281ac8dabde%3Fsrc%3Dlinkedin&urlHash=9m4m","US",,"https://media.licdn.com/dms/image/v2/D560BAQG_IpK-JMDZoQ/company-logo_100_100/company-logo_100_100/0/1700665277019/aiagent_logo?e=**********&v=beta&t=5Dd7RRVRaLbWfvW8hkSWbmMzp2v2B11ho8_DGMo5byc","2025-02-23T16:58:36.355Z","{""name"":null,""title"":null,""url"":null}",true,"<section class=""show-more-less-html"" data-max-lines=""5"">
        <div class=""show-more-less-html__markup show-more-less-html__markup--clamp-after-5
            relative overflow-hidden"">
          Senior,Lead UIUX Designer - Update &#x20B9;6L &#x2013; &#x20B9;25L<br><br><ul><li> 0.01% &#x2013; 0.015%<br><br></li></ul><strong>About The Role<br><br></strong>You will be our UIUX Designer for a Generative AI startup based out of San Francisco, CA. You will be primarily responsible for UIUX Design for the GennerativeAI platform. You will closely work with a talented team of application developers to design highly usable UIUX designs. As an early hire in a fast-growing startup, you will have a tremendous opportunity to learn latest tech including Cloud, Scalable Systems, GenerativeAI, Security and Performance optimization.<br><br>This role is remote in India.<br><br>Youll Get To<br><br><ul><li>Lead the user experience design and development for the GenAI, Voice, Chat, Web, mobile application, interaction interfaces, and overall experiences from initial unboxing to daily usage.</li><li>Represent the voice of the customer with a deep understanding of user personas, pain points, aesthetic preferences, and customer journeys.</li><li>Collaborate with cross-functional teams to understand customer requirements and create products that meet those needs.</li><li>Communicate research findings, conceptual ideas, detailed designs, and design rationale to cross-functional teams in both verbal and visual formats.</li><li>Continuously gather customer feedback through concept testing, analytics tools, A,B testing, surveys, and interviews to validate key aspects of the product and reduce risk.</li><li>Utilize design thinking and agile methodologies to create and test prototypes, incorporating user feedback and making iterative improvements.</li><li>Stay informed about current consumer and competitor product offerings, best-in-class user experience, and design trends in the U.S. and international markets.<br><br></li></ul>Were Looking For<br><br>Portfolio is required with the application<br><br><ul><li>3+ years experience in a UIUX Design role</li><li>A Bachelor`s degree in a Design field from a recognized educational institution required</li><li>Possesses exceptional design skills, with a track record of creating visually stunning and user-friendly UX, user interfaces, typography, color, layouts, and motion graphics, that are leading-edge experiences</li><li>Extensive experience creating and leading UX design development, including conducting strategic analysis, creating user flows, designing architecture, developing personas, creating experience maps, ideating wireframes, and testing prototypes</li><li>Hands-on expertise in GenAI technologies and interfaces are plus</li><li>Strong ability to communicate and collaborate with internal stakeholders through engaging presentations and compelling storytelling</li><li>Extensive experience in the development of new products, from initial concept to launch, with a focus on user experience</li><li>Excellent verbal and written communication skills, analytical abilities, abstract reasoning, and idea orientation with a strong focus on driving progress</li><li>A strong, informed approach to a user-centered design and the use of user testing, feedback, and insights to drive change</li><li>A deep passion for Design and a desire to constantly improve and evolve one`s thinking</li></ul>
        </div>

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--more
        ml-0.5"" data-tracking-control-name=""public_jobs_show-more-html-btn"" aria-label=""Show more"" aria-expanded=""false"">
<!---->
        
            Show more
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/cyolgscd0imw2ldqppkrb84vo""></icon>
    </button>
  

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--less
        ml-0.5"" data-tracking-control-name=""public_jobs_show-less-html-btn"" aria-label=""Show less"" aria-expanded=""true"">
<!---->
        
            Show less
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/4chtt12k98xwnba1nimld2oyg""></icon>
    </button>
  
<!---->    </section>",,"{""min_amount"":null,""max_amount"":null,""currency"":null,""payment_period"":null}",,"2025-07-23T16:58:36.395Z","{""url"":""https://www.linkedin.com/jobs/view/senior-lead-ui-ux-designer-at-sproutsai-4136086127"",""discovery_input"":{""location"":""CA"",""keyword"":""UIUX"",""country"":""US"",""time_range"":null,""job_type"":null,""experience_level"":null,""remote"":null}}",,,,
"https://www.linkedin.com/jobs/view/internship-3d-generalist-vehicle-user-interface-fall-2025-at-tesla-4217132725?_l=en","4217132725","Internship, 3D Generalist, Vehicle User Interface (Fall 2025)","Tesla","15564","Hawthorne, CA","What To Expect Consider before submitting an application: This position is expected to start around September 2025 and continue through the Fall term (approximately December 2025) or into Spring 2026 if available and there is an opportunity to do so. We ask for a minimum of 12 weeks, full-time and on-site, for most internships. Our internship program is for students who are actively enrolled in an academic program. Recent graduates seeking employment after graduation and not returning to school should apply for full-time positions, not internships. International Students: If your work authorization is through CPT, please consult your school on your ability to work 40 hours per week before applying. You must be able to work 40 hours per week on-site. Many students will be limited to part-time during the academic year. About The Team The Tesla UI Design team is responsible for the user interface of all vehicles and the mobile app. As a member of the Visualization Team, you will be intrical to the design and creation of digital assets and environments, as well as real-time and pre-rendered photorealistic automotive and product visualizations. Your work has the potential to be seen on a global scale, helping influence people and communities to accelerate the world’s transition to sustainable energy. What You'll Do Creation of realistic and fictitious virtual environments and assets Optimization of assets and scenes for use in both real-time and pre-rendered environments Ability to create good topology 3D models within pre-determined polygon count limits Material generation and virtual material prototyping (PBR pipeline) Environmental atmospheric and lighting setups, UV and texturing Rendering high-quality, photorealistic vehicle and product content Scanning (photogrammetry) of real-world, organic environments and assets What You'll Bring Pursuing a degree in Design, UIUX, or relevant field of study with a graduation date between 2025 -2026 Must be able to relocate and work on site in Los Angeles, CA Strong portfolio displaying your amazing skills as a 3D generalist with special attention to Vehicles and Environments Solid artistic and technical knowledge Knowledge of PBR based workflows and software (e.g., Substance 3D) Ability to understand color, visual compositions, and lighting Strong knowledge of traditional DCC tools (e.g., Maya, 3ds Max, Blender, Zbrush, Photoshop) Experience with photogrammetry and knowledge of processing software (e.g., Reality Capture) a plus Benefits Compensation and Benefits As a full-time Tesla Intern, you will be eligible for: Aetna PPO and HSA plans > 2 medical plan options with $0 payroll deduction Family-building, fertility, adoption and surrogacy benefits Dental (including orthodontic coverage) and vision plans. Both have an option with a $0 payroll contribution Company Paid (Health Savings Account) HSA Contribution when enrolled in the High Deductible Medical Plan with HSA Healthcare and Dependent Care Flexible Spending Accounts (FSA) 401(k), Employee Stock Purchase Plans, and other financial benefits Company Paid Basic Life, AD&D, and short-term disability insurance Employee Assistance Program Sick time after 90 days of employment and Paid Holidays Back-up childcare and parenting support resources Voluntary benefits to include: critical illness, hospital indemnity, accident insurance, theft & legal services, and pet insurance Commuter benefits Employee discounts and perks program Expected Compensation $36.00 - $50.00/hour + benefits Pay offered may vary depending on multiple individualized factors, including market location, job-related knowledge, skills, and experience. The total compensation package for this position may also include other elements dependent on the position offered. Details of participation in these benefit plans will be provided if an employee receives an offer of employment. , Tesla Show more Show less",,"Internship","Design, Art/Creative, and Information Technology","Internship","Motor Vehicle Manufacturing, Renewable Energy Semiconductor Manufacturing, and Utilities",,"https://www.linkedin.com/company/tesla-motors?trk=public_jobs_topcard-org-name","2 months ago",200,"{""location"":""CA"",""keyword"":""UIUX"",""country"":""US"",""time_range"":"""",""job_type"":"""",""experience_level"":"""",""remote"":"""",""company"":"""",""location_radius"":""""}","https://www.linkedin.com/jobs/view/externalApply/4217132725?url=https%3A%2F%2Fwww%2Etesla%2Ecom%2Fcareers%2Fsearch%2Fjob%2F242180%3Fsource%3DLinkedIn&urlHash=OliL","US","7121","https://media.licdn.com/dms/image/v2/C4D0BAQHUcu98SZ2TVw/company-logo_100_100/company-logo_100_100/0/1630576446368/tesla_motors_logo?e=**********&v=beta&t=XoR-tn8x_HdLXtmMsln9xhsZ3fOnB3vJyQUwi3lBI0s","2025-05-24T16:58:37.386Z","{""name"":null,""title"":null,""url"":null}",true,"<section class=""show-more-less-html"" data-max-lines=""5"">
        <div class=""show-more-less-html__markup show-more-less-html__markup--clamp-after-5
            relative overflow-hidden"">
          <strong>What To Expect<br></strong>Consider before submitting an application:<br><br>This position is expected to start around September 2025 and continue through the Fall term (approximately December 2025) or into Spring 2026 if available and there is an opportunity to do so. We ask for a minimum of 12 weeks, full-time and on-site, for most internships. Our internship program is for students who are actively enrolled in an academic program. Recent graduates seeking employment after graduation and not returning to school should apply for full-time positions, not internships.<br><br>International Students: If your work authorization is through CPT, please consult your school on your ability to work 40 hours per week before applying. You must be able to work 40 hours per week on-site. Many students will be limited to part-time during the academic year.<br><br><strong>About The Team<br></strong>The Tesla UI Design team is responsible for the user interface of all vehicles and the mobile app. As a member of the Visualization Team, you will be intrical to the design and creation of digital assets and environments, as well as real-time and pre-rendered photorealistic automotive and product visualizations. Your work has the potential to be seen on a global scale, helping influence people and communities to accelerate the world&#x2019;s transition to sustainable energy.<br><br><strong>What You&apos;ll Do<br><br></strong><li>Creation of realistic and fictitious virtual environments and assets</li><li>Optimization of assets and scenes for use in both real-time and pre-rendered environments</li><li>Ability to create good topology 3D models within pre-determined polygon count limits </li><li>Material generation and virtual material prototyping (PBR pipeline) </li><li>Environmental atmospheric and lighting setups, UV and texturing</li><li>Rendering high-quality, photorealistic vehicle and product content</li><li>Scanning (photogrammetry) of real-world, organic environments and assets <br><br><br></li><strong>What You&apos;ll Bring<br><br></strong><li>Pursuing a degree in Design, UIUX, or relevant field of study with a graduation date between 2025 -2026</li><li>Must be able to relocate and work on site in Los Angeles, CA</li><li>Strong portfolio displaying your amazing skills as a 3D generalist with special attention to Vehicles and Environments</li><li>Solid artistic and technical knowledge</li><li>Knowledge of PBR based workflows and software (e.g., Substance 3D)</li><li>Ability to understand color, visual compositions, and lighting </li><li>Strong knowledge of traditional DCC tools (e.g., Maya, 3ds Max, Blender, Zbrush, Photoshop) </li><li>Experience with photogrammetry and knowledge of processing software (e.g., Reality Capture) a plus<br><br><br></li><strong>Benefits<br></strong><strong>Compensation and Benefits<br></strong>As a full-time Tesla Intern, you will be eligible for:<br><li> Aetna PPO and HSA plans &gt; 2 medical plan options with $0 payroll deduction</li><li> Family-building, fertility, adoption and surrogacy benefits</li><li> Dental (including orthodontic coverage) and vision plans. Both have an option with a $0 payroll contribution</li><li> Company Paid (Health Savings Account) HSA Contribution when enrolled in the High Deductible Medical Plan with HSA</li><li> Healthcare and Dependent Care Flexible Spending Accounts (FSA)</li><li> 401(k), Employee Stock Purchase Plans, and other financial benefits</li><li> Company Paid Basic Life, AD&amp;D, and short-term disability insurance</li><li> Employee Assistance Program</li><li> Sick time after 90 days of employment and Paid Holidays</li><li> Back-up childcare and parenting support resources</li><li> Voluntary benefits to include: critical illness, hospital indemnity, accident insurance, theft &amp; legal services, and pet insurance</li><li> Commuter benefits</li><li> Employee discounts and perks program<br><br><br></li>Expected Compensation<br><br>$36.00 - $50.00/hour + benefits<br><br>Pay offered may vary depending on multiple individualized factors, including market location, job-related knowledge, skills, and experience. The total compensation package for this position may also include other elements dependent on the position offered. Details of participation in these benefit plans will be provided if an employee receives an offer of employment.<br><br>, Tesla
        </div>

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--more
        ml-0.5"" data-tracking-control-name=""public_jobs_show-more-html-btn"" aria-label=""Show more"" aria-expanded=""false"">
<!---->
        
            Show more
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/cyolgscd0imw2ldqppkrb84vo""></icon>
    </button>
  

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--less
        ml-0.5"" data-tracking-control-name=""public_jobs_show-less-html-btn"" aria-label=""Show less"" aria-expanded=""true"">
<!---->
        
            Show less
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/4chtt12k98xwnba1nimld2oyg""></icon>
    </button>
  
<!---->    </section>",,"{""min_amount"":null,""max_amount"":null,""currency"":null,""payment_period"":null}",,"2025-07-23T16:58:37.412Z","{""url"":""https://www.linkedin.com/jobs/view/internship-3d-generalist-vehicle-user-interface-fall-2025-at-tesla-4217132725"",""discovery_input"":{""location"":""CA"",""keyword"":""UIUX"",""country"":""US"",""time_range"":null,""job_type"":null,""experience_level"":null,""remote"":null}}",,,,
"https://www.linkedin.com/jobs/view/front-end-ui-engineer-734-at-protegrity-4267762398?_l=en","4267762398","Front-End UI Engineer 734","Protegrity","15741","San Jose, CA","At Protegrity, we lead innovation by using AI and quantum-resistant cryptography to transform data protection across cloud-native, hybrid, on-premises, and open source environments. We leverage advanced cryptographic methods such as tokenization, format-preserving encryption, and quantum-resilient techniques to protect sensitive data. As a global leader in data security, our mission is to ensure that data isn’t just valuable but also usable, trusted, and safe. Protegrity offers the opportunity to work at the intersection of innovation and collaboration, with the ability to make a meaningful impact on the industry while working alongside some of the brightest minds. Together, we are redefining how the world safeguards data, enabling organizations to thrive in a GenAI era where data is the ultimate currency. If you're ready to shape the future of data security, Protegrity is the place for you. Protegrity is seeking a passionate and talented Front-End UI Engineer to join our Product Innovation Team. This position is a hybrid position, with a preference for candidates located in Northern California, New York or Stamford Connecticut. Data Privacy is important to all of us. At Protegrity our products make this possible in rest, in motion and in view for some of the largest companies in the world. Protegrity puts our customers in the best possible position to choose how they want to protect their data and make it private. Thanks to the centralized management and transparency of our platform- a “single pane of glass”- teams can see where the data resides and what its purpose is. Unlike other data protection options, the Protegrity data Protection Platform clarifies the state of data so organizations can choose how to protect it and keep it private using the full range of methods from basic monitoring and dynamic data masking to highly secure vaultless tokenization. Since our founding over 10 years ago, we are fully vested in helping clients protect their information. Whether encrypting, tokenizing, or applying privacy methods, our platform secures the data behind the many operational systems that drive the day-to-day functions of business, as well as the analytical systems behind decision-making, personalized customer experiences, and AI modeling. All of this propels businesses forward by fueling innovation and giving clients a competitive advantage over less savvy competitors. This is a once in a lifetime opportunity to be part of a global team with a product that the business marketplace MUST have in place to do business. Responsibilities Work as part of our centralized UI/UX Team, bringing cross company branding and identity to a global level across our security product user interfaces. Daily/weekly collaboration with teammates: engineer standups, team meetings, 1:1, development demos, sprint planning, UX design scrub meetings. Experience with WCAG Accessibility Standards 2.2 Level AA. Build, debug and maintain scalable, responsive, quality front-end solutions following best practices that function effectively across a variety of platforms and devices. HTML5, CSS/SASS (or comparable CSS preprocessor), Bootstrap, JavaScript and/or JS Frameworks such as React.js/Jest. On-hands experience with UX Designers. Knowledge of Figma (or alternative) bridging the gap between design and development. Implementing high-fidelity designs transformed into smooth, intuitive, easy-to-use journeys in our user interfaces. Curiosity and enthusiasm for leveraging Ai development tools to streamline work and time management. Comfortable using Git for version control and Webpack for automation tasks. Experience working with design systems and understanding the fundamentals and importance of this ecosystem. Testing and debugging at all stages of the SDLC. Participate in code review and open-brainstorm sessions. Help in facilitation of JIRA scrum tasks following agile methodologies. Familiarity working with REST APIs and JSON. Qualifications 5+ years of software engineering experience with a college degree and/or other demonstration of deep expertise in front-end engineering (profile encouraged). High competence in verbal and written communication is essential. Accountability and self-management to handle several projects at once across multiple code bases (we are a centralized team). Expertise in computer science fundamentals. Ability to multiply the abilities and skills of others. Knowledge and experience implementing client-side security best practices. Comfort with critical thinking and creative problem-solving. Ability to articulate ideas clearly and collaborate effectively with cross-functional team members, including VPs, POs, PMs, QA, DevOps, Program Management, designers and back-end developers. Preferred Qualifications Knowledge and experience working in data security. Scrum master/project management experience. Hands on experience working with RESTful APIs to OpenAPI 3.0 specification. Enthusiasm to learn and understand natural language processing (NLP) for human-AI interaction. Familiarity with cloud services like AWS, Google Cloud, or Azure. Familiarity with building Jenkins pipelines and other CI/CD processes. Inspection of code quality, scanning, monitoring, and security with Snyk and SonarQube. Design and Prototyping. Bonus points for working with Docker Containers Key Personal Attributes Exceptional problem-solving skills. Detail-oriented with an eye for aesthetics and usability. Passion for staying up to date on the latest trends and/or new developments in the UIUX arena. Understanding user needs and implementation of solutions. Ability to efficiently manage workload and prioritize tasks to meet deadlines. Understanding and sharing the feelings of others, fostering a supportive team environment is important. Showing Integrity and always maintaining honest and ethical standards. Flexibility in schedule working internationally across time zones. Organization is top tier, tracking multiple tasks at once and keeping projects running as efficiently as possible and on time. Analytical problem solving, able to make bite-size chunks out of complex issues. Why Choose Protegrity Become a member of a leading Data Protection, Privacy and Security company during one of the best market opportunities to come along in a generation. Competitive Compensation/Total Reward Packages that include: Health Benefits (Health/Dental/Vision). Paid Time Off (PTO). 401K. Annual Bonus Incentives. Short and Long Term Disability. Work on global projects with diverse, energetic, team members who respect each other and celebrate differences. Talent First Workforce. Working Model This is a hybrid role and will require you to be in office 2-3 days a week. In certain circumstances the work model may change to accommodate business needs. Should you accept this position, you will be required to consent to and successfully complete a background investigation. This may include, subject to local laws, verification of extended education and additional criminal and civil checks. We offer a competitive salary and comprehensive benefits with generous vacation and holiday time off. All employees are also provided access to ongoing learning & development. Ensuring a diverse and inclusive workplace is our priority. We are committed to an environment of acceptance where you are free to bring your full self to work. All qualified applicants and current employees will not be discriminated against on the basis of race, color, religion, sex, sexual orientation, gender identity, age, national origin, disability or veteran status. Please reference Section 12: Supplemental Notice for Job Applicants in our Privacy Policy to inform you of the categories of personal information that we collect from individuals who inquire about and/or apply to work for Protegrity USA, Inc., or its parent company, subsidiaries or affiliates, and the purposes for which we use such personal information. Compensation Range: $117K - $144K Show more Show less",,"Mid-Senior level","Engineering and Information Technology","Full-time","Computer and Network Security","$117,000.00/yr - $144,000.00/yr","https://www.linkedin.com/company/protegrity?trk=public_jobs_topcard-org-name","6 days ago",50,"{""location"":""CA"",""keyword"":""UIUX"",""country"":""US"",""time_range"":"""",""job_type"":"""",""experience_level"":"""",""remote"":"""",""company"":"""",""location_radius"":""""}","https://www.linkedin.com/jobs/view/externalApply/4267762398?url=https%3A%2F%2Fjobs%2Eashbyhq%2Ecom%2Fprotegrity%2Fcf118b10-36cd-460a-a260-bb675e9ce1dc%3Futm_source%3DLinkedin_Applicant&urlHash=igLf","US","8632","https://media.licdn.com/dms/image/v2/D560BAQEjzkD3AIx29w/company-logo_100_100/company-logo_100_100/0/1697749697495/protegrity_logo?e=**********&v=beta&t=QE8SK2VHsmJBImfsVPQk2a_x7rNjtiuK_P9ECf3btic","2025-07-17T16:58:41.671Z","{""name"":null,""title"":null,""url"":null}",true,"<section class=""show-more-less-html"" data-max-lines=""5"">
        <div class=""show-more-less-html__markup show-more-less-html__markup--clamp-after-5
            relative overflow-hidden"">
          At Protegrity, we lead innovation by using AI and quantum-resistant cryptography to transform data protection across cloud-native, hybrid, on-premises, and open source environments. We leverage advanced cryptographic methods such as tokenization, format-preserving encryption, and quantum-resilient techniques to protect sensitive data. As a global leader in data security, our mission is to ensure that data isn&#x2019;t just valuable but also usable, trusted, and safe.<br><br>Protegrity offers the opportunity to work at the intersection of innovation and collaboration, with the ability to make a meaningful impact on the industry while working alongside some of the brightest minds. Together, we are redefining how the world safeguards data, enabling organizations to thrive in a GenAI era where data is the ultimate currency. If you&apos;re ready to shape the future of data security, Protegrity is the place for you.<br><br>Protegrity is seeking a passionate and talented Front-End UI Engineer to join our Product Innovation Team. This position is a hybrid position, with a preference for candidates located in Northern California, New York or Stamford Connecticut.<br><br>Data Privacy is important to all of us. At Protegrity our products make this possible in rest, in motion and in view for some of the largest companies in the world. Protegrity puts our customers in the best possible position to choose how they want to protect their data and make it private. Thanks to the centralized management and transparency of our platform- a &#x201C;single pane of glass&#x201D;- teams can see where the data resides and what its purpose is. Unlike other data protection options, the Protegrity data Protection Platform clarifies the state of data so organizations can choose how to protect it and keep it private using the full range of methods from basic monitoring and dynamic data masking to highly secure vaultless tokenization.<br><br>Since our founding over 10 years ago, we are fully vested in helping clients protect their information. Whether encrypting, tokenizing, or applying privacy methods, our platform secures the data behind the many operational systems that drive the day-to-day functions of business, as well as the analytical systems behind decision-making, personalized customer experiences, and AI modeling. All of this propels businesses forward by fueling innovation and giving clients a competitive advantage over less savvy competitors. This is a once in a lifetime opportunity to be part of a global team with a product that the business marketplace MUST have in place to do business.<br><br><strong>Responsibilities<br><br></strong><ul><li>Work as part of our centralized UI/UX Team, bringing cross company branding and identity to a global level across our security product user interfaces.</li><li>Daily/weekly collaboration with teammates: engineer standups, team meetings, 1:1, development demos, sprint planning, UX design scrub meetings.</li><li>Experience with WCAG Accessibility Standards 2.2 Level AA.</li><li>Build, debug and maintain scalable, responsive, quality front-end solutions following best practices that function effectively across a variety of platforms and devices.</li><li>HTML5, CSS/SASS (or comparable CSS preprocessor), Bootstrap, JavaScript and/or JS Frameworks such as React.js/Jest.</li><li>On-hands experience with UX Designers. Knowledge of Figma (or alternative) bridging the gap between design and development. Implementing high-fidelity designs transformed into smooth, intuitive, easy-to-use journeys in our user interfaces.</li><li>Curiosity and enthusiasm for leveraging Ai development tools to streamline work and time management.</li><li>Comfortable using Git for version control and Webpack for automation tasks.</li><li>Experience working with design systems and understanding the fundamentals and importance of this ecosystem.</li><li>Testing and debugging at all stages of the SDLC.</li><li>Participate in code review and open-brainstorm sessions.</li><li>Help in facilitation of JIRA scrum tasks following agile methodologies.</li><li>Familiarity working with REST APIs and JSON.<br><br></li></ul><strong>Qualifications<br><br></strong><ul><li>5+ years of software engineering experience with a college degree and/or other demonstration of deep expertise in front-end engineering (profile encouraged).</li><li>High competence in verbal and written communication is essential.</li><li>Accountability and self-management to handle several projects at once across multiple code bases (we are a centralized team).</li><li>Expertise in computer science fundamentals.</li><li>Ability to multiply the abilities and skills of others.</li><li>Knowledge and experience implementing client-side security best practices.</li><li>Comfort with critical thinking and creative problem-solving.</li><li>Ability to articulate ideas clearly and collaborate effectively with cross-functional team members, including VPs, POs, PMs, QA, DevOps, Program Management, designers and back-end developers.<br><br></li></ul><strong>Preferred Qualifications<br><br></strong><ul><li>Knowledge and experience working in data security.</li><li>Scrum master/project management experience.</li><li>Hands on experience working with RESTful APIs to OpenAPI 3.0 specification.</li><li>Enthusiasm to learn and understand natural language processing (NLP) for human-AI interaction.</li><li>Familiarity with cloud services like AWS, Google Cloud, or Azure.</li><li>Familiarity with building Jenkins pipelines and other CI/CD processes.</li><li>Inspection of code quality, scanning, monitoring, and security with Snyk and SonarQube.</li><li>Design and Prototyping.</li><li>Bonus points for working with Docker Containers<br><br></li></ul><strong>Key Personal Attributes<br><br></strong><ul><li>Exceptional problem-solving skills.</li><li>Detail-oriented with an eye for aesthetics and usability.</li><li>Passion for staying up to date on the latest trends and/or new developments in the UIUX arena.</li><li>Understanding user needs and implementation of solutions.</li><li>Ability to efficiently manage workload and prioritize tasks to meet deadlines.</li><li>Understanding and sharing the feelings of others, fostering a supportive team environment is important.</li><li>Showing Integrity and always maintaining honest and ethical standards.</li><li>Flexibility in schedule working internationally across time zones.</li><li>Organization is top tier, tracking multiple tasks at once and keeping projects running as efficiently as possible and on time.</li><li>Analytical problem solving, able to make bite-size chunks out of complex issues.<br><br></li></ul><strong>Why Choose Protegrity<br><br></strong><ul><li>Become a member of a leading Data Protection, Privacy and Security company during one of the best market opportunities to come along in a generation.</li><li>Competitive Compensation/Total Reward Packages that include:</li><li>Health Benefits (Health/Dental/Vision).</li><li>Paid Time Off (PTO).</li><li>401K.</li><li>Annual Bonus Incentives.</li><li>Short and Long Term Disability.</li><li>Work on global projects with diverse, energetic, team members who respect each other and celebrate differences.</li><li>Talent First Workforce.<br><br></li></ul><strong>Working Model<br><br></strong><ul><li>This is a hybrid role and will require you to be in office 2-3 days a week. In certain circumstances the work model may change to accommodate business needs.<br><br></li></ul>Should you accept this position, you will be required to consent to and successfully complete a background investigation. This may include, subject to local laws, verification of extended education and additional criminal and civil checks.<br><br>We offer a competitive salary and comprehensive benefits with generous vacation and holiday time off. All employees are also provided access to ongoing learning &amp; development.<br><br>Ensuring a diverse and inclusive workplace is our priority. We are committed to an environment of acceptance where you are free to bring your full self to work. All qualified applicants and current employees will not be discriminated against on the basis of race, color, religion, sex, sexual orientation, gender identity, age, national origin, disability or veteran status.<br><br>Please reference Section 12: Supplemental Notice for Job Applicants in our Privacy Policy to inform you of the categories of personal information that we collect from individuals who inquire about and/or apply to work for Protegrity USA, Inc., or its parent company, subsidiaries or affiliates, and the purposes for which we use such personal information.<br><br>Compensation Range: $117K - $144K
        </div>

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--more
        ml-0.5"" data-tracking-control-name=""public_jobs_show-more-html-btn"" aria-label=""Show more"" aria-expanded=""false"">
<!---->
        
            Show more
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/cyolgscd0imw2ldqppkrb84vo""></icon>
    </button>
  

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--less
        ml-0.5"" data-tracking-control-name=""public_jobs_show-less-html-btn"" aria-label=""Show less"" aria-expanded=""true"">
<!---->
        
            Show less
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/4chtt12k98xwnba1nimld2oyg""></icon>
    </button>
  
<!---->    </section>",,"{""min_amount"":117000,""max_amount"":144000,""currency"":""$"",""payment_period"":""yr""}","Retrieved from the description.","2025-07-23T16:58:41.705Z","{""url"":""https://www.linkedin.com/jobs/view/front-end-ui-engineer-734-at-protegrity-4267762398"",""discovery_input"":{""location"":""CA"",""keyword"":""UIUX"",""country"":""US"",""time_range"":null,""job_type"":null,""experience_level"":null,""remote"":null}}",,,,
"https://www.linkedin.com/jobs/view/front-end-ui-engineer-734-at-tm2-group-llc-4267770307?_l=en","4267770307","Front-End UI Engineer 734","TM2 Group, LLC","42394501","San Jose, CA","At Protegrity, we lead innovation by using AI and quantum-resistant cryptography to transform data protection across cloud-native, hybrid, on-premises, and open source environments. We leverage advanced cryptographic methods such as tokenization, format-preserving encryption, and quantum-resilient techniques to protect sensitive data. As a global leader in data security, our mission is to ensure that data isn’t just valuable but also usable, trusted, and safe. Protegrity offers the opportunity to work at the intersection of innovation and collaboration, with the ability to make a meaningful impact on the industry while working alongside some of the brightest minds. Together, we are redefining how the world safeguards data, enabling organizations to thrive in a GenAI era where data is the ultimate currency. If you're ready to shape the future of data security, Protegrity is the place for you. Protegrity is seeking a passionate and talented Front-End UI Engineer to join our Product Innovation Team. This position is a hybrid position, with a preference for candidates located in Northern California, New York or Stamford Connecticut. Data Privacy is important to all of us. At Protegrity our products make this possible in rest, in motion and in view for some of the largest companies in the world. Protegrity puts our customers in the best possible position to choose how they want to protect their data and make it private. Thanks to the centralized management and transparency of our platform- a “single pane of glass”- teams can see where the data resides and what its purpose is. Unlike other data protection options, the Protegrity data Protection Platform clarifies the state of data so organizations can choose how to protect it and keep it private using the full range of methods from basic monitoring and dynamic data masking to highly secure vaultless tokenization. Since our founding over 10 years ago, we are fully vested in helping clients protect their information. Whether encrypting, tokenizing, or applying privacy methods, our platform secures the data behind the many operational systems that drive the day-to-day functions of business, as well as the analytical systems behind decision-making, personalized customer experiences, and AI modeling. All of this propels businesses forward by fueling innovation and giving clients a competitive advantage over less savvy competitors. This is a once in a lifetime opportunity to be part of a global team with a product that the business marketplace MUST have in place to do business. Responsibilities: Work as part of our centralized UI/UX Team, bringing cross company branding and identity to a global level across our security product user interfaces. Daily/weekly collaboration with teammates: engineer standups, team meetings, 1:1, development demos, sprint planning, UX design scrub meetings. Experience with WCAG Accessibility Standards 2.2 Level AA. Build, debug and maintain scalable, responsive, quality front-end solutions following best practices that function effectively across a variety of platforms and devices. HTML5, CSS/SASS (or comparable CSS preprocessor), Bootstrap, JavaScript and/or JS Frameworks such as React.js/Jest. On-hands experience with UX Designers. Knowledge of Figma (or alternative) bridging the gap between design and development. Implementing high-fidelity designs transformed into smooth, intuitive, easy-to-use journeys in our user interfaces. Curiosity and enthusiasm for leveraging Ai development tools to streamline work and time management. Comfortable using Git for version control and Webpack for automation tasks. Experience working with design systems and understanding the fundamentals and importance of this ecosystem. Testing and debugging at all stages of the SDLC. Participate in code review and open-brainstorm sessions. Help in facilitation of JIRA scrum tasks following agile methodologies. Familiarity working with REST APIs and JSON. Qualifications : 5+ years of software engineering experience with a college degree and/or other demonstration of deep expertise in front-end engineering (profile encouraged). High competence in verbal and written communication is essential. Accountability and self-management to handle several projects at once across multiple code bases (we are a centralized team). Expertise in computer science fundamentals. Ability to multiply the abilities and skills of others. Knowledge and experience implementing client-side security best practices. Comfort with critical thinking and creative problem-solving. Ability to articulate ideas clearly and collaborate effectively with cross-functional team members, including VPs, POs, PMs, QA, DevOps, Program Management, designers and back-end developers. Preferred Qualifications: Knowledge and experience working in data security. Scrum master/project management experience. Hands on experience working with RESTful APIs to OpenAPI 3.0 specification. Enthusiasm to learn and understand natural language processing (NLP) for human-AI interaction. Familiarity with cloud services like AWS, Google Cloud, or Azure. Familiarity with building Jenkins pipelines and other CI/CD processes. Inspection of code quality, scanning, monitoring, and security with Snyk and SonarQube. Design and Prototyping. Bonus points for working with Docker Containers Key Personal Attributes: Exceptional problem-solving skills. Detail-oriented with an eye for aesthetics and usability. Passion for staying up to date on the latest trends and/or new developments in the UIUX arena. Understanding user needs and implementation of solutions. Ability to efficiently manage workload and prioritize tasks to meet deadlines. Understanding and sharing the feelings of others, fostering a supportive team environment is important. Showing Integrity and always maintaining honest and ethical standards. Flexibility in schedule working internationally across time zones. Organization is top tier, tracking multiple tasks at once and keeping projects running as efficiently as possible and on time. Analytical problem solving, able to make bite-size chunks out of complex issues. Why Choose Protegrity: Become a member of a leading Data Protection, Privacy and Security company during one of the best market opportunities to come along in a generation. Competitive Compensation/Total Reward Packages that include: Health Benefits (Health/Dental/Vision). Paid Time Off (PTO). 401K. Annual Bonus Incentives. Short and Long Term Disability. Work on global projects with diverse, energetic, team members who respect each other and celebrate differences. Talent First Workforce. Working Model: This is a hybrid role and will require you to be in office 2-3 days a week. In certain circumstances the work model may change to accommodate business needs. Should you accept this position, you will be required to consent to and successfully complete a background investigation. This may include, subject to local laws, verification of extended education and additional criminal and civil checks. We offer a competitive salary and comprehensive benefits with generous vacation and holiday time off. All employees are also provided access to ongoing learning & development. Ensuring a diverse and inclusive workplace is our priority. We are committed to an environment of acceptance where you are free to bring your full self to work. All qualified applicants and current employees will not be discriminated against on the basis of race, color, religion, sex, sexual orientation, gender identity, age, national origin, disability or veteran status. Please reference Section 12: Supplemental Notice for Job Applicants in our Privacy Policy to inform you of the categories of personal information that we collect from individuals who inquire about and/or apply to work for Protegrity USA, Inc., or its parent company, subsidiaries or affiliates, and the purposes for which we use such personal information. Compensation Range: $117K - $144K Show more Show less",,"Mid-Senior level","Engineering and Information Technology","Full-time","Business Consulting and Services","$117,000.00/yr - $144,000.00/yr","https://www.linkedin.com/company/tm2-group-llc?trk=public_jobs_topcard-org-name","6 days ago",29,"{""location"":""CA"",""keyword"":""UIUX"",""country"":""US"",""time_range"":"""",""job_type"":"""",""experience_level"":"""",""remote"":"""",""company"":"""",""location_radius"":""""}","https://www.linkedin.com/jobs/view/externalApply/4267770307?url=https%3A%2F%2Fjobs%2Eashbyhq%2Ecom%2Fprotegrity%2Fcf118b10-36cd-460a-a260-bb675e9ce1dc%2Fapplication%3Futm_source%3Dl7522D97Kr&urlHash=o0sM","US","8632","https://media.licdn.com/dms/image/v2/C4D0BAQHv63NXbANpTA/company-logo_100_100/company-logo_100_100/0/1630569237936?e=**********&v=beta&t=4-hSetAXxA92cKBDLkgNXP2jCKJdzAwcsdiju57i51o","2025-07-17T16:58:46.733Z","{""name"":null,""title"":null,""url"":null}",true,"<section class=""show-more-less-html"" data-max-lines=""5"">
        <div class=""show-more-less-html__markup show-more-less-html__markup--clamp-after-5
            relative overflow-hidden"">
          At Protegrity, we lead innovation by using AI and quantum-resistant cryptography to transform data protection across cloud-native, hybrid, on-premises, and open source environments. We leverage advanced cryptographic methods such as tokenization, format-preserving encryption, and quantum-resilient techniques to protect sensitive data. As a global leader in data security, our mission is to ensure that data isn&#x2019;t just valuable but also usable, trusted, and safe.<br><br>Protegrity offers the opportunity to work at the intersection of innovation and collaboration, with the ability to make a meaningful impact on the industry while working alongside some of the brightest minds. Together, we are redefining how the world safeguards data, enabling organizations to thrive in a GenAI era where data is the ultimate currency. If you&apos;re ready to shape the future of data security, Protegrity is the place for you.<br><br>Protegrity is seeking a passionate and talented Front-End UI Engineer to join our Product Innovation Team. This position is a hybrid position, with a preference for candidates located in Northern California, New York or Stamford Connecticut.<br><br>Data Privacy is important to all of us. At Protegrity our products make this possible in rest, in motion and in view for some of the largest companies in the world. Protegrity puts our customers in the best possible position to choose how they want to protect their data and make it private. Thanks to the centralized management and transparency of our platform- a &#x201C;single pane of glass&#x201D;- teams can see where the data resides and what its purpose is. Unlike other data protection options, the Protegrity data Protection Platform clarifies the state of data so organizations can choose how to protect it and keep it private using the full range of methods from basic monitoring and dynamic data masking to highly secure vaultless tokenization.<br><br>Since our founding over 10 years ago, we are fully vested in helping clients protect their information. Whether encrypting, tokenizing, or applying privacy methods, our platform secures the data behind the many operational systems that drive the day-to-day functions of business, as well as the analytical systems behind decision-making, personalized customer experiences, and AI modeling. All of this propels businesses forward by fueling innovation and giving clients a competitive advantage over less savvy competitors. This is a once in a lifetime opportunity to be part of a global team with a product that the business marketplace MUST have in place to do business.<br><br><strong>Responsibilities:<br><br></strong><ul><li>Work as part of our centralized UI/UX Team, bringing cross company branding and identity to a global level across our security product user interfaces. </li><li>Daily/weekly collaboration with teammates: engineer standups, team meetings, 1:1, development demos, sprint planning, UX design scrub meetings. </li><li>Experience with WCAG Accessibility Standards 2.2 Level AA. </li><li>Build, debug and maintain scalable, responsive, quality front-end solutions following best practices that function effectively across a variety of platforms and devices. </li><li>HTML5, CSS/SASS (or comparable CSS preprocessor), Bootstrap, JavaScript and/or JS Frameworks such as React.js/Jest. </li><li>On-hands experience with UX Designers. Knowledge of Figma (or alternative) bridging the gap between design and development. Implementing high-fidelity designs transformed into smooth, intuitive, easy-to-use journeys in our user interfaces. </li><li>Curiosity and enthusiasm for leveraging Ai development tools to streamline work and time management. </li><li>Comfortable using Git for version control and Webpack for automation tasks. </li><li>Experience working with design systems and understanding the fundamentals and importance of this ecosystem. </li><li>Testing and debugging at all stages of the SDLC. </li><li>Participate in code review and open-brainstorm sessions. </li><li>Help in facilitation of JIRA scrum tasks following agile methodologies. </li><li>Familiarity working with REST APIs and JSON. <br><br></li></ul><strong>Qualifications</strong>:<br><br><ul><li>5+ years of software engineering experience with a college degree and/or other demonstration of deep expertise in front-end engineering (profile encouraged). </li><li>High competence in verbal and written communication is essential. </li><li>Accountability and self-management to handle several projects at once across multiple code bases (we are a centralized team). </li><li>Expertise in computer science fundamentals. </li><li>Ability to multiply the abilities and skills of others. </li><li>Knowledge and experience implementing client-side security best practices. </li><li>Comfort with critical thinking and creative problem-solving. </li><li>Ability to articulate ideas clearly and collaborate effectively with cross-functional team members, including VPs, POs, PMs, QA, DevOps, Program Management, designers and back-end developers. <br><br></li></ul><strong>Preferred Qualifications:<br><br></strong><ul><li>Knowledge and experience working in data security. </li><li>Scrum master/project management experience. </li><li>Hands on experience working with RESTful APIs to OpenAPI 3.0 specification. </li><li>Enthusiasm to learn and understand natural language processing (NLP) for human-AI interaction. </li><li>Familiarity with cloud services like AWS, Google Cloud, or Azure. </li><li>Familiarity with building Jenkins pipelines and other CI/CD processes. </li><li>Inspection of code quality, scanning, monitoring, and security with Snyk and SonarQube. </li><li>Design and Prototyping. </li><li>Bonus points for working with Docker Containers<br><br></li></ul><strong>Key Personal Attributes:<br><br></strong><ul><li>Exceptional problem-solving skills. </li><li>Detail-oriented with an eye for aesthetics and usability. </li><li>Passion for staying up to date on the latest trends and/or new developments in the UIUX arena. </li><li>Understanding user needs and implementation of solutions. </li><li>Ability to efficiently manage workload and prioritize tasks to meet deadlines. </li><li>Understanding and sharing the feelings of others, fostering a supportive team environment is important. </li><li>Showing Integrity and always maintaining honest and ethical standards. </li><li>Flexibility in schedule working internationally across time zones. </li><li>Organization is top tier, tracking multiple tasks at once and keeping projects running as efficiently as possible and on time. </li><li>Analytical problem solving, able to make bite-size chunks out of complex issues. <br><br></li></ul><strong>Why Choose Protegrity:<br><br></strong><ul><li>Become a member of a leading Data Protection, Privacy and Security company during one of the best market opportunities to come along in a generation. </li><li>Competitive Compensation/Total Reward Packages that include:</li><li>Health Benefits (Health/Dental/Vision). </li><li>Paid Time Off (PTO). </li><li>401K. </li><li>Annual Bonus Incentives. </li><li>Short and Long Term Disability. </li><li>Work on global projects with diverse, energetic, team members who respect each other and celebrate differences. </li><li>Talent First Workforce. <br><br></li></ul><strong>Working Model:<br><br></strong><ul><li>This is a hybrid role and will require you to be in office 2-3 days a week. In certain circumstances the work model may change to accommodate business needs. <br><br></li></ul>Should you accept this position, you will be required to consent to and successfully complete a background investigation. This may include, subject to local laws, verification of extended education and additional criminal and civil checks.<br><br>We offer a competitive salary and comprehensive benefits with generous vacation and holiday time off. All employees are also provided access to ongoing learning &amp; development.<br><br>Ensuring a diverse and inclusive workplace is our priority. We are committed to an environment of acceptance where you are free to bring your full self to work. All qualified applicants and current employees will not be discriminated against on the basis of race, color, religion, sex, sexual orientation, gender identity, age, national origin, disability or veteran status.<br><br>Please reference Section 12: Supplemental Notice for Job Applicants in our Privacy Policy to inform you of the categories of personal information that we collect from individuals who inquire about and/or apply to work for Protegrity USA, Inc., or its parent company, subsidiaries or affiliates, and the purposes for which we use such personal information.<br><br>Compensation Range: $117K - $144K<br><br>
        </div>

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--more
        ml-0.5"" data-tracking-control-name=""public_jobs_show-more-html-btn"" aria-label=""Show more"" aria-expanded=""false"">
<!---->
        
            Show more
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/cyolgscd0imw2ldqppkrb84vo""></icon>
    </button>
  

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--less
        ml-0.5"" data-tracking-control-name=""public_jobs_show-less-html-btn"" aria-label=""Show less"" aria-expanded=""true"">
<!---->
        
            Show less
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/4chtt12k98xwnba1nimld2oyg""></icon>
    </button>
  
<!---->    </section>",,"{""min_amount"":117000,""max_amount"":144000,""currency"":""$"",""payment_period"":""yr""}","This range is provided by TM2 Group, LLC. Your actual pay will be based on your skills and experience — talk with your recruiter to learn more.","2025-07-23T16:58:46.770Z","{""url"":""https://www.linkedin.com/jobs/view/front-end-ui-engineer-734-at-tm2-group-llc-4267770307"",""discovery_input"":{""location"":""CA"",""keyword"":""UIUX"",""country"":""US"",""time_range"":null,""job_type"":null,""experience_level"":null,""remote"":null}}",,,,
"https://www.linkedin.com/jobs/view/front-end-ui-engineer-734-at-protegrity-4267764166?_l=en","4267764166","Front-End UI Engineer 734","Protegrity","15741","San Francisco, CA","At Protegrity, we lead innovation by using AI and quantum-resistant cryptography to transform data protection across cloud-native, hybrid, on-premises, and open source environments. We leverage advanced cryptographic methods such as tokenization, format-preserving encryption, and quantum-resilient techniques to protect sensitive data. As a global leader in data security, our mission is to ensure that data isn’t just valuable but also usable, trusted, and safe. Protegrity offers the opportunity to work at the intersection of innovation and collaboration, with the ability to make a meaningful impact on the industry while working alongside some of the brightest minds. Together, we are redefining how the world safeguards data, enabling organizations to thrive in a GenAI era where data is the ultimate currency. If you're ready to shape the future of data security, Protegrity is the place for you. Protegrity is seeking a passionate and talented Front-End UI Engineer to join our Product Innovation Team. This position is a hybrid position, with a preference for candidates located in Northern California, New York or Stamford Connecticut. Data Privacy is important to all of us. At Protegrity our products make this possible in rest, in motion and in view for some of the largest companies in the world. Protegrity puts our customers in the best possible position to choose how they want to protect their data and make it private. Thanks to the centralized management and transparency of our platform- a “single pane of glass”- teams can see where the data resides and what its purpose is. Unlike other data protection options, the Protegrity data Protection Platform clarifies the state of data so organizations can choose how to protect it and keep it private using the full range of methods from basic monitoring and dynamic data masking to highly secure vaultless tokenization. Since our founding over 10 years ago, we are fully vested in helping clients protect their information. Whether encrypting, tokenizing, or applying privacy methods, our platform secures the data behind the many operational systems that drive the day-to-day functions of business, as well as the analytical systems behind decision-making, personalized customer experiences, and AI modeling. All of this propels businesses forward by fueling innovation and giving clients a competitive advantage over less savvy competitors. This is a once in a lifetime opportunity to be part of a global team with a product that the business marketplace MUST have in place to do business. Responsibilities Work as part of our centralized UI/UX Team, bringing cross company branding and identity to a global level across our security product user interfaces. Daily/weekly collaboration with teammates: engineer standups, team meetings, 1:1, development demos, sprint planning, UX design scrub meetings. Experience with WCAG Accessibility Standards 2.2 Level AA. Build, debug and maintain scalable, responsive, quality front-end solutions following best practices that function effectively across a variety of platforms and devices. HTML5, CSS/SASS (or comparable CSS preprocessor), Bootstrap, JavaScript and/or JS Frameworks such as React.js/Jest. On-hands experience with UX Designers. Knowledge of Figma (or alternative) bridging the gap between design and development. Implementing high-fidelity designs transformed into smooth, intuitive, easy-to-use journeys in our user interfaces. Curiosity and enthusiasm for leveraging Ai development tools to streamline work and time management. Comfortable using Git for version control and Webpack for automation tasks. Experience working with design systems and understanding the fundamentals and importance of this ecosystem. Testing and debugging at all stages of the SDLC. Participate in code review and open-brainstorm sessions. Help in facilitation of JIRA scrum tasks following agile methodologies. Familiarity working with REST APIs and JSON. Qualifications 5+ years of software engineering experience with a college degree and/or other demonstration of deep expertise in front-end engineering (profile encouraged). High competence in verbal and written communication is essential. Accountability and self-management to handle several projects at once across multiple code bases (we are a centralized team). Expertise in computer science fundamentals. Ability to multiply the abilities and skills of others. Knowledge and experience implementing client-side security best practices. Comfort with critical thinking and creative problem-solving. Ability to articulate ideas clearly and collaborate effectively with cross-functional team members, including VPs, POs, PMs, QA, DevOps, Program Management, designers and back-end developers. Preferred Qualifications Knowledge and experience working in data security. Scrum master/project management experience. Hands on experience working with RESTful APIs to OpenAPI 3.0 specification. Enthusiasm to learn and understand natural language processing (NLP) for human-AI interaction. Familiarity with cloud services like AWS, Google Cloud, or Azure. Familiarity with building Jenkins pipelines and other CI/CD processes. Inspection of code quality, scanning, monitoring, and security with Snyk and SonarQube. Design and Prototyping. Bonus points for working with Docker Containers Key Personal Attributes Exceptional problem-solving skills. Detail-oriented with an eye for aesthetics and usability. Passion for staying up to date on the latest trends and/or new developments in the UIUX arena. Understanding user needs and implementation of solutions. Ability to efficiently manage workload and prioritize tasks to meet deadlines. Understanding and sharing the feelings of others, fostering a supportive team environment is important. Showing Integrity and always maintaining honest and ethical standards. Flexibility in schedule working internationally across time zones. Organization is top tier, tracking multiple tasks at once and keeping projects running as efficiently as possible and on time. Analytical problem solving, able to make bite-size chunks out of complex issues. Why Choose Protegrity Become a member of a leading Data Protection, Privacy and Security company during one of the best market opportunities to come along in a generation. Competitive Compensation/Total Reward Packages that include: Health Benefits (Health/Dental/Vision). Paid Time Off (PTO). 401K. Annual Bonus Incentives. Short and Long Term Disability. Work on global projects with diverse, energetic, team members who respect each other and celebrate differences. Talent First Workforce. Working Model This is a hybrid role and will require you to be in office 2-3 days a week. In certain circumstances the work model may change to accommodate business needs. Should you accept this position, you will be required to consent to and successfully complete a background investigation. This may include, subject to local laws, verification of extended education and additional criminal and civil checks. We offer a competitive salary and comprehensive benefits with generous vacation and holiday time off. All employees are also provided access to ongoing learning & development. Ensuring a diverse and inclusive workplace is our priority. We are committed to an environment of acceptance where you are free to bring your full self to work. All qualified applicants and current employees will not be discriminated against on the basis of race, color, religion, sex, sexual orientation, gender identity, age, national origin, disability or veteran status. Please reference Section 12: Supplemental Notice for Job Applicants in our Privacy Policy to inform you of the categories of personal information that we collect from individuals who inquire about and/or apply to work for Protegrity USA, Inc., or its parent company, subsidiaries or affiliates, and the purposes for which we use such personal information. Compensation Range: $117K - $144K Show more Show less",,"Mid-Senior level","Engineering and Information Technology","Full-time","Computer and Network Security","$117,000.00/yr - $144,000.00/yr","https://www.linkedin.com/company/protegrity?trk=public_jobs_topcard-org-name","6 days ago",82,"{""location"":""CA"",""keyword"":""UIUX"",""country"":""US"",""time_range"":"""",""job_type"":"""",""experience_level"":"""",""remote"":"""",""company"":"""",""location_radius"":""""}","https://www.linkedin.com/jobs/view/externalApply/4267764166?url=https%3A%2F%2Fjobs%2Eashbyhq%2Ecom%2Fprotegrity%2Fcf118b10-36cd-460a-a260-bb675e9ce1dc%3Futm_source%3DLinkedin_Applicant&urlHash=igLf","US","8632","https://media.licdn.com/dms/image/v2/D560BAQEjzkD3AIx29w/company-logo_100_100/company-logo_100_100/0/1697749697495/protegrity_logo?e=**********&v=beta&t=QE8SK2VHsmJBImfsVPQk2a_x7rNjtiuK_P9ECf3btic","2025-07-17T16:58:48.070Z","{""name"":null,""title"":null,""url"":null}",true,"<section class=""show-more-less-html"" data-max-lines=""5"">
        <div class=""show-more-less-html__markup show-more-less-html__markup--clamp-after-5
            relative overflow-hidden"">
          At Protegrity, we lead innovation by using AI and quantum-resistant cryptography to transform data protection across cloud-native, hybrid, on-premises, and open source environments. We leverage advanced cryptographic methods such as tokenization, format-preserving encryption, and quantum-resilient techniques to protect sensitive data. As a global leader in data security, our mission is to ensure that data isn&#x2019;t just valuable but also usable, trusted, and safe.<br><br>Protegrity offers the opportunity to work at the intersection of innovation and collaboration, with the ability to make a meaningful impact on the industry while working alongside some of the brightest minds. Together, we are redefining how the world safeguards data, enabling organizations to thrive in a GenAI era where data is the ultimate currency. If you&apos;re ready to shape the future of data security, Protegrity is the place for you.<br><br>Protegrity is seeking a passionate and talented Front-End UI Engineer to join our Product Innovation Team. This position is a hybrid position, with a preference for candidates located in Northern California, New York or Stamford Connecticut.<br><br>Data Privacy is important to all of us. At Protegrity our products make this possible in rest, in motion and in view for some of the largest companies in the world. Protegrity puts our customers in the best possible position to choose how they want to protect their data and make it private. Thanks to the centralized management and transparency of our platform- a &#x201C;single pane of glass&#x201D;- teams can see where the data resides and what its purpose is. Unlike other data protection options, the Protegrity data Protection Platform clarifies the state of data so organizations can choose how to protect it and keep it private using the full range of methods from basic monitoring and dynamic data masking to highly secure vaultless tokenization.<br><br>Since our founding over 10 years ago, we are fully vested in helping clients protect their information. Whether encrypting, tokenizing, or applying privacy methods, our platform secures the data behind the many operational systems that drive the day-to-day functions of business, as well as the analytical systems behind decision-making, personalized customer experiences, and AI modeling. All of this propels businesses forward by fueling innovation and giving clients a competitive advantage over less savvy competitors. This is a once in a lifetime opportunity to be part of a global team with a product that the business marketplace MUST have in place to do business.<br><br><strong>Responsibilities<br><br></strong><ul><li>Work as part of our centralized UI/UX Team, bringing cross company branding and identity to a global level across our security product user interfaces.</li><li>Daily/weekly collaboration with teammates: engineer standups, team meetings, 1:1, development demos, sprint planning, UX design scrub meetings.</li><li>Experience with WCAG Accessibility Standards 2.2 Level AA.</li><li>Build, debug and maintain scalable, responsive, quality front-end solutions following best practices that function effectively across a variety of platforms and devices.</li><li>HTML5, CSS/SASS (or comparable CSS preprocessor), Bootstrap, JavaScript and/or JS Frameworks such as React.js/Jest.</li><li>On-hands experience with UX Designers. Knowledge of Figma (or alternative) bridging the gap between design and development. Implementing high-fidelity designs transformed into smooth, intuitive, easy-to-use journeys in our user interfaces.</li><li>Curiosity and enthusiasm for leveraging Ai development tools to streamline work and time management.</li><li>Comfortable using Git for version control and Webpack for automation tasks.</li><li>Experience working with design systems and understanding the fundamentals and importance of this ecosystem.</li><li>Testing and debugging at all stages of the SDLC.</li><li>Participate in code review and open-brainstorm sessions.</li><li>Help in facilitation of JIRA scrum tasks following agile methodologies.</li><li>Familiarity working with REST APIs and JSON.<br><br></li></ul><strong>Qualifications<br><br></strong><ul><li>5+ years of software engineering experience with a college degree and/or other demonstration of deep expertise in front-end engineering (profile encouraged).</li><li>High competence in verbal and written communication is essential.</li><li>Accountability and self-management to handle several projects at once across multiple code bases (we are a centralized team).</li><li>Expertise in computer science fundamentals.</li><li>Ability to multiply the abilities and skills of others.</li><li>Knowledge and experience implementing client-side security best practices.</li><li>Comfort with critical thinking and creative problem-solving.</li><li>Ability to articulate ideas clearly and collaborate effectively with cross-functional team members, including VPs, POs, PMs, QA, DevOps, Program Management, designers and back-end developers.<br><br></li></ul><strong>Preferred Qualifications<br><br></strong><ul><li>Knowledge and experience working in data security.</li><li>Scrum master/project management experience.</li><li>Hands on experience working with RESTful APIs to OpenAPI 3.0 specification.</li><li>Enthusiasm to learn and understand natural language processing (NLP) for human-AI interaction.</li><li>Familiarity with cloud services like AWS, Google Cloud, or Azure.</li><li>Familiarity with building Jenkins pipelines and other CI/CD processes.</li><li>Inspection of code quality, scanning, monitoring, and security with Snyk and SonarQube.</li><li>Design and Prototyping.</li><li>Bonus points for working with Docker Containers<br><br></li></ul><strong>Key Personal Attributes<br><br></strong><ul><li>Exceptional problem-solving skills.</li><li>Detail-oriented with an eye for aesthetics and usability.</li><li>Passion for staying up to date on the latest trends and/or new developments in the UIUX arena.</li><li>Understanding user needs and implementation of solutions.</li><li>Ability to efficiently manage workload and prioritize tasks to meet deadlines.</li><li>Understanding and sharing the feelings of others, fostering a supportive team environment is important.</li><li>Showing Integrity and always maintaining honest and ethical standards.</li><li>Flexibility in schedule working internationally across time zones.</li><li>Organization is top tier, tracking multiple tasks at once and keeping projects running as efficiently as possible and on time.</li><li>Analytical problem solving, able to make bite-size chunks out of complex issues.<br><br></li></ul><strong>Why Choose Protegrity<br><br></strong><ul><li>Become a member of a leading Data Protection, Privacy and Security company during one of the best market opportunities to come along in a generation.</li><li>Competitive Compensation/Total Reward Packages that include:</li><li>Health Benefits (Health/Dental/Vision).</li><li>Paid Time Off (PTO).</li><li>401K.</li><li>Annual Bonus Incentives.</li><li>Short and Long Term Disability.</li><li>Work on global projects with diverse, energetic, team members who respect each other and celebrate differences.</li><li>Talent First Workforce.<br><br></li></ul><strong>Working Model<br><br></strong><ul><li>This is a hybrid role and will require you to be in office 2-3 days a week. In certain circumstances the work model may change to accommodate business needs.<br><br></li></ul>Should you accept this position, you will be required to consent to and successfully complete a background investigation. This may include, subject to local laws, verification of extended education and additional criminal and civil checks.<br><br>We offer a competitive salary and comprehensive benefits with generous vacation and holiday time off. All employees are also provided access to ongoing learning &amp; development.<br><br>Ensuring a diverse and inclusive workplace is our priority. We are committed to an environment of acceptance where you are free to bring your full self to work. All qualified applicants and current employees will not be discriminated against on the basis of race, color, religion, sex, sexual orientation, gender identity, age, national origin, disability or veteran status.<br><br>Please reference Section 12: Supplemental Notice for Job Applicants in our Privacy Policy to inform you of the categories of personal information that we collect from individuals who inquire about and/or apply to work for Protegrity USA, Inc., or its parent company, subsidiaries or affiliates, and the purposes for which we use such personal information.<br><br>Compensation Range: $117K - $144K
        </div>

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--more
        ml-0.5"" data-tracking-control-name=""public_jobs_show-more-html-btn"" aria-label=""Show more"" aria-expanded=""false"">
<!---->
        
            Show more
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/cyolgscd0imw2ldqppkrb84vo""></icon>
    </button>
  

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--less
        ml-0.5"" data-tracking-control-name=""public_jobs_show-less-html-btn"" aria-label=""Show less"" aria-expanded=""true"">
<!---->
        
            Show less
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/4chtt12k98xwnba1nimld2oyg""></icon>
    </button>
  
<!---->    </section>",,"{""min_amount"":117000,""max_amount"":144000,""currency"":""$"",""payment_period"":""yr""}","Retrieved from the description.","2025-07-23T16:58:48.098Z","{""url"":""https://www.linkedin.com/jobs/view/front-end-ui-engineer-734-at-protegrity-4267764166"",""discovery_input"":{""location"":""CA"",""keyword"":""UIUX"",""country"":""US"",""time_range"":null,""job_type"":null,""experience_level"":null,""remote"":null}}",,,,
"https://www.linkedin.com/jobs/view/front-end-ui-engineer-734-at-tm2-group-llc-4267769395?_l=en","4267769395","Front-End UI Engineer 734","TM2 Group, LLC","42394501","San Francisco, CA","At Protegrity, we lead innovation by using AI and quantum-resistant cryptography to transform data protection across cloud-native, hybrid, on-premises, and open source environments. We leverage advanced cryptographic methods such as tokenization, format-preserving encryption, and quantum-resilient techniques to protect sensitive data. As a global leader in data security, our mission is to ensure that data isn’t just valuable but also usable, trusted, and safe. Protegrity offers the opportunity to work at the intersection of innovation and collaboration, with the ability to make a meaningful impact on the industry while working alongside some of the brightest minds. Together, we are redefining how the world safeguards data, enabling organizations to thrive in a GenAI era where data is the ultimate currency. If you're ready to shape the future of data security, Protegrity is the place for you. Protegrity is seeking a passionate and talented Front-End UI Engineer to join our Product Innovation Team. This position is a hybrid position, with a preference for candidates located in Northern California, New York or Stamford Connecticut. Data Privacy is important to all of us. At Protegrity our products make this possible in rest, in motion and in view for some of the largest companies in the world. Protegrity puts our customers in the best possible position to choose how they want to protect their data and make it private. Thanks to the centralized management and transparency of our platform- a “single pane of glass”- teams can see where the data resides and what its purpose is. Unlike other data protection options, the Protegrity data Protection Platform clarifies the state of data so organizations can choose how to protect it and keep it private using the full range of methods from basic monitoring and dynamic data masking to highly secure vaultless tokenization. Since our founding over 10 years ago, we are fully vested in helping clients protect their information. Whether encrypting, tokenizing, or applying privacy methods, our platform secures the data behind the many operational systems that drive the day-to-day functions of business, as well as the analytical systems behind decision-making, personalized customer experiences, and AI modeling. All of this propels businesses forward by fueling innovation and giving clients a competitive advantage over less savvy competitors. This is a once in a lifetime opportunity to be part of a global team with a product that the business marketplace MUST have in place to do business. Responsibilities: Work as part of our centralized UI/UX Team, bringing cross company branding and identity to a global level across our security product user interfaces. Daily/weekly collaboration with teammates: engineer standups, team meetings, 1:1, development demos, sprint planning, UX design scrub meetings. Experience with WCAG Accessibility Standards 2.2 Level AA. Build, debug and maintain scalable, responsive, quality front-end solutions following best practices that function effectively across a variety of platforms and devices. HTML5, CSS/SASS (or comparable CSS preprocessor), Bootstrap, JavaScript and/or JS Frameworks such as React.js/Jest. On-hands experience with UX Designers. Knowledge of Figma (or alternative) bridging the gap between design and development. Implementing high-fidelity designs transformed into smooth, intuitive, easy-to-use journeys in our user interfaces. Curiosity and enthusiasm for leveraging Ai development tools to streamline work and time management. Comfortable using Git for version control and Webpack for automation tasks. Experience working with design systems and understanding the fundamentals and importance of this ecosystem. Testing and debugging at all stages of the SDLC. Participate in code review and open-brainstorm sessions. Help in facilitation of JIRA scrum tasks following agile methodologies. Familiarity working with REST APIs and JSON. Qualifications : 5+ years of software engineering experience with a college degree and/or other demonstration of deep expertise in front-end engineering (profile encouraged). High competence in verbal and written communication is essential. Accountability and self-management to handle several projects at once across multiple code bases (we are a centralized team). Expertise in computer science fundamentals. Ability to multiply the abilities and skills of others. Knowledge and experience implementing client-side security best practices. Comfort with critical thinking and creative problem-solving. Ability to articulate ideas clearly and collaborate effectively with cross-functional team members, including VPs, POs, PMs, QA, DevOps, Program Management, designers and back-end developers. Preferred Qualifications: Knowledge and experience working in data security. Scrum master/project management experience. Hands on experience working with RESTful APIs to OpenAPI 3.0 specification. Enthusiasm to learn and understand natural language processing (NLP) for human-AI interaction. Familiarity with cloud services like AWS, Google Cloud, or Azure. Familiarity with building Jenkins pipelines and other CI/CD processes. Inspection of code quality, scanning, monitoring, and security with Snyk and SonarQube. Design and Prototyping. Bonus points for working with Docker Containers Key Personal Attributes: Exceptional problem-solving skills. Detail-oriented with an eye for aesthetics and usability. Passion for staying up to date on the latest trends and/or new developments in the UIUX arena. Understanding user needs and implementation of solutions. Ability to efficiently manage workload and prioritize tasks to meet deadlines. Understanding and sharing the feelings of others, fostering a supportive team environment is important. Showing Integrity and always maintaining honest and ethical standards. Flexibility in schedule working internationally across time zones. Organization is top tier, tracking multiple tasks at once and keeping projects running as efficiently as possible and on time. Analytical problem solving, able to make bite-size chunks out of complex issues. Why Choose Protegrity: Become a member of a leading Data Protection, Privacy and Security company during one of the best market opportunities to come along in a generation. Competitive Compensation/Total Reward Packages that include: Health Benefits (Health/Dental/Vision). Paid Time Off (PTO). 401K. Annual Bonus Incentives. Short and Long Term Disability. Work on global projects with diverse, energetic, team members who respect each other and celebrate differences. Talent First Workforce. Working Model: This is a hybrid role and will require you to be in office 2-3 days a week. In certain circumstances the work model may change to accommodate business needs. Should you accept this position, you will be required to consent to and successfully complete a background investigation. This may include, subject to local laws, verification of extended education and additional criminal and civil checks. We offer a competitive salary and comprehensive benefits with generous vacation and holiday time off. All employees are also provided access to ongoing learning & development. Ensuring a diverse and inclusive workplace is our priority. We are committed to an environment of acceptance where you are free to bring your full self to work. All qualified applicants and current employees will not be discriminated against on the basis of race, color, religion, sex, sexual orientation, gender identity, age, national origin, disability or veteran status. Please reference Section 12: Supplemental Notice for Job Applicants in our Privacy Policy to inform you of the categories of personal information that we collect from individuals who inquire about and/or apply to work for Protegrity USA, Inc., or its parent company, subsidiaries or affiliates, and the purposes for which we use such personal information. Compensation Range: $117K - $144K Show more Show less",,"Mid-Senior level","Engineering and Information Technology","Full-time","Business Consulting and Services","$117,000.00/yr - $144,000.00/yr","https://www.linkedin.com/company/tm2-group-llc?trk=public_jobs_topcard-org-name","6 days ago",29,"{""location"":""CA"",""keyword"":""UIUX"",""country"":""US"",""time_range"":"""",""job_type"":"""",""experience_level"":"""",""remote"":"""",""company"":"""",""location_radius"":""""}","https://www.linkedin.com/jobs/view/externalApply/4267769395?url=https%3A%2F%2Fjobs%2Eashbyhq%2Ecom%2Fprotegrity%2Fcf118b10-36cd-460a-a260-bb675e9ce1dc%2Fapplication%3Futm_source%3Dl7522D97Kr&urlHash=o0sM","US","8632","https://media.licdn.com/dms/image/v2/C4D0BAQHv63NXbANpTA/company-logo_100_100/company-logo_100_100/0/1630569237936?e=**********&v=beta&t=4-hSetAXxA92cKBDLkgNXP2jCKJdzAwcsdiju57i51o","2025-07-17T16:58:53.217Z","{""name"":null,""title"":null,""url"":null}",true,"<section class=""show-more-less-html"" data-max-lines=""5"">
        <div class=""show-more-less-html__markup show-more-less-html__markup--clamp-after-5
            relative overflow-hidden"">
          At Protegrity, we lead innovation by using AI and quantum-resistant cryptography to transform data protection across cloud-native, hybrid, on-premises, and open source environments. We leverage advanced cryptographic methods such as tokenization, format-preserving encryption, and quantum-resilient techniques to protect sensitive data. As a global leader in data security, our mission is to ensure that data isn&#x2019;t just valuable but also usable, trusted, and safe.<br><br>Protegrity offers the opportunity to work at the intersection of innovation and collaboration, with the ability to make a meaningful impact on the industry while working alongside some of the brightest minds. Together, we are redefining how the world safeguards data, enabling organizations to thrive in a GenAI era where data is the ultimate currency. If you&apos;re ready to shape the future of data security, Protegrity is the place for you.<br><br>Protegrity is seeking a passionate and talented Front-End UI Engineer to join our Product Innovation Team. This position is a hybrid position, with a preference for candidates located in Northern California, New York or Stamford Connecticut.<br><br>Data Privacy is important to all of us. At Protegrity our products make this possible in rest, in motion and in view for some of the largest companies in the world. Protegrity puts our customers in the best possible position to choose how they want to protect their data and make it private. Thanks to the centralized management and transparency of our platform- a &#x201C;single pane of glass&#x201D;- teams can see where the data resides and what its purpose is. Unlike other data protection options, the Protegrity data Protection Platform clarifies the state of data so organizations can choose how to protect it and keep it private using the full range of methods from basic monitoring and dynamic data masking to highly secure vaultless tokenization.<br><br>Since our founding over 10 years ago, we are fully vested in helping clients protect their information. Whether encrypting, tokenizing, or applying privacy methods, our platform secures the data behind the many operational systems that drive the day-to-day functions of business, as well as the analytical systems behind decision-making, personalized customer experiences, and AI modeling. All of this propels businesses forward by fueling innovation and giving clients a competitive advantage over less savvy competitors. This is a once in a lifetime opportunity to be part of a global team with a product that the business marketplace MUST have in place to do business.<br><br><strong>Responsibilities:<br><br></strong><ul><li>Work as part of our centralized UI/UX Team, bringing cross company branding and identity to a global level across our security product user interfaces. </li><li>Daily/weekly collaboration with teammates: engineer standups, team meetings, 1:1, development demos, sprint planning, UX design scrub meetings. </li><li>Experience with WCAG Accessibility Standards 2.2 Level AA. </li><li>Build, debug and maintain scalable, responsive, quality front-end solutions following best practices that function effectively across a variety of platforms and devices. </li><li>HTML5, CSS/SASS (or comparable CSS preprocessor), Bootstrap, JavaScript and/or JS Frameworks such as React.js/Jest. </li><li>On-hands experience with UX Designers. Knowledge of Figma (or alternative) bridging the gap between design and development. Implementing high-fidelity designs transformed into smooth, intuitive, easy-to-use journeys in our user interfaces. </li><li>Curiosity and enthusiasm for leveraging Ai development tools to streamline work and time management. </li><li>Comfortable using Git for version control and Webpack for automation tasks. </li><li>Experience working with design systems and understanding the fundamentals and importance of this ecosystem. </li><li>Testing and debugging at all stages of the SDLC. </li><li>Participate in code review and open-brainstorm sessions. </li><li>Help in facilitation of JIRA scrum tasks following agile methodologies. </li><li>Familiarity working with REST APIs and JSON. <br><br></li></ul><strong>Qualifications</strong>:<br><br><ul><li>5+ years of software engineering experience with a college degree and/or other demonstration of deep expertise in front-end engineering (profile encouraged). </li><li>High competence in verbal and written communication is essential. </li><li>Accountability and self-management to handle several projects at once across multiple code bases (we are a centralized team). </li><li>Expertise in computer science fundamentals. </li><li>Ability to multiply the abilities and skills of others. </li><li>Knowledge and experience implementing client-side security best practices. </li><li>Comfort with critical thinking and creative problem-solving. </li><li>Ability to articulate ideas clearly and collaborate effectively with cross-functional team members, including VPs, POs, PMs, QA, DevOps, Program Management, designers and back-end developers. <br><br></li></ul><strong>Preferred Qualifications:<br><br></strong><ul><li>Knowledge and experience working in data security. </li><li>Scrum master/project management experience. </li><li>Hands on experience working with RESTful APIs to OpenAPI 3.0 specification. </li><li>Enthusiasm to learn and understand natural language processing (NLP) for human-AI interaction. </li><li>Familiarity with cloud services like AWS, Google Cloud, or Azure. </li><li>Familiarity with building Jenkins pipelines and other CI/CD processes. </li><li>Inspection of code quality, scanning, monitoring, and security with Snyk and SonarQube. </li><li>Design and Prototyping. </li><li>Bonus points for working with Docker Containers<br><br></li></ul><strong>Key Personal Attributes:<br><br></strong><ul><li>Exceptional problem-solving skills. </li><li>Detail-oriented with an eye for aesthetics and usability. </li><li>Passion for staying up to date on the latest trends and/or new developments in the UIUX arena. </li><li>Understanding user needs and implementation of solutions. </li><li>Ability to efficiently manage workload and prioritize tasks to meet deadlines. </li><li>Understanding and sharing the feelings of others, fostering a supportive team environment is important. </li><li>Showing Integrity and always maintaining honest and ethical standards. </li><li>Flexibility in schedule working internationally across time zones. </li><li>Organization is top tier, tracking multiple tasks at once and keeping projects running as efficiently as possible and on time. </li><li>Analytical problem solving, able to make bite-size chunks out of complex issues. <br><br></li></ul><strong>Why Choose Protegrity:<br><br></strong><ul><li>Become a member of a leading Data Protection, Privacy and Security company during one of the best market opportunities to come along in a generation. </li><li>Competitive Compensation/Total Reward Packages that include:</li><li>Health Benefits (Health/Dental/Vision). </li><li>Paid Time Off (PTO). </li><li>401K. </li><li>Annual Bonus Incentives. </li><li>Short and Long Term Disability. </li><li>Work on global projects with diverse, energetic, team members who respect each other and celebrate differences. </li><li>Talent First Workforce. <br><br></li></ul><strong>Working Model:<br><br></strong><ul><li>This is a hybrid role and will require you to be in office 2-3 days a week. In certain circumstances the work model may change to accommodate business needs. <br><br></li></ul>Should you accept this position, you will be required to consent to and successfully complete a background investigation. This may include, subject to local laws, verification of extended education and additional criminal and civil checks.<br><br>We offer a competitive salary and comprehensive benefits with generous vacation and holiday time off. All employees are also provided access to ongoing learning &amp; development.<br><br>Ensuring a diverse and inclusive workplace is our priority. We are committed to an environment of acceptance where you are free to bring your full self to work. All qualified applicants and current employees will not be discriminated against on the basis of race, color, religion, sex, sexual orientation, gender identity, age, national origin, disability or veteran status.<br><br>Please reference Section 12: Supplemental Notice for Job Applicants in our Privacy Policy to inform you of the categories of personal information that we collect from individuals who inquire about and/or apply to work for Protegrity USA, Inc., or its parent company, subsidiaries or affiliates, and the purposes for which we use such personal information.<br><br>Compensation Range: $117K - $144K<br><br>
        </div>

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--more
        ml-0.5"" data-tracking-control-name=""public_jobs_show-more-html-btn"" aria-label=""Show more"" aria-expanded=""false"">
<!---->
        
            Show more
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/cyolgscd0imw2ldqppkrb84vo""></icon>
    </button>
  

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--less
        ml-0.5"" data-tracking-control-name=""public_jobs_show-less-html-btn"" aria-label=""Show less"" aria-expanded=""true"">
<!---->
        
            Show less
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/4chtt12k98xwnba1nimld2oyg""></icon>
    </button>
  
<!---->    </section>",,"{""min_amount"":117000,""max_amount"":144000,""currency"":""$"",""payment_period"":""yr""}","This range is provided by TM2 Group, LLC. Your actual pay will be based on your skills and experience — talk with your recruiter to learn more.","2025-07-23T16:58:53.243Z","{""url"":""https://www.linkedin.com/jobs/view/front-end-ui-engineer-734-at-tm2-group-llc-4267769395"",""discovery_input"":{""location"":""CA"",""keyword"":""UIUX"",""country"":""US"",""time_range"":null,""job_type"":null,""experience_level"":null,""remote"":null}}",,,,
"https://www.linkedin.com/jobs/view/senior-ui-engineer-at-turtle-rock-studios-4257217563?_l=en","4257217563","Senior UI Engineer","Turtle Rock Studios","86572","Irvine, CA","At Turtle Rock, we make the games we want to play. We're dedicated to providing a collaborative environment where our team members feel empowered with the creative freedom to contribute and grow. While we are based in Orange County, CA, we support flexible work arrangements ranging from in-studio, hybrid, to fully remote throughout North America. Join our award-winning game development team and help create our next AAA title. We’re looking for a Senior UI Engineer to join our team and play a key role in delivering world-class interfaces that elevate gameplay. As the Senior UI Engineer, you will work closely with the UIUX team and take key UI feature ownership to ensure our games offer seamless user experiences. This role combines technical expertise, requiring a strong sense of design and the ability to inspire and guide your team. Responsibilities Tool Development: Utilize and extend UI tools, seamlessly integrating them into the game engine. Collaboration: Partner with UI artists to integrate art assets and data into user interfaces effectively. Localization: Address localization challenges, including managing in-game text, fonts, textures, and voiceover assets. Feature Ownership: Take technical ownership of key UI features and screens, ensuring quality and functionality. Core Engine Work: Dive into core engine code when needed to support and enhance UI systems. Optimization: Profile and optimize UI systems for maximum performance. Requirements Expertise in C++ programming Experience with UMG in Unreal Engine 4 or 5 Demonstrated experience with event-driven UI systems Proven ability to communicate and collaborate effectively in a team environment Passionate gamer with a dedication to creating exceptional gaming experiences Preferences Prior experience shipping a PC game with multiple input options Previous work on multiplayer titles Bachelor’s degree in Computer Science, Engineering, or equivalent experience Benefits Some of the benefits and perks our employees get to enjoy, include: 100% Medical, Dental, Vision Coverage for you and your family 401k Matching Student Loan Repayment Plan / College Savings Plan Career Improvement Plan Flexible Work Arrangements - In-House, Hybrid, and Remote Generous Time Off Policy Company Events - In-Person + Virtual Events Fully Stocked Kitchen + Sponsored Food Trucks The estimated base pay range for this role is: $136,000 - $170,000 USD We are an equal opportunity employer and value diversity at our company. We do not discriminate on the basis of race, religion, color, national origin, gender, sexual orientation, age, marital status, veteran status, or disability status. Show more Show less",,"Mid-Senior level","Engineering and Information Technology","Full-time","Computer Games","$136,000.00/yr - $170,000.00/yr","https://www.linkedin.com/company/turtlerockstudios?trk=public_jobs_topcard-org-name","4 weeks ago",90,"{""location"":""CA"",""keyword"":""UIUX"",""country"":""US"",""time_range"":"""",""job_type"":"""",""experience_level"":"""",""remote"":"""",""company"":"""",""location_radius"":""""}","https://www.linkedin.com/jobs/view/externalApply/4257217563?url=https%3A%2F%2Fjob-boards%2Egreenhouse%2Eio%2Fturtlerockstudios%2Fjobs%2F4578743005%3Fgh_src%3D0v68py&urlHash=v4Is","US","18703","https://media.licdn.com/dms/image/v2/C4D0BAQGxbc6T1bjkLw/company-logo_100_100/company-logo_100_100/0/1631316063135?e=**********&v=beta&t=5W-5l2-HjoQDFx4pFrabnc7iBsVEqmfz8A2G1qUJOaw","2025-06-25T16:58:59.247Z","{""name"":null,""title"":null,""url"":null}",true,"<section class=""show-more-less-html"" data-max-lines=""5"">
        <div class=""show-more-less-html__markup show-more-less-html__markup--clamp-after-5
            relative overflow-hidden"">
          At Turtle Rock, we make the games we want to play. We&apos;re dedicated to providing a collaborative environment where our team members feel empowered with the creative freedom to contribute and grow. While we are based in Orange County, CA, we support flexible work arrangements ranging from in-studio, hybrid, to fully remote throughout North America. Join our award-winning game development team and help create our next AAA title.<br><br>We&#x2019;re looking for a <strong>Senior UI Engineer</strong> to join our team and play a key role in delivering world-class interfaces that elevate gameplay. As the Senior UI Engineer, you will work closely with the UIUX team and take key UI feature ownership to ensure our games offer seamless user experiences. This role combines technical expertise, requiring a strong sense of design and the ability to inspire and guide your team.<br><br><strong>Responsibilities<br><br></strong><ul><li>Tool Development: Utilize and extend UI tools, seamlessly integrating them into the game engine. </li><li>Collaboration: Partner with UI artists to integrate art assets and data into user interfaces effectively. </li><li>Localization: Address localization challenges, including managing in-game text, fonts, textures, and voiceover assets. </li><li>Feature Ownership: Take technical ownership of key UI features and screens, ensuring quality and functionality. </li><li>Core Engine Work: Dive into core engine code when needed to support and enhance UI systems. </li><li>Optimization: Profile and optimize UI systems for maximum performance. <br><br></li></ul><strong>Requirements<br><br></strong><ul><li>Expertise in C++ programming</li><li>Experience with UMG in Unreal Engine 4 or 5</li><li>Demonstrated experience with event-driven UI systems</li><li>Proven ability to communicate and collaborate effectively in a team environment</li><li>Passionate gamer with a dedication to creating exceptional gaming experiences<br><br></li></ul><strong>Preferences<br><br></strong><ul><li>Prior experience shipping a PC game with multiple input options</li><li>Previous work on multiplayer titles</li><li>Bachelor&#x2019;s degree in Computer Science, Engineering, or equivalent experience<br><br></li></ul><strong>Benefits<br><br></strong>Some of the benefits and perks our employees get to enjoy, include:<br><br><ul><li>100% Medical, Dental, Vision Coverage for you and your family</li><li>401k Matching</li><li>Student Loan Repayment Plan / College Savings Plan</li><li>Career Improvement Plan</li><li>Flexible Work Arrangements - In-House, Hybrid, and Remote</li><li>Generous Time Off Policy</li><li>Company Events - In-Person + Virtual Events</li><li>Fully Stocked Kitchen + Sponsored Food Trucks<br><br></li></ul>The estimated base pay range for this role is: $136,000 - $170,000 USD<br><br><em>We are an equal opportunity employer and value diversity at our company. We do not discriminate on the basis of race, religion, color, national origin, gender, sexual orientation, age, marital status, veteran status, or disability status.</em>
        </div>

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--more
        ml-0.5"" data-tracking-control-name=""public_jobs_show-more-html-btn"" aria-label=""Show more"" aria-expanded=""false"">
<!---->
        
            Show more
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/cyolgscd0imw2ldqppkrb84vo""></icon>
    </button>
  

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--less
        ml-0.5"" data-tracking-control-name=""public_jobs_show-less-html-btn"" aria-label=""Show less"" aria-expanded=""true"">
<!---->
        
            Show less
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/4chtt12k98xwnba1nimld2oyg""></icon>
    </button>
  
<!---->    </section>",,"{""min_amount"":136000,""max_amount"":170000,""currency"":""$"",""payment_period"":""yr""}","Retrieved from the description.","2025-07-23T16:58:59.270Z","{""url"":""https://www.linkedin.com/jobs/view/senior-ui-engineer-at-turtle-rock-studios-4257217563"",""discovery_input"":{""location"":""CA"",""keyword"":""UIUX"",""country"":""US"",""time_range"":null,""job_type"":null,""experience_level"":null,""remote"":null}}",,,,
"https://www.linkedin.com/jobs/view/frontend-engineer-%E2%80%93-data-visualization-performance-at-meet-life-sciences-4254892824?_l=en","4254892824","Frontend Engineer – Data Visualization & Performance","Meet Life Sciences","649827","San Francisco Bay Area","Summary: Join a mission-driven team building powerful, interactive tools that help scientists visualize and interpret data. You'll work on the cutting edge of frontend development using D3.js, WebAssembly (WASM), and GPU shaders to create performant, intuitive scientific tools. Responsibilities: • Design and build web applications that visualize DNA sequencing results • Develop rich, interactive visualizations using D3.js or similar frameworks • Integrate WebAssembly (WASM) modules to optimize performance for large datasets • Use GPU shaders or WebGL to enable real-time rendering of genomic data • Collaborate with scientists and stakeholders to translate workflows into user-friendly tools • Own projects from prototyping through deployment and iteration • Write and maintain unit, integration, and end-to-end tests • Contribute to CI/CD pipelines and continuous product improvement • Participate in code reviews, architecture discussions, and roadmap planning Qualifications: • 6+ years of professional experience in frontend development • Proficiency with JavaScript and a modern frontend framework (e.g., React, Svelte, Vue) • Hands-on experience with D3.js for building complex data visualizations • Familiarity with WebAssembly (WASM) and browser performance optimization • Experience with GPU shaders or WebGL in a production environment • Understanding of RESTful APIs and microservices • Experience working with CI/CD pipelines and test frameworks • Comfortable working in full-stack Python environments (Flask or Django) • Experience with SQL and data modeling • Background in visualizing scientific or medical datasets Show more Show less",,"Mid-Senior level","Engineering","Full-time","Biotechnology Research","$175,000.00/yr - $190,000.00/yr","https://uk.linkedin.com/company/meet?trk=public_jobs_topcard-org-name","3 weeks ago",183,"{""location"":""CA"",""keyword"":""UIUX"",""country"":""US"",""time_range"":"""",""job_type"":"""",""experience_level"":"""",""remote"":"""",""company"":"""",""location_radius"":""""}",,"US","3172","https://media.licdn.com/dms/image/v2/D4E0BAQGuOj9f7VZ_BQ/company-logo_100_100/company-logo_100_100/0/1704115888406/meet_logo?e=**********&v=beta&t=0Af7sSSp8-vtUBIcIGlumWKZg4wdqMx1Pukhws0U9uk","2025-07-02T16:59:03.637Z","{""name"":""Cristina Mansir"",""title"":""Recruiter - Medical Device, Biotech, & Pharma"",""url"":""https://www.linkedin.com/in/cristina-mansir-704b9a215""}",true,"<section class=""show-more-less-html"" data-max-lines=""5"">
        <div class=""show-more-less-html__markup show-more-less-html__markup--clamp-after-5
            relative overflow-hidden"">
          <p><strong>Summary:</strong></p><p> Join a mission-driven team building powerful, interactive tools that help scientists visualize and interpret data. You&apos;ll work on the cutting edge of frontend development using D3.js, WebAssembly (WASM), and GPU shaders to create performant, intuitive scientific tools.</p><p><br></p><p><strong>Responsibilities:</strong></p><p> &#x2022; Design and build web applications that visualize DNA sequencing results</p><p> &#x2022; Develop rich, interactive visualizations using D3.js or similar frameworks</p><p> &#x2022; Integrate WebAssembly (WASM) modules to optimize performance for large datasets</p><p> &#x2022; Use GPU shaders or WebGL to enable real-time rendering of genomic data</p><p> &#x2022; Collaborate with scientists and stakeholders to translate workflows into user-friendly tools</p><p> &#x2022; Own projects from prototyping through deployment and iteration</p><p> &#x2022; Write and maintain unit, integration, and end-to-end tests</p><p> &#x2022; Contribute to CI/CD pipelines and continuous product improvement</p><p> &#x2022; Participate in code reviews, architecture discussions, and roadmap planning</p><p><br></p><p><strong>Qualifications:</strong></p><p> &#x2022; 6+ years of professional experience in frontend development</p><p>&#x2022; Proficiency with JavaScript and a modern frontend framework (e.g., React, Svelte, Vue)</p><p> &#x2022; Hands-on experience with D3.js for building complex data visualizations</p><p> &#x2022; Familiarity with WebAssembly (WASM) and browser performance optimization</p><p> &#x2022; Experience with GPU shaders or WebGL in a production environment</p><p> &#x2022; Understanding of RESTful APIs and microservices</p><p> &#x2022; Experience working with CI/CD pipelines and test frameworks</p><p> &#x2022; Comfortable working in full-stack Python environments (Flask or Django)</p><p> &#x2022; Experience with SQL and data modeling</p><p> &#x2022; Background in visualizing scientific or medical datasets</p>
        </div>

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--more
        ml-0.5"" data-tracking-control-name=""public_jobs_show-more-html-btn"" aria-label=""Show more"" aria-expanded=""false"">
<!---->
        
            Show more
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/cyolgscd0imw2ldqppkrb84vo""></icon>
    </button>
  

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--less
        ml-0.5"" data-tracking-control-name=""public_jobs_show-less-html-btn"" aria-label=""Show less"" aria-expanded=""true"">
<!---->
        
            Show less
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/4chtt12k98xwnba1nimld2oyg""></icon>
    </button>
  
<!---->    </section>",,"{""min_amount"":175000,""max_amount"":190000,""currency"":""$"",""payment_period"":""yr""}","This range is provided by Meet Life Sciences. Your actual pay will be based on your skills and experience — talk with your recruiter to learn more.","2025-07-23T16:59:03.668Z","{""url"":""https://www.linkedin.com/jobs/view/frontend-engineer-%E2%80%93-data-visualization-performance-at-meet-life-sciences-4254892824"",""discovery_input"":{""location"":""CA"",""keyword"":""UIUX"",""country"":""US"",""time_range"":null,""job_type"":null,""experience_level"":null,""remote"":null}}",,,,
"https://www.linkedin.com/jobs/view/senior-software-engineer-at-key-talent-solutions-4271443888?_l=en","4271443888","Senior Software Engineer","Key Talent Solutions","89194920","California, United States","Software Engineer – TS/SCI with CI Polygraph | Agile | Java | Secure Systems Locations: San Diego, CA | Aurora, CO | Annapolis, MD | Alexandria, VA Are you an experienced Software Engineer looking to make a real-world impact on national security? Join a high-performing agile team delivering critical capabilities for a mission-focused government program. Our client, a long-standing leader in enterprise security, cloud computing, and cyber defense, has been supporting federal and private sector programs with advanced technology solutions since 1996. With deep expertise in software development, systems integration, and secure engineering, they are expanding their talented team. Role Overview You’ll work on a nationally significant program using the Scaled Agile Framework (SAFe), DevOps best practices, and modern software development approaches including automation and Behavior Driven Development (BDD). You’ll contribute to delivering secure, scalable solutions in a dynamic development environment. Key Responsibilities Develop software both independently and within a collaborative agile team Analyse complex system requirements and use formal design methodologies (e.g., CASE tools, data flow diagrams) Write secure, maintainable, and efficient code Support shared development practices, process improvements, and code reviews Create and document automation scripts for production deployment Required Qualifications Active TS/SCI with CI Polygraph clearance Bachelor’s degree in Computer Science or related field (or equivalent experience) Strong Java development experience Experience with one or more of the following: Python, C/C++, SQL, SOAP, PowerShell, Eclipse, Oracle, Jenkins, Postgres Familiarity with UI development, XML/XSLT, and Windows or Linux environments Solid understanding of secure development principles and testable code Preferred Experience - Nice to have Background in JEE, Tomcat, WebLogic, JBoss, and scripting languages Experience in secure systems engineering, CI/CD pipelines, and interface control Familiarity with Agile practices like BDD, TDD, refactoring, and pair programming Hands-on experience with Selenium, Cucumber, Mockito, UFT, or FITNesse SAFe Agile experience or certification Knowledge of Layer 7 policy configuration and secure software development best practices If you're ready to apply your skills on projects that matter, we want to hear from you. This is a chance to join a tech-driven organization solving critical challenges with a strong mission focus. Show more Show less",,"Mid-Senior level","Project Management","Full-time","Security and Investigations, Computer and Network Security, and IT Services and IT Consulting",,"https://www.linkedin.com/company/key-talent-solutions1?trk=public_jobs_topcard-org-name","1 day ago",122,"{""location"":""CA"",""keyword"":""UIUX"",""country"":""US"",""time_range"":"""",""job_type"":"""",""experience_level"":"""",""remote"":"""",""company"":"""",""location_radius"":""""}",,"US","9","https://media.licdn.com/dms/image/v2/D4E0BAQFecl8TBYAV8g/company-logo_100_100/company-logo_100_100/0/1665584284921?e=**********&v=beta&t=SlmnZW9TEYmZAjTrbHc4FZBFI0O9nIBRVnZW7nDzccY","2025-07-22T16:59:11.124Z","{""name"":null,""title"":null,""url"":null}",true,"<section class=""show-more-less-html"" data-max-lines=""5"">
        <div class=""show-more-less-html__markup show-more-less-html__markup--clamp-after-5
            relative overflow-hidden"">
          <p>Software Engineer &#x2013; TS/SCI with CI Polygraph | Agile | Java | Secure Systems</p><p><br></p><p><strong>Locations: San Diego, CA | Aurora, CO | Annapolis, MD | Alexandria, VA</strong></p><p><br></p><p>Are you an experienced Software Engineer looking to make a real-world impact on national security? Join a high-performing agile team delivering critical capabilities for a mission-focused government program.</p><p><br></p><p>Our client, a long-standing leader in enterprise security, cloud computing, and cyber defense, has been supporting federal and private sector programs with advanced technology solutions since 1996. With deep expertise in software development, systems integration, and secure engineering, they are expanding their talented team.</p><p><br></p><p>Role Overview</p><p>You&#x2019;ll work on a nationally significant program using the Scaled Agile Framework (SAFe), DevOps best practices, and modern software development approaches including automation and Behavior Driven Development (BDD). You&#x2019;ll contribute to delivering secure, scalable solutions in a dynamic development environment.</p><p><br></p><p>Key Responsibilities</p><ul><li>Develop software both independently and within a collaborative agile team</li><li>Analyse complex system requirements and use formal design methodologies (e.g., CASE tools, data flow diagrams)</li><li>Write secure, maintainable, and efficient code</li><li>Support shared development practices, process improvements, and code reviews</li><li>Create and document automation scripts for production deployment</li></ul><p><br></p><p>Required Qualifications</p><ul><li>Active TS/SCI with CI Polygraph clearance</li><li>Bachelor&#x2019;s degree in Computer Science or related field (or equivalent experience)</li><li>Strong Java development experience</li><li>Experience with one or more of the following: Python, C/C++, SQL, SOAP, PowerShell, Eclipse, Oracle, Jenkins, Postgres</li><li>Familiarity with UI development, XML/XSLT, and Windows or Linux environments</li><li>Solid understanding of secure development principles and testable code</li></ul><p><br></p><p>Preferred Experience - Nice to have</p><ul><li>Background in JEE, Tomcat, WebLogic, JBoss, and scripting languages</li><li>Experience in secure systems engineering, CI/CD pipelines, and interface control</li><li>Familiarity with Agile practices like BDD, TDD, refactoring, and pair programming</li><li>Hands-on experience with Selenium, Cucumber, Mockito, UFT, or FITNesse</li><li>SAFe Agile experience or certification</li><li>Knowledge of Layer 7 policy configuration and secure software development best practices</li></ul><p><br></p><p>If you&apos;re ready to apply your skills on projects that matter, we want to hear from you. This is a chance to join a tech-driven organization solving critical challenges with a strong mission focus.</p>
        </div>

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--more
        ml-0.5"" data-tracking-control-name=""public_jobs_show-more-html-btn"" aria-label=""Show more"" aria-expanded=""false"">
<!---->
        
            Show more
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/cyolgscd0imw2ldqppkrb84vo""></icon>
    </button>
  

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--less
        ml-0.5"" data-tracking-control-name=""public_jobs_show-less-html-btn"" aria-label=""Show less"" aria-expanded=""true"">
<!---->
        
            Show less
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/4chtt12k98xwnba1nimld2oyg""></icon>
    </button>
  
<!---->    </section>",,"{""min_amount"":null,""max_amount"":null,""currency"":null,""payment_period"":null}",,"2025-07-23T16:59:11.170Z","{""url"":""https://www.linkedin.com/jobs/view/senior-software-engineer-at-key-talent-solutions-4271443888"",""discovery_input"":{""location"":""CA"",""keyword"":""UIUX"",""country"":""US"",""time_range"":null,""job_type"":null,""experience_level"":null,""remote"":null}}",,,,
"https://www.linkedin.com/jobs/view/senior-software-engineer-%E2%80%93-ui-react-at-walmart-global-tech-4265351261?_l=en","4265351261","Senior Software Engineer – UI (React)","Walmart Global Tech","11174522","Sunnyvale, CA","We're aggressively hiring Senior & Staff Software Engineers with deep expertise in: 🔥 UI Engineering — React, TypeScript, modern web frameworks 🔥 Full Stack — Node.js, React, Java, GraphQL, Spring Boot No visa sponsorship available. Must be authorized to work in the U.S. without sponsorship. This is your chance to meet our technical leaders face-to-face and interview on the spot. Think you can handle billions of transactions? Prove it. Come code with us. 💡 Why join Walmart Global Tech? Work with world-class engineers Solve complex scale problems Direct customer impact at massive scale Tech org backed by the world’s largest company 🛠️ We’re not just hiring coders — we want builders, architects, and problem-solvers. 👉 Apply NOW to be considered for the event. Selected candidates will receive a formal invite. 🔗 Apply here: https://walmart.wd5.myworkdayjobs.com/WalmartExternal/job/Sunnyvale-CA/Senior--Software-Engineer--UI_R-2238563 ✅ What We’re Looking For: 5+ years of professional front-end development Strong with React , Hooks , State Management , and scalable design systems Obsessed with performance , accessibility , and UX polish Comfortable in CI/CD pipelines, Git, and testing frameworks (Jest, Cypress) Bonus: Experience in enterprise-scale apps, e-commerce, or micro frontends 🚀 Ready to disrupt the status quo? 📩 Drop your resume, or come meet us in person at our Sunnyvale Hiring Event. This is where real engineers come to scale. Let’s build what’s next. Together. #ReactJS #FrontendJobs #WalmartTech #SunnyvaleJobs #HiringEvent #SeniorEngineer #UIUX #ReactDeveloper #TechAtScale #SoftwareEngineering Show more Show less",,"Not Applicable","Information Technology","Full-time","Retail",,"https://www.linkedin.com/company/walmartglobaltech?trk=public_jobs_topcard-org-name","1 week ago",200,"{""location"":""CA"",""keyword"":""UIUX"",""country"":""US"",""time_range"":"""",""job_type"":"""",""experience_level"":"""",""remote"":"""",""company"":"""",""location_radius"":""""}",,"US","9","https://media.licdn.com/dms/image/v2/C560BAQEjq-bcaW-GSg/company-logo_100_100/company-logo_100_100/0/1630657358931/walmartglobaltech_logo?e=**********&v=beta&t=aOtdTbxVDQBQsa-xyW2VUJjF-RmLDYOwyzoYhqf9ytg","2025-07-16T16:59:15.617Z","{""name"":null,""title"":null,""url"":null}",true,"<section class=""show-more-less-html"" data-max-lines=""5"">
        <div class=""show-more-less-html__markup show-more-less-html__markup--clamp-after-5
            relative overflow-hidden"">
          <p>We&apos;re aggressively hiring <strong>Senior &amp; Staff Software Engineers</strong> with deep expertise in:</p><p>&#x1F525; <strong>UI Engineering &#x2014; React, TypeScript, modern web frameworks</strong></p><p>&#x1F525; <strong>Full Stack &#x2014; Node.js, React, Java, GraphQL, Spring Boot</strong></p><p><strong>No visa sponsorship available. Must be authorized to work in the U.S. without sponsorship.</strong></p><p>This is your chance to meet our technical leaders face-to-face and interview on the spot.</p><p>Think you can handle billions of transactions? Prove it. Come code with us.</p><p>&#x1F4A1; Why join Walmart Global Tech?</p><ul><li>Work with world-class engineers</li><li>Solve complex scale problems</li><li>Direct customer impact at massive scale</li><li>Tech org backed by the world&#x2019;s largest company</li></ul><p>&#x1F6E0;&#xFE0F; We&#x2019;re not just hiring coders &#x2014; we want builders, architects, and problem-solvers.</p><p>&#x1F449; <strong>Apply NOW to be considered for the event. Selected candidates will receive a formal invite.</strong></p><p>&#x1F517; Apply here: https://walmart.wd5.myworkdayjobs.com/WalmartExternal/job/Sunnyvale-CA/Senior--Software-Engineer--UI_R-2238563</p><p>&#x2705; What We&#x2019;re Looking For:</p><ul><li>5+ years of professional front-end development</li><li>Strong with <strong>React</strong>, <strong>Hooks</strong>, <strong>State Management</strong>, and scalable design systems</li><li>Obsessed with <strong>performance</strong>, <strong>accessibility</strong>, and <strong>UX polish</strong></li><li>Comfortable in CI/CD pipelines, Git, and testing frameworks (Jest, Cypress)</li><li>Bonus: Experience in enterprise-scale apps, e-commerce, or micro frontends</li></ul><p>&#x1F680; <strong>Ready to disrupt the status quo?</strong></p><p>&#x1F4E9; Drop your resume, or come meet us in person at our Sunnyvale Hiring Event.</p><p>This is where real engineers come to scale.</p><p><strong>Let&#x2019;s build what&#x2019;s next. Together.</strong></p><p>#ReactJS #FrontendJobs #WalmartTech #SunnyvaleJobs #HiringEvent #SeniorEngineer #UIUX #ReactDeveloper #TechAtScale #SoftwareEngineering</p>
        </div>

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--more
        ml-0.5"" data-tracking-control-name=""public_jobs_show-more-html-btn"" aria-label=""Show more"" aria-expanded=""false"">
<!---->
        
            Show more
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/cyolgscd0imw2ldqppkrb84vo""></icon>
    </button>
  

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--less
        ml-0.5"" data-tracking-control-name=""public_jobs_show-less-html-btn"" aria-label=""Show less"" aria-expanded=""true"">
<!---->
        
            Show less
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/4chtt12k98xwnba1nimld2oyg""></icon>
    </button>
  
<!---->    </section>",,"{""min_amount"":null,""max_amount"":null,""currency"":null,""payment_period"":null}",,"2025-07-23T16:59:15.644Z","{""url"":""https://www.linkedin.com/jobs/view/senior-software-engineer-%E2%80%93-ui-react-at-walmart-global-tech-4265351261"",""discovery_input"":{""location"":""CA"",""keyword"":""UIUX"",""country"":""US"",""time_range"":null,""job_type"":null,""experience_level"":null,""remote"":null}}",,,,
"https://www.linkedin.com/jobs/view/principal-network-operations-site-reliability-systems-engineer-at-hpe-aruba-networking-4233347867?_l=en","4233347867","Principal Network Operations Site Reliability Systems Engineer","HPE Aruba Networking","162533","San Jose, CA","This role has been designed as ‘Hybrid’ with an expectation that you will work on average 2 days per week from an HPE office. Who We Are: Hewlett Packard Enterprise is the global edge-to-cloud company advancing the way people live and work. We help companies connect, protect, analyze, and act on their data and applications wherever they live, from edge to cloud, so they can turn insights into outcomes at the speed required to thrive in today’s complex world. Our culture thrives on finding new and better ways to accelerate what’s next. We know varied backgrounds are valued and succeed here. We have the flexibility to manage our work and personal needs. We make bold moves, together, and are a force for good. If you are looking to stretch and grow your career our culture will embrace you. Open up opportunities with HPE. Job Description: Who We Are: Hewlett Packard Enterprise is the global edge-to-cloud company advancing the way people live and work. We help companies connect, protect, analyze, and act on their data and applications wherever they live, from edge to cloud, so they can turn insights into outcomes at the speed required to thrive in today’s complex world. Our culture thrives on finding new and better ways to accelerate what’s next. We know varied backgrounds are valued and succeed here. We have the flexibility to manage our work and personal needs. We make bold moves, together, and are a force for good. If you are looking to stretch and grow your career our culture will embrace you. Open up opportunities with HPE. The primary work location is as listed; however, remote work options will be considered. Job Description: Contributions have a visible technical impact on a services product. Apply in-depth professional knowledge and innovative ideas to solve complex network and software problems. Visible contributions improve service performance and network availability, achieve cost reductions, or satisfy current and future unmet customer needs. Recognized internal authority on key technology area applying innovative principles and ideas. Provides technical leadership for significant project/program work. Leads or participates in cross-functional initiatives and contributes to mentorship and knowledge sharing across the organization. Responsibilities: Develop strategies and implement plans to incorporate SRE concepts into network, tool, and process designs and leads execution of those strategies and plans Evaluates LAN, WLAN, SD-WAN, AAA, Private 5G, and other network designs for fit-for-use criteria, and designs prototype analysis tools to facilitate rapid iteration of network delivery service enhancements Identifies and engineers new ways to leverage data from multiple platforms to identify network performance trends and detect anomalies Prototypes machine learning anomaly detection, event signature identification, and trend identification Automates common incident management and problem management procedures Develops organization-wide architectures, methodologies, and prototypes for software systems design and development across multiple platforms and organizations within the Global Business Unit. Identifies and evaluates new technologies and innovations for alignment with technology roadmap and business value; creates plans for prototyping and prototype iteration. Reviews and evaluates designs and project activities for compliance with development guidelines and standards; provides tangible feedback to improve product quality and mitigate failure risk. Education And Experience Required: Bachelor’s or master’s degree in computer science, Computer Engineering, Information Systems, or equivalent. Typically, 10+ years’ experience. Knowledge And Skills: Experience with cloud platforms Experience with software development languages for console and web-based applications Experience in User Interface (UI/UX) design Understanding of and experience with common network infrastructure devices such as switches, routers, access points, authentication, authorization, and accounting systems and protocols, and network management utilities Experience with network monitoring protocols Ability to design and implement relational database solutions, time-series databases, and NoSQL database solutions Excellent analytical and problem-solving skills Experience in the overall architecture of software systems for products and solutions Designing and integrating software systems running on multiple platform types into overall architecture Evaluating and selecting forms and processes for software systems testing and methodology, including writing and execution of test plans, debugging, and testing scripts and tools History of innovation with multiple patents or deployed solutions in the field of software design Excellent written and verbal communication skills; mastery of English language; Ability to effectively communicate product architectures and design proposals and negotiate options at business unit and executive levels #unitedstates LI-Remote #network #relationaldatabase #SRE #sitereliability #architecture #10years+ #prototype #UIUX #design Additional Skills: Cloud Architectures, Cross Domain Knowledge, Design Thinking, Development Fundamentals, DevOps, Distributed Computing, Microservices Fluency, Full Stack Development, Security-First Mindset, User Experience (UX) What We Can Offer You: Health & Wellbeing We strive to provide our team members and their loved ones with a comprehensive suite of benefits that supports their physical, financial and emotional wellbeing. Personal & Professional Development We also invest in your career because the better you are, the better we all are. We have specific programs catered to helping you reach any career goals you have — whether you want to become a knowledge expert in your field or apply your skills to another division. Unconditional Inclusion We are unconditionally inclusive in the way we work and celebrate individual uniqueness. We know varied backgrounds are valued and succeed here. We have the flexibility to manage our work and personal needs. We make bold moves, together, and are a force for good. Let's Stay Connected: Follow @HPECareers on Instagram to see the latest on people, culture and tech at HPE. #unitedstates #aruba Job: Engineering Job Level: TCP_05 States with Pay Range Requirement The expected salary/wage range for a U.S.-based hire filling this position is provided below. Actual offer may vary from this range based upon geographic location, work experience, education/training, and/or skill level. If this is a sales role, then the listed salary range reflects combined base salary and target-level sales compensation pay. If this is a non-sales role, then the listed salary range reflects base salary only. Variable incentives may also be offered. Information about employee benefits offered can be found at https://myhperewards.com/main/new-hire-enrollment.html. USD Annual Salary: $115,500.00 - $266,000.00 HPE is an Equal Employment Opportunity/ Veterans/Disabled/LGBT employer. We do not discriminate on the basis of race, gender, or any other protected category, and all decisions we make are made on the basis of qualifications, merit, and business need. Our goal is to be one global team that is representative of our customers, in an inclusive environment where we can continue to innovate and grow together. Please click here: Equal Employment Opportunity. Hewlett Packard Enterprise is EEO Protected Veteran/ Individual with Disabilities. HPE will comply with all applicable laws related to employer use of arrest and conviction records, including laws requiring employers to consider for employment qualified applicants with criminal histories. Show more Show less",,"Not Applicable","Engineering and Information Technology","Full-time","IT Services and IT Consulting","$115,500.00/yr - $266,000.00/yr","https://www.linkedin.com/company/aruba-a-hewlett-packard-enterprise-company?trk=public_jobs_topcard-org-name","2 months ago",25,"{""location"":""CA"",""keyword"":""UIUX"",""country"":""US"",""time_range"":"""",""job_type"":"""",""experience_level"":"""",""remote"":"""",""company"":"""",""location_radius"":""""}","https://www.linkedin.com/jobs/view/externalApply/4233347867?url=https%3A%2F%2Fcareers%2Ehpe%2Ecom%2Fus%2Fen%2Fjob%2F1187888%2FPrincipal-Network-Operations-Site-Reliability-Systems-Engineer%3Futm_source%3Dlinkedin&urlHash=X0hS","US","22848","https://media.licdn.com/dms/image/v2/D4E0BAQGDnmjOQlmgtg/company-logo_100_100/B4EZdk_QyCHsAU-/0/1749745995186/aruba_a_hewlett_packard_enterprise_company_logo?e=**********&v=beta&t=Jew_bDgyEZD6bjlkWumu32nNXI8BFKNx7cbTlFGQPKE","2025-05-24T16:59:16.671Z","{""name"":null,""title"":null,""url"":null}",true,"<section class=""show-more-less-html"" data-max-lines=""5"">
        <div class=""show-more-less-html__markup show-more-less-html__markup--clamp-after-5
            relative overflow-hidden"">
          This role has been designed as &#x2018;Hybrid&#x2019; with an expectation that you will work on average 2 days per week from an HPE office.<br><br><strong><strong>Who We Are:<br><br></strong></strong>Hewlett Packard Enterprise is the global edge-to-cloud company advancing the way people live and work. We help companies connect, protect, analyze, and act on their data and applications wherever they live, from edge to cloud, so they can turn insights into outcomes at the speed required to thrive in today&#x2019;s complex world. Our culture thrives on finding new and better ways to accelerate what&#x2019;s next. We know varied backgrounds are valued and succeed here. We have the flexibility to manage our work and personal needs. We make bold moves, together, and are a force for good. If you are looking to stretch and grow your career our culture will embrace you. Open up opportunities with HPE.<br><br><strong><strong>Job Description:<br><br></strong></strong><strong>Who We Are:<br><br></strong>Hewlett Packard Enterprise is the global edge-to-cloud company advancing the way people live and work. We help companies connect, protect, analyze, and act on their data and applications wherever they live, from edge to cloud, so they can turn insights into outcomes at the speed required to thrive in today&#x2019;s complex world. Our culture thrives on finding new and better ways to accelerate what&#x2019;s next. We know varied backgrounds are valued and succeed here. We have the flexibility to manage our work and personal needs. We make bold moves, together, and are a force for good. If you are looking to stretch and grow your career our culture will embrace you. Open up opportunities with HPE.<br><br><strong>The primary work location is as listed; however, remote work options will be considered. <br><br></strong><strong><strong>Job Description:<br><br></strong></strong>Contributions have a visible technical impact on a services product. Apply in-depth professional knowledge and innovative ideas to solve complex network and software problems. Visible contributions improve service performance and network availability, achieve cost reductions, or satisfy current and future unmet customer needs. Recognized internal authority on key technology area applying innovative principles and ideas. Provides technical leadership for significant project/program work. Leads or participates in cross-functional initiatives and contributes to mentorship and knowledge sharing across the organization.<br><br><strong><strong>Responsibilities: <br><br></strong></strong><ul><li>Develop strategies and implement plans to incorporate SRE concepts into network, tool, and process designs and leads execution of those strategies and plans</li><li>Evaluates LAN, WLAN, SD-WAN, AAA, Private 5G, and other network designs for fit-for-use criteria, and designs prototype analysis tools to facilitate rapid iteration of network delivery service enhancements</li><li>Identifies and engineers new ways to leverage data from multiple platforms to identify network performance trends and detect anomalies</li><li>Prototypes machine learning anomaly detection, event signature identification, and trend identification</li><li>Automates common incident management and problem management procedures</li><li>Develops organization-wide architectures, methodologies, and prototypes for software systems design and development across multiple platforms and organizations within the Global Business Unit.</li><li>Identifies and evaluates new technologies and innovations for alignment with technology roadmap and business value; creates plans for prototyping and prototype iteration.</li><li>Reviews and evaluates designs and project activities for compliance with development guidelines and standards; provides tangible feedback to improve product quality and mitigate failure risk.<br><br></li></ul><strong><strong><em>Education And Experience Required: <br><br></em></strong></strong><ul><li>Bachelor&#x2019;s or master&#x2019;s degree in computer science, Computer Engineering, Information Systems, or equivalent.</li><li>Typically, 10+ years&#x2019; experience.<br><br></li></ul><strong><strong><em>Knowledge And Skills: <br><br></em></strong></strong><ul><li>Experience with cloud platforms</li><li>Experience with software development languages for console and web-based applications</li><li>Experience in User Interface (UI/UX) design</li><li>Understanding of and experience with common network infrastructure devices such as switches, routers, access points, authentication, authorization, and accounting systems and protocols, and network management utilities</li><li>Experience with network monitoring protocols</li><li>Ability to design and implement relational database solutions, time-series databases, and NoSQL database solutions</li><li>Excellent analytical and problem-solving skills</li><li>Experience in the overall architecture of software systems for products and solutions</li><li>Designing and integrating software systems running on multiple platform types into overall architecture</li><li>Evaluating and selecting forms and processes for software systems testing and methodology, including writing and execution of test plans, debugging, and testing scripts and tools</li><li>History of innovation with multiple patents or deployed solutions in the field of software design</li><li>Excellent written and verbal communication skills; mastery of English language; Ability to effectively communicate product architectures and design proposals and negotiate options at business unit and executive levels<br><br></li></ul>#unitedstates LI-Remote #network #relationaldatabase #SRE #sitereliability #architecture #10years+ #prototype #UIUX #design<br><br><strong><strong>Additional Skills:<br><br></strong></strong>Cloud Architectures, Cross Domain Knowledge, Design Thinking, Development Fundamentals, DevOps, Distributed Computing, Microservices Fluency, Full Stack Development, Security-First Mindset, User Experience (UX)<br><br><strong><strong>What We Can Offer You:<br><br></strong></strong><strong>Health &amp; Wellbeing<br><br></strong>We strive to provide our team members and their loved ones with a comprehensive suite of benefits that supports their physical, financial and emotional wellbeing.<br><br><strong>Personal &amp; Professional Development<br><br></strong>We also invest in your career because the better you are, the better we all are. We have specific programs catered to helping you reach any career goals you have &#x2014; whether you want to become a knowledge expert in your field or apply your skills to another division.<br><br><strong>Unconditional Inclusion<br><br></strong>We are unconditionally inclusive in the way we work and celebrate individual uniqueness. We know varied backgrounds are valued and succeed here. We have the flexibility to manage our work and personal needs. We make bold moves, together, and are a force for good.<br><br><strong><strong>Let&apos;s Stay Connected:<br><br></strong></strong>Follow @HPECareers on Instagram to see the latest on people, culture and tech at HPE.<br><br>#unitedstates<br><br>#aruba<br><br><strong><strong>Job:<br><br></strong></strong>Engineering<br><br><strong><strong>Job Level:<br><br></strong></strong>TCP_05<br><br><strong>States with Pay Range Requirement<br><br></strong>The expected salary/wage range for a U.S.-based hire filling this position is provided below. Actual offer may vary from this range based upon geographic location, work experience, education/training, and/or skill level. If this is a sales role, then the listed salary range reflects combined base salary and target-level sales compensation pay. If this is a non-sales role, then the listed salary range reflects base salary only. Variable incentives may also be offered. Information about employee benefits offered can be found at https://myhperewards.com/main/new-hire-enrollment.html.<br><br>USD Annual Salary: $115,500.00 - $266,000.00<br><br>HPE is an Equal Employment Opportunity/ Veterans/Disabled/LGBT employer. We do not discriminate on the basis of race, gender, or any other protected category, and all decisions we make are made on the basis of qualifications, merit, and business need. Our goal is to be one global team that is representative of our customers, in an inclusive environment where we can continue to innovate and grow together. Please click here: Equal Employment Opportunity.<br><br><strong>Hewlett Packard Enterprise is EEO Protected Veteran/ Individual with Disabilities.<br><br></strong>HPE will comply with all applicable laws related to employer use of arrest and conviction records, including laws requiring employers to consider for employment qualified applicants with criminal histories.
        </div>

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--more
        ml-0.5"" data-tracking-control-name=""public_jobs_show-more-html-btn"" aria-label=""Show more"" aria-expanded=""false"">
<!---->
        
            Show more
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/cyolgscd0imw2ldqppkrb84vo""></icon>
    </button>
  

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--less
        ml-0.5"" data-tracking-control-name=""public_jobs_show-less-html-btn"" aria-label=""Show less"" aria-expanded=""true"">
<!---->
        
            Show less
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/4chtt12k98xwnba1nimld2oyg""></icon>
    </button>
  
<!---->    </section>",,"{""min_amount"":115500,""max_amount"":266000,""currency"":""$"",""payment_period"":""yr""}","Retrieved from the description.","2025-07-23T16:59:16.696Z","{""url"":""https://www.linkedin.com/jobs/view/principal-network-operations-site-reliability-systems-engineer-at-hpe-aruba-networking-4233347867"",""discovery_input"":{""location"":""CA"",""keyword"":""UIUX"",""country"":""US"",""time_range"":null,""job_type"":null,""experience_level"":null,""remote"":null}}",,,,
"https://www.linkedin.com/jobs/view/senior-designer-otherlife-at-moonpay-4266773653?_l=en","4266773653","Senior Designer, Otherlife","MoonPay","12596773","New York, NY","About Otherlife 🌖💸 Otherlife by MoonPay is a digital creative agency designing unconventional, end-to-end solutions in Web3 strategy, design, branding, and development for the world's leading companies, including Mastercard, Arsenal Club, Coinbase, OpenSea, and Shopify. Our team of passionate creators is committed to building innovative solutions at the intersection of creativity and technology. We are always on the lookout for talented individuals who share our vision. 🌔 About the Opportunity The Senior Designer is primarily responsible for leading branding projects at Otherlife. They are also responsible for delivering against a wide range of design mandates in a multidisciplinary environment including but not limited to web design/UIUX, motion design, and collaborating on 3D and Illustration. This pivotal role ensures delivering and presenting the highest quality of creative work to Otherlife’s top tier clients. Working under the guidance of Design Directors and the Creative Director, they must demonstrate excellent attention to detail in design and craft. They will work closely with other Senior Designers, Illustrators, Motion Designers, and Design Leadership to ensure design integrity and brand alignment across all touch points. Note: This role requires 5+ years of design experience with 3+ years in a Senior Designer role (or higher) within an agency setting. 💻 Key Responsibilities, Skills & Experience: A passion for all aspects of design, including conceptual thinking, typography, motion, copy, and imagery Lead brand projects starting with research, including trends and competitor analysis and identifying whitespace opportunities Define and develop innovative, compelling and interactive brands, digital experiences, and concepts in collaboration with design leads, brand strategists, and clients Establish design systems, style guidelines for type, color usage, patterns, UI, iconography, illustration, motion, 3D, photography and videography Create and deliver presentations and written rationales for internal critiques and client-facing reviews Experience presenting work and interfacing with clients Design wireframes, screens, and prototypes to illustrate product behavior in relation to interaction, 3D, and motion Experience designing new, innovative layouts and designs that scale and flex across different platforms, including web, mobile, presentations, etc A user-centric design approach, as evidenced by considerations for accessibility, technology platforms, and contextual storytelling Experience designing interactive websites utilizing three.js, WebGL, React Three Fiber, Unity, Unreal Engine, GSAP Strong leadership and teamwork skills — the ability to work with Design Directors and Creative Directors, as well as the confidence and skill to communicate ideas clearly Adobe Creative Suite, Figma, Sketch, Cinema 4D Bachelor’s degree (or equivalent) in Graphic Design, Digital Media, or a related field Bonus: Design and animate 2D & 3D objects, characters, props, and environments in varying project styles Bonus experience: Experience working with crypto, NFTs, blockchain technology and Web3 Experience with project management Otherlife Perks Competitive salary, based on experience, market data and location Share options Health & dental benefits Company events Access to special events Office lunches Swag / merch Research has shown that women are less likely than men to apply for this role if they do not have experience in 100% of these areas. Please know that this list is indicative, and that we would still love to hear from you even if you feel that you are only a 75% match. Skills can be learnt, diversity cannot. Please let us know if you require any accommodations for the interview process, and we’ll do our best to provide assistance. Commitment To Diversity At MoonPay we believe that every voice matters. We strive to create a mindful and respectful environment where everyone can bring their authentic self to work, and experience a culture that is free of harassment, racism, and discrimination. That’s why we are committed to diversity and inclusion in the workplace and are a proud equal opportunity employer. We prohibit discrimination and harassment of any kind based on race, color, religion, sex, sexual orientation, gender identity, national origin, age, disability, protected veteran status or any other characteristic protected by law. This policy applies to all employment practices within our organization, including, but not limited to, hiring, recruiting, promotion, termination, layoff, and leave of absence. MoonPay is also committed to providing reasonable accommodations in our job application procedures for qualified individuals with disabilities. Please inform our Talent Team if you need any assistance completing any forms or to otherwise participate in the application process. Please be aware that MoonPay does not request an AI-led interview without seeing a recruiter or team member from MoonPay on video call. We won't ask for your personal identification documents or any money from you during your interview process with us. Be fraud smart! If you receive an email - claiming to be from MoonPay - but from an email address ending in anything other than @moonpay.com, please be aware that this is not us. Show more Show less",,"Not Applicable","Design, Art/Creative, and Information Technology","Full-time","Hospitality, Food and Beverage Services, and Retail","$125,000.00/yr - $175,000.00/yr","https://www.linkedin.com/company/moonpay?trk=public_jobs_topcard-org-name","1 week ago",40,"{""location"":""NY"",""keyword"":""UIUX"",""country"":""US"",""time_range"":"""",""job_type"":"""",""experience_level"":"""",""remote"":"""",""company"":"""",""location_radius"":""""}",,"US","239","https://media.licdn.com/dms/image/v2/D560BAQHlaph_qWt7MA/company-logo_100_100/company-logo_100_100/0/1702140517386/moonpay_logo?e=**********&v=beta&t=Gd5M7gZFMFHEinrbgCNVXYVdpW6yNGxYEE_3sps_IB8","2025-07-16T16:59:24.649Z","{""name"":null,""title"":null,""url"":null}",true,"<section class=""show-more-less-html"" data-max-lines=""5"">
        <div class=""show-more-less-html__markup show-more-less-html__markup--clamp-after-5
            relative overflow-hidden"">
          <strong>About Otherlife &#x1F316;&#x1F4B8;<br></strong>Otherlife by MoonPay is a digital creative agency designing unconventional, end-to-end solutions in Web3 strategy, design, branding, and development for the world&apos;s leading companies, including Mastercard, Arsenal Club, Coinbase, OpenSea, and Shopify.<br><br>Our team of passionate creators is committed to building innovative solutions at the intersection of creativity and technology. We are always on the lookout for talented individuals who share our vision.<br><br>&#x1F314; About the Opportunity <br><br>The Senior Designer is primarily responsible for leading branding projects at Otherlife. They are also responsible for delivering against a wide range of design mandates in a multidisciplinary environment including but not limited to web design/UIUX, motion design, and collaborating on 3D and Illustration. This pivotal role ensures delivering and presenting the highest quality of creative work to Otherlife&#x2019;s top tier clients.<br><br>Working under the guidance of Design Directors and the Creative Director, they must demonstrate excellent attention to detail in design and craft. They will work closely with other Senior Designers, Illustrators, Motion Designers, and Design Leadership to ensure design integrity and brand alignment across all touch points.<br><br>Note: <br><br>This role requires 5+ years of design experience with 3+ years in a Senior Designer role (or higher) within an agency setting.<br><br>&#x1F4BB; Key Responsibilities, Skills &amp; Experience:<br><br><br><ul><li> A passion for all aspects of design, including conceptual thinking, typography, motion, copy, and imagery</li><li> Lead brand projects starting with research, including trends and competitor analysis and identifying whitespace opportunities</li><li> Define and develop innovative, compelling and interactive brands, digital experiences, and concepts in collaboration with design leads, brand strategists, and clients</li><li> Establish design systems, style guidelines for type, color usage, patterns, UI, iconography, illustration, motion, 3D, photography and videography</li><li> Create and deliver presentations and written rationales for internal critiques and client-facing reviews</li><li> Experience presenting work and interfacing with clients</li><li> Design wireframes, screens, and prototypes to illustrate product behavior in relation to interaction, 3D, and motion</li><li> Experience designing new, innovative layouts and designs that scale and flex across different platforms, including web, mobile, presentations, etc</li><li> A user-centric design approach, as evidenced by considerations for accessibility, technology platforms, and contextual storytelling</li><li> Experience designing interactive websites utilizing three.js, WebGL, React Three Fiber, Unity, Unreal Engine, GSAP</li><li> Strong leadership and teamwork skills &#x2014; the ability to work with Design Directors and Creative Directors, as well as the confidence and skill to communicate ideas clearly</li><li> Adobe Creative Suite, Figma, Sketch, Cinema 4D</li><li> Bachelor&#x2019;s degree (or equivalent) in Graphic Design, Digital Media, or a related field</li><li> Bonus: Design and animate 2D &amp; 3D objects, characters, props, and environments in varying project styles<br><br><br><br><br></li></ul>Bonus experience:<br><br><br><ul><li> Experience working with crypto, NFTs, blockchain technology and Web3</li><li>Experience with project management<br><br><br><br><br></li></ul><strong>Otherlife Perks<br></strong>Competitive salary, based on experience, market data and location<br><br>Share options<br><br>Health &amp; dental benefits<br><br>Company events<br><br>Access to special events<br><br>Office lunches<br><br>Swag / merch<br><br><em><strong>Research has shown that women are less likely than men to apply for this role if they do not have experience in 100% of these areas. Please know that this list is indicative, and that we would still love to hear from you even if you feel that you are only a 75% match. Skills can be learnt, diversity cannot.<br><br></strong></em><strong>Please let us know if you require any accommodations for the interview process, and we&#x2019;ll do our best to provide assistance.<br></strong><strong>Commitment To Diversity<br></strong>At MoonPay we believe that every voice matters. We strive to create a mindful and respectful environment where everyone can bring their authentic self to work, and experience a culture that is free of harassment, racism, and discrimination. That&#x2019;s why we are committed to diversity and inclusion in the workplace and are a proud equal opportunity employer. We prohibit discrimination and harassment of any kind based on race, color, religion, sex, sexual orientation, gender identity, national origin, age, disability, protected veteran status or any other characteristic protected by law. This policy applies to all employment practices within our organization, including, but not limited to, hiring, recruiting, promotion, termination, layoff, and leave of absence.<br><br>MoonPay is also committed to providing reasonable accommodations in our job application procedures for qualified individuals with disabilities. Please inform our Talent Team if you need any assistance completing any forms or to otherwise participate in the application process.<br><br><em>Please be aware that MoonPay does not request an AI-led interview without seeing a recruiter or team member from MoonPay on video call. We won&apos;t ask for your personal identification documents or any money from you during your interview process with us. Be fraud smart! If you receive an email - claiming to be from MoonPay - but from an email address ending in anything other than @moonpay.com, please be aware that this is not us.</em>
        </div>

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--more
        ml-0.5"" data-tracking-control-name=""public_jobs_show-more-html-btn"" aria-label=""Show more"" aria-expanded=""false"">
<!---->
        
            Show more
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/cyolgscd0imw2ldqppkrb84vo""></icon>
    </button>
  

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--less
        ml-0.5"" data-tracking-control-name=""public_jobs_show-less-html-btn"" aria-label=""Show less"" aria-expanded=""true"">
<!---->
        
            Show less
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/4chtt12k98xwnba1nimld2oyg""></icon>
    </button>
  
<!---->    </section>",,"{""min_amount"":125000,""max_amount"":175000,""currency"":""$"",""payment_period"":""yr""}","This range is provided by MoonPay. Your actual pay will be based on your skills and experience — talk with your recruiter to learn more.","2025-07-23T16:59:24.675Z","{""url"":""https://www.linkedin.com/jobs/view/senior-designer-otherlife-at-moonpay-4266773653"",""discovery_input"":{""location"":""NY"",""keyword"":""UIUX"",""country"":""US"",""time_range"":null,""job_type"":null,""experience_level"":null,""remote"":null}}",,,,
"https://www.linkedin.com/jobs/view/front-end-ui-engineer-734-at-protegrity-4267763213?_l=en","4267763213","Front-End UI Engineer 734","Protegrity","15741","New York, NY","At Protegrity, we lead innovation by using AI and quantum-resistant cryptography to transform data protection across cloud-native, hybrid, on-premises, and open source environments. We leverage advanced cryptographic methods such as tokenization, format-preserving encryption, and quantum-resilient techniques to protect sensitive data. As a global leader in data security, our mission is to ensure that data isn’t just valuable but also usable, trusted, and safe. Protegrity offers the opportunity to work at the intersection of innovation and collaboration, with the ability to make a meaningful impact on the industry while working alongside some of the brightest minds. Together, we are redefining how the world safeguards data, enabling organizations to thrive in a GenAI era where data is the ultimate currency. If you're ready to shape the future of data security, Protegrity is the place for you. Protegrity is seeking a passionate and talented Front-End UI Engineer to join our Product Innovation Team. This position is a hybrid position, with a preference for candidates located in Northern California, New York or Stamford Connecticut. Data Privacy is important to all of us. At Protegrity our products make this possible in rest, in motion and in view for some of the largest companies in the world. Protegrity puts our customers in the best possible position to choose how they want to protect their data and make it private. Thanks to the centralized management and transparency of our platform- a “single pane of glass”- teams can see where the data resides and what its purpose is. Unlike other data protection options, the Protegrity data Protection Platform clarifies the state of data so organizations can choose how to protect it and keep it private using the full range of methods from basic monitoring and dynamic data masking to highly secure vaultless tokenization. Since our founding over 10 years ago, we are fully vested in helping clients protect their information. Whether encrypting, tokenizing, or applying privacy methods, our platform secures the data behind the many operational systems that drive the day-to-day functions of business, as well as the analytical systems behind decision-making, personalized customer experiences, and AI modeling. All of this propels businesses forward by fueling innovation and giving clients a competitive advantage over less savvy competitors. This is a once in a lifetime opportunity to be part of a global team with a product that the business marketplace MUST have in place to do business. Responsibilities Work as part of our centralized UI/UX Team, bringing cross company branding and identity to a global level across our security product user interfaces. Daily/weekly collaboration with teammates: engineer standups, team meetings, 1:1, development demos, sprint planning, UX design scrub meetings. Experience with WCAG Accessibility Standards 2.2 Level AA. Build, debug and maintain scalable, responsive, quality front-end solutions following best practices that function effectively across a variety of platforms and devices. HTML5, CSS/SASS (or comparable CSS preprocessor), Bootstrap, JavaScript and/or JS Frameworks such as React.js/Jest. On-hands experience with UX Designers. Knowledge of Figma (or alternative) bridging the gap between design and development. Implementing high-fidelity designs transformed into smooth, intuitive, easy-to-use journeys in our user interfaces. Curiosity and enthusiasm for leveraging Ai development tools to streamline work and time management. Comfortable using Git for version control and Webpack for automation tasks. Experience working with design systems and understanding the fundamentals and importance of this ecosystem. Testing and debugging at all stages of the SDLC. Participate in code review and open-brainstorm sessions. Help in facilitation of JIRA scrum tasks following agile methodologies. Familiarity working with REST APIs and JSON. Qualifications 5+ years of software engineering experience with a college degree and/or other demonstration of deep expertise in front-end engineering (profile encouraged). High competence in verbal and written communication is essential. Accountability and self-management to handle several projects at once across multiple code bases (we are a centralized team). Expertise in computer science fundamentals. Ability to multiply the abilities and skills of others. Knowledge and experience implementing client-side security best practices. Comfort with critical thinking and creative problem-solving. Ability to articulate ideas clearly and collaborate effectively with cross-functional team members, including VPs, POs, PMs, QA, DevOps, Program Management, designers and back-end developers. Preferred Qualifications Knowledge and experience working in data security. Scrum master/project management experience. Hands on experience working with RESTful APIs to OpenAPI 3.0 specification. Enthusiasm to learn and understand natural language processing (NLP) for human-AI interaction. Familiarity with cloud services like AWS, Google Cloud, or Azure. Familiarity with building Jenkins pipelines and other CI/CD processes. Inspection of code quality, scanning, monitoring, and security with Snyk and SonarQube. Design and Prototyping. Bonus points for working with Docker Containers Key Personal Attributes Exceptional problem-solving skills. Detail-oriented with an eye for aesthetics and usability. Passion for staying up to date on the latest trends and/or new developments in the UIUX arena. Understanding user needs and implementation of solutions. Ability to efficiently manage workload and prioritize tasks to meet deadlines. Understanding and sharing the feelings of others, fostering a supportive team environment is important. Showing Integrity and always maintaining honest and ethical standards. Flexibility in schedule working internationally across time zones. Organization is top tier, tracking multiple tasks at once and keeping projects running as efficiently as possible and on time. Analytical problem solving, able to make bite-size chunks out of complex issues. Why Choose Protegrity Become a member of a leading Data Protection, Privacy and Security company during one of the best market opportunities to come along in a generation. Competitive Compensation/Total Reward Packages that include: Health Benefits (Health/Dental/Vision). Paid Time Off (PTO). 401K. Annual Bonus Incentives. Short and Long Term Disability. Work on global projects with diverse, energetic, team members who respect each other and celebrate differences. Talent First Workforce. Working Model This is a hybrid role and will require you to be in office 2-3 days a week. In certain circumstances the work model may change to accommodate business needs. Should you accept this position, you will be required to consent to and successfully complete a background investigation. This may include, subject to local laws, verification of extended education and additional criminal and civil checks. We offer a competitive salary and comprehensive benefits with generous vacation and holiday time off. All employees are also provided access to ongoing learning & development. Ensuring a diverse and inclusive workplace is our priority. We are committed to an environment of acceptance where you are free to bring your full self to work. All qualified applicants and current employees will not be discriminated against on the basis of race, color, religion, sex, sexual orientation, gender identity, age, national origin, disability or veteran status. Please reference Section 12: Supplemental Notice for Job Applicants in our Privacy Policy to inform you of the categories of personal information that we collect from individuals who inquire about and/or apply to work for Protegrity USA, Inc., or its parent company, subsidiaries or affiliates, and the purposes for which we use such personal information. Compensation Range: $117K - $144K Show more Show less",,"Mid-Senior level","Engineering and Information Technology","Full-time","Computer and Network Security","$117,000.00/yr - $144,000.00/yr","https://www.linkedin.com/company/protegrity?trk=public_jobs_topcard-org-name","6 days ago",59,"{""location"":""NY"",""keyword"":""UIUX"",""country"":""US"",""time_range"":"""",""job_type"":"""",""experience_level"":"""",""remote"":"""",""company"":"""",""location_radius"":""""}","https://www.linkedin.com/jobs/view/externalApply/4267763213?url=https%3A%2F%2Fjobs%2Eashbyhq%2Ecom%2Fprotegrity%2Fcf118b10-36cd-460a-a260-bb675e9ce1dc%3Futm_source%3DLinkedin_Applicant&urlHash=igLf","US","8632","https://media.licdn.com/dms/image/v2/D560BAQEjzkD3AIx29w/company-logo_100_100/company-logo_100_100/0/1697749697495/protegrity_logo?e=**********&v=beta&t=QE8SK2VHsmJBImfsVPQk2a_x7rNjtiuK_P9ECf3btic","2025-07-17T16:59:29.592Z","{""name"":null,""title"":null,""url"":null}",true,"<section class=""show-more-less-html"" data-max-lines=""5"">
        <div class=""show-more-less-html__markup show-more-less-html__markup--clamp-after-5
            relative overflow-hidden"">
          At Protegrity, we lead innovation by using AI and quantum-resistant cryptography to transform data protection across cloud-native, hybrid, on-premises, and open source environments. We leverage advanced cryptographic methods such as tokenization, format-preserving encryption, and quantum-resilient techniques to protect sensitive data. As a global leader in data security, our mission is to ensure that data isn&#x2019;t just valuable but also usable, trusted, and safe.<br><br>Protegrity offers the opportunity to work at the intersection of innovation and collaboration, with the ability to make a meaningful impact on the industry while working alongside some of the brightest minds. Together, we are redefining how the world safeguards data, enabling organizations to thrive in a GenAI era where data is the ultimate currency. If you&apos;re ready to shape the future of data security, Protegrity is the place for you.<br><br>Protegrity is seeking a passionate and talented Front-End UI Engineer to join our Product Innovation Team. This position is a hybrid position, with a preference for candidates located in Northern California, New York or Stamford Connecticut.<br><br>Data Privacy is important to all of us. At Protegrity our products make this possible in rest, in motion and in view for some of the largest companies in the world. Protegrity puts our customers in the best possible position to choose how they want to protect their data and make it private. Thanks to the centralized management and transparency of our platform- a &#x201C;single pane of glass&#x201D;- teams can see where the data resides and what its purpose is. Unlike other data protection options, the Protegrity data Protection Platform clarifies the state of data so organizations can choose how to protect it and keep it private using the full range of methods from basic monitoring and dynamic data masking to highly secure vaultless tokenization.<br><br>Since our founding over 10 years ago, we are fully vested in helping clients protect their information. Whether encrypting, tokenizing, or applying privacy methods, our platform secures the data behind the many operational systems that drive the day-to-day functions of business, as well as the analytical systems behind decision-making, personalized customer experiences, and AI modeling. All of this propels businesses forward by fueling innovation and giving clients a competitive advantage over less savvy competitors. This is a once in a lifetime opportunity to be part of a global team with a product that the business marketplace MUST have in place to do business.<br><br><strong>Responsibilities<br><br></strong><ul><li>Work as part of our centralized UI/UX Team, bringing cross company branding and identity to a global level across our security product user interfaces.</li><li>Daily/weekly collaboration with teammates: engineer standups, team meetings, 1:1, development demos, sprint planning, UX design scrub meetings.</li><li>Experience with WCAG Accessibility Standards 2.2 Level AA.</li><li>Build, debug and maintain scalable, responsive, quality front-end solutions following best practices that function effectively across a variety of platforms and devices.</li><li>HTML5, CSS/SASS (or comparable CSS preprocessor), Bootstrap, JavaScript and/or JS Frameworks such as React.js/Jest.</li><li>On-hands experience with UX Designers. Knowledge of Figma (or alternative) bridging the gap between design and development. Implementing high-fidelity designs transformed into smooth, intuitive, easy-to-use journeys in our user interfaces.</li><li>Curiosity and enthusiasm for leveraging Ai development tools to streamline work and time management.</li><li>Comfortable using Git for version control and Webpack for automation tasks.</li><li>Experience working with design systems and understanding the fundamentals and importance of this ecosystem.</li><li>Testing and debugging at all stages of the SDLC.</li><li>Participate in code review and open-brainstorm sessions.</li><li>Help in facilitation of JIRA scrum tasks following agile methodologies.</li><li>Familiarity working with REST APIs and JSON.<br><br></li></ul><strong>Qualifications<br><br></strong><ul><li>5+ years of software engineering experience with a college degree and/or other demonstration of deep expertise in front-end engineering (profile encouraged).</li><li>High competence in verbal and written communication is essential.</li><li>Accountability and self-management to handle several projects at once across multiple code bases (we are a centralized team).</li><li>Expertise in computer science fundamentals.</li><li>Ability to multiply the abilities and skills of others.</li><li>Knowledge and experience implementing client-side security best practices.</li><li>Comfort with critical thinking and creative problem-solving.</li><li>Ability to articulate ideas clearly and collaborate effectively with cross-functional team members, including VPs, POs, PMs, QA, DevOps, Program Management, designers and back-end developers.<br><br></li></ul><strong>Preferred Qualifications<br><br></strong><ul><li>Knowledge and experience working in data security.</li><li>Scrum master/project management experience.</li><li>Hands on experience working with RESTful APIs to OpenAPI 3.0 specification.</li><li>Enthusiasm to learn and understand natural language processing (NLP) for human-AI interaction.</li><li>Familiarity with cloud services like AWS, Google Cloud, or Azure.</li><li>Familiarity with building Jenkins pipelines and other CI/CD processes.</li><li>Inspection of code quality, scanning, monitoring, and security with Snyk and SonarQube.</li><li>Design and Prototyping.</li><li>Bonus points for working with Docker Containers<br><br></li></ul><strong>Key Personal Attributes<br><br></strong><ul><li>Exceptional problem-solving skills.</li><li>Detail-oriented with an eye for aesthetics and usability.</li><li>Passion for staying up to date on the latest trends and/or new developments in the UIUX arena.</li><li>Understanding user needs and implementation of solutions.</li><li>Ability to efficiently manage workload and prioritize tasks to meet deadlines.</li><li>Understanding and sharing the feelings of others, fostering a supportive team environment is important.</li><li>Showing Integrity and always maintaining honest and ethical standards.</li><li>Flexibility in schedule working internationally across time zones.</li><li>Organization is top tier, tracking multiple tasks at once and keeping projects running as efficiently as possible and on time.</li><li>Analytical problem solving, able to make bite-size chunks out of complex issues.<br><br></li></ul><strong>Why Choose Protegrity<br><br></strong><ul><li>Become a member of a leading Data Protection, Privacy and Security company during one of the best market opportunities to come along in a generation.</li><li>Competitive Compensation/Total Reward Packages that include:</li><li>Health Benefits (Health/Dental/Vision).</li><li>Paid Time Off (PTO).</li><li>401K.</li><li>Annual Bonus Incentives.</li><li>Short and Long Term Disability.</li><li>Work on global projects with diverse, energetic, team members who respect each other and celebrate differences.</li><li>Talent First Workforce.<br><br></li></ul><strong>Working Model<br><br></strong><ul><li>This is a hybrid role and will require you to be in office 2-3 days a week. In certain circumstances the work model may change to accommodate business needs.<br><br></li></ul>Should you accept this position, you will be required to consent to and successfully complete a background investigation. This may include, subject to local laws, verification of extended education and additional criminal and civil checks.<br><br>We offer a competitive salary and comprehensive benefits with generous vacation and holiday time off. All employees are also provided access to ongoing learning &amp; development.<br><br>Ensuring a diverse and inclusive workplace is our priority. We are committed to an environment of acceptance where you are free to bring your full self to work. All qualified applicants and current employees will not be discriminated against on the basis of race, color, religion, sex, sexual orientation, gender identity, age, national origin, disability or veteran status.<br><br>Please reference Section 12: Supplemental Notice for Job Applicants in our Privacy Policy to inform you of the categories of personal information that we collect from individuals who inquire about and/or apply to work for Protegrity USA, Inc., or its parent company, subsidiaries or affiliates, and the purposes for which we use such personal information.<br><br>Compensation Range: $117K - $144K
        </div>

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--more
        ml-0.5"" data-tracking-control-name=""public_jobs_show-more-html-btn"" aria-label=""Show more"" aria-expanded=""false"">
<!---->
        
            Show more
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/cyolgscd0imw2ldqppkrb84vo""></icon>
    </button>
  

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--less
        ml-0.5"" data-tracking-control-name=""public_jobs_show-less-html-btn"" aria-label=""Show less"" aria-expanded=""true"">
<!---->
        
            Show less
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/4chtt12k98xwnba1nimld2oyg""></icon>
    </button>
  
<!---->    </section>",,"{""min_amount"":117000,""max_amount"":144000,""currency"":""$"",""payment_period"":""yr""}","Retrieved from the description.","2025-07-23T16:59:29.627Z","{""url"":""https://www.linkedin.com/jobs/view/front-end-ui-engineer-734-at-protegrity-4267763213"",""discovery_input"":{""location"":""NY"",""keyword"":""UIUX"",""country"":""US"",""time_range"":null,""job_type"":null,""experience_level"":null,""remote"":null}}",,,,
"https://www.linkedin.com/jobs/view/front-end-ui-engineer-734-at-tm2-group-llc-4267770308?_l=en","4267770308","Front-End UI Engineer 734","TM2 Group, LLC","42394501","New York, NY","At Protegrity, we lead innovation by using AI and quantum-resistant cryptography to transform data protection across cloud-native, hybrid, on-premises, and open source environments. We leverage advanced cryptographic methods such as tokenization, format-preserving encryption, and quantum-resilient techniques to protect sensitive data. As a global leader in data security, our mission is to ensure that data isn’t just valuable but also usable, trusted, and safe. Protegrity offers the opportunity to work at the intersection of innovation and collaboration, with the ability to make a meaningful impact on the industry while working alongside some of the brightest minds. Together, we are redefining how the world safeguards data, enabling organizations to thrive in a GenAI era where data is the ultimate currency. If you're ready to shape the future of data security, Protegrity is the place for you. Protegrity is seeking a passionate and talented Front-End UI Engineer to join our Product Innovation Team. This position is a hybrid position, with a preference for candidates located in Northern California, New York or Stamford Connecticut. Data Privacy is important to all of us. At Protegrity our products make this possible in rest, in motion and in view for some of the largest companies in the world. Protegrity puts our customers in the best possible position to choose how they want to protect their data and make it private. Thanks to the centralized management and transparency of our platform- a “single pane of glass”- teams can see where the data resides and what its purpose is. Unlike other data protection options, the Protegrity data Protection Platform clarifies the state of data so organizations can choose how to protect it and keep it private using the full range of methods from basic monitoring and dynamic data masking to highly secure vaultless tokenization. Since our founding over 10 years ago, we are fully vested in helping clients protect their information. Whether encrypting, tokenizing, or applying privacy methods, our platform secures the data behind the many operational systems that drive the day-to-day functions of business, as well as the analytical systems behind decision-making, personalized customer experiences, and AI modeling. All of this propels businesses forward by fueling innovation and giving clients a competitive advantage over less savvy competitors. This is a once in a lifetime opportunity to be part of a global team with a product that the business marketplace MUST have in place to do business. Responsibilities: Work as part of our centralized UI/UX Team, bringing cross company branding and identity to a global level across our security product user interfaces. Daily/weekly collaboration with teammates: engineer standups, team meetings, 1:1, development demos, sprint planning, UX design scrub meetings. Experience with WCAG Accessibility Standards 2.2 Level AA. Build, debug and maintain scalable, responsive, quality front-end solutions following best practices that function effectively across a variety of platforms and devices. HTML5, CSS/SASS (or comparable CSS preprocessor), Bootstrap, JavaScript and/or JS Frameworks such as React.js/Jest. On-hands experience with UX Designers. Knowledge of Figma (or alternative) bridging the gap between design and development. Implementing high-fidelity designs transformed into smooth, intuitive, easy-to-use journeys in our user interfaces. Curiosity and enthusiasm for leveraging Ai development tools to streamline work and time management. Comfortable using Git for version control and Webpack for automation tasks. Experience working with design systems and understanding the fundamentals and importance of this ecosystem. Testing and debugging at all stages of the SDLC. Participate in code review and open-brainstorm sessions. Help in facilitation of JIRA scrum tasks following agile methodologies. Familiarity working with REST APIs and JSON. Qualifications : 5+ years of software engineering experience with a college degree and/or other demonstration of deep expertise in front-end engineering (profile encouraged). High competence in verbal and written communication is essential. Accountability and self-management to handle several projects at once across multiple code bases (we are a centralized team). Expertise in computer science fundamentals. Ability to multiply the abilities and skills of others. Knowledge and experience implementing client-side security best practices. Comfort with critical thinking and creative problem-solving. Ability to articulate ideas clearly and collaborate effectively with cross-functional team members, including VPs, POs, PMs, QA, DevOps, Program Management, designers and back-end developers. Preferred Qualifications: Knowledge and experience working in data security. Scrum master/project management experience. Hands on experience working with RESTful APIs to OpenAPI 3.0 specification. Enthusiasm to learn and understand natural language processing (NLP) for human-AI interaction. Familiarity with cloud services like AWS, Google Cloud, or Azure. Familiarity with building Jenkins pipelines and other CI/CD processes. Inspection of code quality, scanning, monitoring, and security with Snyk and SonarQube. Design and Prototyping. Bonus points for working with Docker Containers Key Personal Attributes: Exceptional problem-solving skills. Detail-oriented with an eye for aesthetics and usability. Passion for staying up to date on the latest trends and/or new developments in the UIUX arena. Understanding user needs and implementation of solutions. Ability to efficiently manage workload and prioritize tasks to meet deadlines. Understanding and sharing the feelings of others, fostering a supportive team environment is important. Showing Integrity and always maintaining honest and ethical standards. Flexibility in schedule working internationally across time zones. Organization is top tier, tracking multiple tasks at once and keeping projects running as efficiently as possible and on time. Analytical problem solving, able to make bite-size chunks out of complex issues. Why Choose Protegrity: Become a member of a leading Data Protection, Privacy and Security company during one of the best market opportunities to come along in a generation. Competitive Compensation/Total Reward Packages that include: Health Benefits (Health/Dental/Vision). Paid Time Off (PTO). 401K. Annual Bonus Incentives. Short and Long Term Disability. Work on global projects with diverse, energetic, team members who respect each other and celebrate differences. Talent First Workforce. Working Model: This is a hybrid role and will require you to be in office 2-3 days a week. In certain circumstances the work model may change to accommodate business needs. Should you accept this position, you will be required to consent to and successfully complete a background investigation. This may include, subject to local laws, verification of extended education and additional criminal and civil checks. We offer a competitive salary and comprehensive benefits with generous vacation and holiday time off. All employees are also provided access to ongoing learning & development. Ensuring a diverse and inclusive workplace is our priority. We are committed to an environment of acceptance where you are free to bring your full self to work. All qualified applicants and current employees will not be discriminated against on the basis of race, color, religion, sex, sexual orientation, gender identity, age, national origin, disability or veteran status. Please reference Section 12: Supplemental Notice for Job Applicants in our Privacy Policy to inform you of the categories of personal information that we collect from individuals who inquire about and/or apply to work for Protegrity USA, Inc., or its parent company, subsidiaries or affiliates, and the purposes for which we use such personal information. Compensation Range: $117K - $144K Show more Show less",,"Mid-Senior level","Engineering and Information Technology","Full-time","Business Consulting and Services","$117,000.00/yr - $144,000.00/yr","https://www.linkedin.com/company/tm2-group-llc?trk=public_jobs_topcard-org-name","6 days ago",29,"{""location"":""NY"",""keyword"":""UIUX"",""country"":""US"",""time_range"":"""",""job_type"":"""",""experience_level"":"""",""remote"":"""",""company"":"""",""location_radius"":""""}","https://www.linkedin.com/jobs/view/externalApply/4267770308?url=https%3A%2F%2Fjobs%2Eashbyhq%2Ecom%2Fprotegrity%2Fcf118b10-36cd-460a-a260-bb675e9ce1dc%2Fapplication%3Futm_source%3Dl7522D97Kr&urlHash=o0sM","US","8632","https://media.licdn.com/dms/image/v2/C4D0BAQHv63NXbANpTA/company-logo_100_100/company-logo_100_100/0/1630569237936?e=**********&v=beta&t=4-hSetAXxA92cKBDLkgNXP2jCKJdzAwcsdiju57i51o","2025-07-17T16:59:36.717Z","{""name"":null,""title"":null,""url"":null}",true,"<section class=""show-more-less-html"" data-max-lines=""5"">
        <div class=""show-more-less-html__markup show-more-less-html__markup--clamp-after-5
            relative overflow-hidden"">
          At Protegrity, we lead innovation by using AI and quantum-resistant cryptography to transform data protection across cloud-native, hybrid, on-premises, and open source environments. We leverage advanced cryptographic methods such as tokenization, format-preserving encryption, and quantum-resilient techniques to protect sensitive data. As a global leader in data security, our mission is to ensure that data isn&#x2019;t just valuable but also usable, trusted, and safe.<br><br>Protegrity offers the opportunity to work at the intersection of innovation and collaboration, with the ability to make a meaningful impact on the industry while working alongside some of the brightest minds. Together, we are redefining how the world safeguards data, enabling organizations to thrive in a GenAI era where data is the ultimate currency. If you&apos;re ready to shape the future of data security, Protegrity is the place for you.<br><br>Protegrity is seeking a passionate and talented Front-End UI Engineer to join our Product Innovation Team. This position is a hybrid position, with a preference for candidates located in Northern California, New York or Stamford Connecticut.<br><br>Data Privacy is important to all of us. At Protegrity our products make this possible in rest, in motion and in view for some of the largest companies in the world. Protegrity puts our customers in the best possible position to choose how they want to protect their data and make it private. Thanks to the centralized management and transparency of our platform- a &#x201C;single pane of glass&#x201D;- teams can see where the data resides and what its purpose is. Unlike other data protection options, the Protegrity data Protection Platform clarifies the state of data so organizations can choose how to protect it and keep it private using the full range of methods from basic monitoring and dynamic data masking to highly secure vaultless tokenization.<br><br>Since our founding over 10 years ago, we are fully vested in helping clients protect their information. Whether encrypting, tokenizing, or applying privacy methods, our platform secures the data behind the many operational systems that drive the day-to-day functions of business, as well as the analytical systems behind decision-making, personalized customer experiences, and AI modeling. All of this propels businesses forward by fueling innovation and giving clients a competitive advantage over less savvy competitors. This is a once in a lifetime opportunity to be part of a global team with a product that the business marketplace MUST have in place to do business.<br><br><strong>Responsibilities:<br><br></strong><ul><li>Work as part of our centralized UI/UX Team, bringing cross company branding and identity to a global level across our security product user interfaces. </li><li>Daily/weekly collaboration with teammates: engineer standups, team meetings, 1:1, development demos, sprint planning, UX design scrub meetings. </li><li>Experience with WCAG Accessibility Standards 2.2 Level AA. </li><li>Build, debug and maintain scalable, responsive, quality front-end solutions following best practices that function effectively across a variety of platforms and devices. </li><li>HTML5, CSS/SASS (or comparable CSS preprocessor), Bootstrap, JavaScript and/or JS Frameworks such as React.js/Jest. </li><li>On-hands experience with UX Designers. Knowledge of Figma (or alternative) bridging the gap between design and development. Implementing high-fidelity designs transformed into smooth, intuitive, easy-to-use journeys in our user interfaces. </li><li>Curiosity and enthusiasm for leveraging Ai development tools to streamline work and time management. </li><li>Comfortable using Git for version control and Webpack for automation tasks. </li><li>Experience working with design systems and understanding the fundamentals and importance of this ecosystem. </li><li>Testing and debugging at all stages of the SDLC. </li><li>Participate in code review and open-brainstorm sessions. </li><li>Help in facilitation of JIRA scrum tasks following agile methodologies. </li><li>Familiarity working with REST APIs and JSON. <br><br></li></ul><strong>Qualifications</strong>:<br><br><ul><li>5+ years of software engineering experience with a college degree and/or other demonstration of deep expertise in front-end engineering (profile encouraged). </li><li>High competence in verbal and written communication is essential. </li><li>Accountability and self-management to handle several projects at once across multiple code bases (we are a centralized team). </li><li>Expertise in computer science fundamentals. </li><li>Ability to multiply the abilities and skills of others. </li><li>Knowledge and experience implementing client-side security best practices. </li><li>Comfort with critical thinking and creative problem-solving. </li><li>Ability to articulate ideas clearly and collaborate effectively with cross-functional team members, including VPs, POs, PMs, QA, DevOps, Program Management, designers and back-end developers. <br><br></li></ul><strong>Preferred Qualifications:<br><br></strong><ul><li>Knowledge and experience working in data security. </li><li>Scrum master/project management experience. </li><li>Hands on experience working with RESTful APIs to OpenAPI 3.0 specification. </li><li>Enthusiasm to learn and understand natural language processing (NLP) for human-AI interaction. </li><li>Familiarity with cloud services like AWS, Google Cloud, or Azure. </li><li>Familiarity with building Jenkins pipelines and other CI/CD processes. </li><li>Inspection of code quality, scanning, monitoring, and security with Snyk and SonarQube. </li><li>Design and Prototyping. </li><li>Bonus points for working with Docker Containers<br><br></li></ul><strong>Key Personal Attributes:<br><br></strong><ul><li>Exceptional problem-solving skills. </li><li>Detail-oriented with an eye for aesthetics and usability. </li><li>Passion for staying up to date on the latest trends and/or new developments in the UIUX arena. </li><li>Understanding user needs and implementation of solutions. </li><li>Ability to efficiently manage workload and prioritize tasks to meet deadlines. </li><li>Understanding and sharing the feelings of others, fostering a supportive team environment is important. </li><li>Showing Integrity and always maintaining honest and ethical standards. </li><li>Flexibility in schedule working internationally across time zones. </li><li>Organization is top tier, tracking multiple tasks at once and keeping projects running as efficiently as possible and on time. </li><li>Analytical problem solving, able to make bite-size chunks out of complex issues. <br><br></li></ul><strong>Why Choose Protegrity:<br><br></strong><ul><li>Become a member of a leading Data Protection, Privacy and Security company during one of the best market opportunities to come along in a generation. </li><li>Competitive Compensation/Total Reward Packages that include:</li><li>Health Benefits (Health/Dental/Vision). </li><li>Paid Time Off (PTO). </li><li>401K. </li><li>Annual Bonus Incentives. </li><li>Short and Long Term Disability. </li><li>Work on global projects with diverse, energetic, team members who respect each other and celebrate differences. </li><li>Talent First Workforce. <br><br></li></ul><strong>Working Model:<br><br></strong><ul><li>This is a hybrid role and will require you to be in office 2-3 days a week. In certain circumstances the work model may change to accommodate business needs. <br><br></li></ul>Should you accept this position, you will be required to consent to and successfully complete a background investigation. This may include, subject to local laws, verification of extended education and additional criminal and civil checks.<br><br>We offer a competitive salary and comprehensive benefits with generous vacation and holiday time off. All employees are also provided access to ongoing learning &amp; development.<br><br>Ensuring a diverse and inclusive workplace is our priority. We are committed to an environment of acceptance where you are free to bring your full self to work. All qualified applicants and current employees will not be discriminated against on the basis of race, color, religion, sex, sexual orientation, gender identity, age, national origin, disability or veteran status.<br><br>Please reference Section 12: Supplemental Notice for Job Applicants in our Privacy Policy to inform you of the categories of personal information that we collect from individuals who inquire about and/or apply to work for Protegrity USA, Inc., or its parent company, subsidiaries or affiliates, and the purposes for which we use such personal information.<br><br>Compensation Range: $117K - $144K<br><br>
        </div>

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--more
        ml-0.5"" data-tracking-control-name=""public_jobs_show-more-html-btn"" aria-label=""Show more"" aria-expanded=""false"">
<!---->
        
            Show more
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/cyolgscd0imw2ldqppkrb84vo""></icon>
    </button>
  

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--less
        ml-0.5"" data-tracking-control-name=""public_jobs_show-less-html-btn"" aria-label=""Show less"" aria-expanded=""true"">
<!---->
        
            Show less
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/4chtt12k98xwnba1nimld2oyg""></icon>
    </button>
  
<!---->    </section>",,"{""min_amount"":117000,""max_amount"":144000,""currency"":""$"",""payment_period"":""yr""}","This range is provided by TM2 Group, LLC. Your actual pay will be based on your skills and experience — talk with your recruiter to learn more.","2025-07-23T16:59:36.754Z","{""url"":""https://www.linkedin.com/jobs/view/front-end-ui-engineer-734-at-tm2-group-llc-4267770308"",""discovery_input"":{""location"":""NY"",""keyword"":""UIUX"",""country"":""US"",""time_range"":null,""job_type"":null,""experience_level"":null,""remote"":null}}",,,,
"https://www.linkedin.com/jobs/view/intermediate-senior-uiux-designer-at-structurecraft-4252806368?_l=en","4252806368","Intermediate/Senior UIUX Designer","StructureCraft","766923","Seattle, WA","Job Title: Intermediate/Senior UIUX Designer Location: Remote with option to work out of offices in Seattle in US, Abbotsford, and Vancouver in Canada (must be able to work in East Coast or West Coast time zones ). Employment Type: Full-time Team: Branch team Reports to: Senior Product Manager About Branch Join Branch and help us invent the future of structural design software! Branch is a software startup within StructureCraft StructureCraft is an award-winning structural engineering, manufacturing, and construction company that specializes in timber structures. StructureCraft’s mission is to improve the built environment by demonstrating how a return to the traditional master-builder approach by integrating engineering with construction can produce better, more beautiful, and more efficient structures.. Branch aims to change how the building industry works by building a smart, modern, end-to-end design tool. What Is Our Product? Branch is a next-generation software platform for building structure design which cuts across traditional boundaries between design and manufacturing in AEC. Branch aims to seamlessly integrate real-time structural analysis and design, 3D modeling/detailing, and fabrication into a single, integrated design environment. Branch’s goal is to let all stakeholders – from owners and architects to engineers and CAM experts – collaborate in a unified design space. This not only facilitates a smoother workflow but also enables real-time cost and carbon feedback on design changes, dramatically speeding up decision-making and enhancing accuracy. Users can explore high-level design concepts, like building massing, and instantly understand detailed impacts, including shop drawings, bills of materials, and CNC files. Branch is highly interactive and hackable, allowing designers to explore options without limitations. We believe that integrating data from all aspects of the process allows for faster exploration and holistic, elegant designs. Our Opportunity Changing the building industry is an ambitious goal! Here are some reasons why we think we can pull it off. Great People: We have a team full of software experts who also understand and care about the building industry. Solid Product Foundations: In our industry, getting the details right is table stakes. Branch has already been used to detail and produce fabrication data for more than 600,000 square feet of mass timber. Access to Users and Expertise: Our siting within StructureCraft means we work closely with experts from every stage of the process, from architects to carpenters. Long-Term Vision: We’re a startup, but without the short-term focus that often comes with VC funding. We know we’re tackling a hard problem and we’re in it for the long haul! Having developed Branch quietly for use by StructureCraft, we’re looking to grow Branch to the next level. We need your help in changing the industry! About The Role We’re hiring a UI/UX Designer to lead the design vision and execution for the next phase of Branch Concept . You’ll be the primary designer for this product , which means you’ll have full ownership over the platform’s user experience—from early UX research through to final interface polish. This role goes beyond traditional 2D web design. You’ll be designing a hybrid interface that combines 2D UI components with 3D spatial interaction , enabling users to manipulate building models, compare structural systems, and explore cost and carbon data directly within a 3D environment. You’ll help define interaction patterns for selecting, editing, and navigating complex models—balancing precision, clarity, and performance in a building design context. If you enjoy working across multiple dimensions—literally and figuratively—and are excited to shape the future of digital design tools in architecture and engineering, we’d love to hear from you. This role is ideal for someone who thrives in an independent, self-directed environment and is excited to shape a complex product used by professionals across the architecture, engineering, and construction (AEC) industry. You’ll work closely with product, engineering, and business development teams to deliver powerful, intuitive design for 3D spatial interactions and data-rich workflows. What You'll Do Within 3 Months Deeply understand our product, workflows, and user base—especially around mass timber and AEC decision-making. Audit and refine our existing UI system and Figma libraries for consistency, clarity, and reusability. This will be for both 2D UI and 2D and 3D interactions related to creating 3D buildings in the geometry canvas. Help drive internal alignment and understanding of UIUX priorities and principles for the product. Collaborate with the team to develop wireframes and mockups—starting from low-fidelity concepts through to high-fidelity designs—to support product development and ensure design clarity. Partner with internal and external users to validate interaction patterns and interface clarity. By 6 Months Own and refine the design principes and design system that enable us to build out our product in the near term. Launch production-ready UI components across key flows including geometry editing, system switching, and export interfaces. Develop a UX framework for interacting with 3D geometry, balancing precision and usability to support complex modeling tasks. You'll define controls that make a powerful 3D system approachable and intuitive for architects, engineers, and other AEC users. Build out responsive design patterns that support clean UX across desktop and large-screen environments. Lay foundations for user onboarding, in-app help, and progressive disclosure strategies. Help develop the onboarding materials and curating the experience. Support developing materials for marketing efforts on a as-need basis. By 12 Months Own the full end-to-end UI/UX experience of Branch Concept. Establish and evolve a design system that supports modular development and future product expansion. Lead usability testing and feedback synthesis with external users (architects, engineers, suppliers). Contribute to product strategy and roadmap planning through the lens of user experience. Track and improve UX metrics (feature adoption, task time, error rate, user satisfaction). Must-haves What We're Looking For Familiarity with AEC workflows—especially concept or schematic design OR experience designing tools with 3D interaction (e.g. CAD, BIM, game engines, modeling tools). 4–6+ years designing modern web applications (SaaS, data, or technical tools preferred) Strong portfolio showing clean, intentional UI and UX thinking for complex workflows General 3D experience i.e. motion graphics and animations, or 3D web experiences. Comfortable working independently with a high level of ownership Expertise in Figma and prototyping workflows Strong communicator across product and engineering teams Based in or available to work within North American East or West Coast time zones Nice-to-haves Experience in structural engineering or working on tools used by engineers Experience designing for technical or spatial tools (e.g. Rhino, Revit, Unity, Unreal Interest in sustainable design, mass timber, and climate impact Salary Range (British Columbia, Canada): $80,000-130,000 CAD To apply please send your resume and application through the Branch application portal or reach out <NAME_EMAIL> Show more Show less",,"Mid-Senior level","Design, Art/Creative, and Information Technology","Full-time","Civil Engineering",,"https://ca.linkedin.com/company/structurecraft?trk=public_jobs_topcard-org-name","1 month ago",81,"{""location"":""WA"",""keyword"":""UIUX"",""country"":""US"",""time_range"":"""",""job_type"":"""",""experience_level"":"""",""remote"":"""",""company"":"""",""location_radius"":""""}","https://www.linkedin.com/jobs/view/externalApply/4252806368?url=https%3A%2F%2Fats%2Erippling%2Ecom%2Fstructurecraft%2Fjobs%2F5ac8690a-6b60-4158-aba1-f2e6e630529a%3FjobSite%3DLinkedIn&urlHash=dCfN","US","10331","https://media.licdn.com/dms/image/v2/C4E0BAQH4ZHFneTYUfQ/company-logo_100_100/company-logo_100_100/0/1630653354270/structurecraft_logo?e=**********&v=beta&t=zn_1t5HDb4yRMCob1lUd3u0Bf7pRSJYpGmV_Xa-yxG8","2025-06-23T16:59:36.999Z","{""name"":null,""title"":null,""url"":null}",true,"<section class=""show-more-less-html"" data-max-lines=""5"">
        <div class=""show-more-less-html__markup show-more-less-html__markup--clamp-after-5
            relative overflow-hidden"">
          <strong><strong>Job Title:</strong></strong> Intermediate/Senior UIUX Designer<br><br><strong><strong>Location:</strong></strong> Remote with option to work out of offices in Seattle in US, Abbotsford, and Vancouver in Canada (must be able to work in <strong><strong>East Coast or West Coast time zones</strong></strong>).<br><br><strong><strong>Employment Type:</strong></strong> Full-time<br><br><strong><strong>Team:</strong></strong> Branch team<br><br><strong><strong>Reports to:</strong></strong> Senior Product Manager<br><br><strong>About Branch<br><br></strong><strong><strong>Join Branch and help us invent the future of structural design software!<br><br></strong></strong><strong><strong>Branch is a software startup within StructureCraft<br><br></strong></strong>StructureCraft is an award-winning structural engineering, manufacturing, and construction company that specializes in timber structures. StructureCraft&#x2019;s mission is to improve the built environment by demonstrating how a return to the traditional master-builder approach by integrating engineering with construction can produce better, more beautiful, and more efficient structures..<br><br>Branch aims to change how the building industry works by building a smart, modern, end-to-end design tool.<br><br><strong><strong>What Is Our Product?<br><br></strong></strong>Branch is a next-generation software platform for building structure design which cuts across traditional boundaries between design and manufacturing in AEC. Branch aims to seamlessly integrate real-time structural analysis and design, 3D modeling/detailing, and fabrication into a single, integrated design environment.<br><br>Branch&#x2019;s goal is to let all stakeholders &#x2013; from owners and architects to engineers and CAM experts &#x2013; collaborate in a unified design space. This not only facilitates a smoother workflow but also enables real-time cost and carbon feedback on design changes, dramatically speeding up decision-making and enhancing accuracy. Users can explore high-level design concepts, like building massing, and instantly understand detailed impacts, including shop drawings, bills of materials, and CNC files. Branch is highly interactive and hackable, allowing designers to explore options without limitations. We believe that integrating data from all aspects of the process allows for faster exploration and holistic, elegant designs.<br><br><strong><strong>Our Opportunity<br><br></strong></strong>Changing the building industry is an ambitious goal! Here are some reasons why we think we can pull it off.<br><br><ul><li>Great People: We have a team full of software experts who also understand and care about the building industry.</li><li>Solid Product Foundations: In our industry, getting the details right is table stakes. Branch has already been used to detail and produce fabrication data for more than 600,000 square feet of mass timber.</li><li>Access to Users and Expertise: Our siting within StructureCraft means we work closely with experts from every stage of the process, from architects to carpenters.</li><li>Long-Term Vision: We&#x2019;re a startup, but without the short-term focus that often comes with VC funding. We know we&#x2019;re tackling a hard problem and we&#x2019;re in it for the long haul!<br><br></li></ul><strong><strong>Having developed Branch quietly for use by StructureCraft, we&#x2019;re looking to grow Branch to the next level. We need your help in changing the industry!<br><br></strong></strong><strong>About The Role<br><br></strong>We&#x2019;re hiring a <strong><strong>UI/UX Designer</strong></strong> to lead the design vision and execution for the next phase of <em><em>Branch Concept</em></em>. You&#x2019;ll be the <strong><strong>primary designer for this product</strong></strong>, which means you&#x2019;ll have full ownership over the platform&#x2019;s user experience&#x2014;from early UX research through to final interface polish. This role goes beyond traditional 2D web design. You&#x2019;ll be designing a <strong><strong>hybrid interface that combines 2D UI components with 3D spatial interaction</strong></strong>, enabling users to manipulate building models, compare structural systems, and explore cost and carbon data directly within a 3D environment. You&#x2019;ll help define interaction patterns for selecting, editing, and navigating complex models&#x2014;balancing precision, clarity, and performance in a building design context.<br><br>If you enjoy working across multiple dimensions&#x2014;literally and figuratively&#x2014;and are excited to shape the future of digital design tools in architecture and engineering, we&#x2019;d love to hear from you.<br><br>This role is ideal for someone who thrives in an independent, self-directed environment and is excited to shape a complex product used by professionals across the architecture, engineering, and construction (AEC) industry. You&#x2019;ll work closely with product, engineering, and business development teams to deliver powerful, intuitive design for 3D spatial interactions and data-rich workflows.<br><br><strong>What You&apos;ll Do<br><br></strong><strong><strong>Within 3 Months<br><br></strong></strong><ul><li>Deeply understand our product, workflows, and user base&#x2014;especially around mass timber and AEC decision-making.</li><li>Audit and refine our existing UI system and Figma libraries for consistency, clarity, and reusability. This will be for both 2D UI and 2D and 3D interactions related to creating 3D buildings in the geometry canvas.</li><li>Help drive internal alignment and understanding of UIUX priorities and principles for the product.</li><li>Collaborate with the team to develop wireframes and mockups&#x2014;starting from low-fidelity concepts through to high-fidelity designs&#x2014;to support product development and ensure design clarity.</li><li>Partner with internal and external users to validate interaction patterns and interface clarity.<br><br></li></ul><strong><strong>By 6 Months<br><br></strong></strong><ul><li>Own and refine the design principes and design system that enable us to build out our product in the near term.</li><li>Launch production-ready UI components across key flows including geometry editing, system switching, and export interfaces.</li><li>Develop a UX framework for interacting with 3D geometry, balancing precision and usability to support complex modeling tasks. You&apos;ll define controls that make a powerful 3D system approachable and intuitive for architects, engineers, and other AEC users.</li><li>Build out responsive design patterns that support clean UX across desktop and large-screen environments.</li><li>Lay foundations for user onboarding, in-app help, and progressive disclosure strategies. Help develop the onboarding materials and curating the experience.</li><li>Support developing materials for marketing efforts on a as-need basis. <br><br></li></ul><strong><strong>By 12 Months<br><br></strong></strong><ul><li>Own the full end-to-end UI/UX experience of Branch Concept.</li><li>Establish and evolve a design system that supports modular development and future product expansion.</li><li>Lead usability testing and feedback synthesis with external users (architects, engineers, suppliers).</li><li>Contribute to product strategy and roadmap planning through the lens of user experience.</li><li>Track and improve UX metrics (feature adoption, task time, error rate, user satisfaction).<br><br></li></ul><strong>Must-haves<br><br></strong><strong><strong>What We&apos;re Looking For<br><br></strong></strong><ul><li>Familiarity with AEC workflows&#x2014;especially concept or schematic design OR experience designing tools with 3D interaction (e.g. CAD, BIM, game engines, modeling tools).</li><li>4&#x2013;6+ years designing modern web applications (SaaS, data, or technical tools preferred)</li><li>Strong portfolio showing clean, intentional UI and UX thinking for complex workflows</li><li>General 3D experience i.e. motion graphics and animations, or 3D web experiences.</li><li>Comfortable working independently with a high level of ownership</li><li>Expertise in Figma and prototyping workflows</li><li>Strong communicator across product and engineering teams</li><li>Based in or available to work within North American East or West Coast time zones<br><br></li></ul><strong>Nice-to-haves<br><br></strong><ul><li>Experience in structural engineering or working on tools used by engineers</li><li>Experience designing for technical or spatial tools (e.g. Rhino, Revit, Unity, Unreal</li><li>Interest in sustainable design, mass timber, and climate impact<br><br></li></ul>Salary Range (British Columbia, Canada): $80,000-130,000 CAD<br><br>To apply please send your resume and application through the Branch application portal or reach out <NAME_EMAIL>
        </div>

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--more
        ml-0.5"" data-tracking-control-name=""public_jobs_show-more-html-btn"" aria-label=""Show more"" aria-expanded=""false"">
<!---->
        
            Show more
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/cyolgscd0imw2ldqppkrb84vo""></icon>
    </button>
  

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--less
        ml-0.5"" data-tracking-control-name=""public_jobs_show-less-html-btn"" aria-label=""Show less"" aria-expanded=""true"">
<!---->
        
            Show less
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/4chtt12k98xwnba1nimld2oyg""></icon>
    </button>
  
<!---->    </section>",,"{""min_amount"":null,""max_amount"":null,""currency"":null,""payment_period"":null}",,"2025-07-23T16:59:37.022Z","{""url"":""https://www.linkedin.com/jobs/view/intermediate-senior-uiux-designer-at-structurecraft-4252806368"",""discovery_input"":{""location"":""WA"",""keyword"":""UIUX"",""country"":""US"",""time_range"":null,""job_type"":null,""experience_level"":null,""remote"":null}}",,,,
"https://www.linkedin.com/jobs/view/art-supervisor-uiux-at-fingerpaint-group-4256828035?_l=en","4256828035","Art Supervisor UIUX","Fingerpaint Group","213096","Cedar Knolls, NJ","Description Position at Fingerpaint Marketing Imagine a workplace where your expertise is valued, your ideas spark change, and creativity fuels innovation. Fingerpaint Group is a vibrant blend of individuals, a full spectrum of life science solutions, and a culture that thrives on originality and a shared commitment to pushing the boundaries of what’s possible. Never Paint by Number isn't just our motto—it's how we work—collaboratively and creatively. If you're ready to bring your passion to a team that sees beyond limitations, we want to hear from you. We are seeking an Associate Creative Director (UI/UX) to lead the creative strategy and execution of user-centered digital experiences. This role blends hands-on design work with creative direction and team leadership. The ideal candidate is an experienced digital designer who can deliver exceptional web and mobile interfaces while mentoring a team and collaborating across disciplines. Key Responsibilities Lead the concepting, design, and execution of user experiences across digital products including websites, platforms, and apps. Create and maintain scalable design systems and component libraries. Translate complex problems into intuitive and elegant solutions. Collaborate with creative directors, UX strategists, developers, and project managers to ensure cohesive and high-quality delivery. Guide and mentor designers through reviews, feedback, and career development. Present design concepts and rationale to internal stakeholders and external clients. Use data, user research, and testing to inform design decisions and drive performance. Stay current with digital design trends, accessibility standards, and emerging technologies. Qualifications 7–10+ years of experience in digital design, with at least 3 years in a leadership role at an agency. A portfolio showcasing exceptional UI/UX design work across a range of digital experiences. Expertise in tools such as Figma, Adobe Creative Suite, and other key software and tools relevant to the industry. Strong understanding of responsive design, design systems, accessibility (WCAG), and user flows. Experience mentoring and managing designers. Excellent presentation, communication, and collaboration skills. Bachelor’s degree in Design, Human-Computer Interaction, or a related field preferred. Nice To Have Experience working in pharmaceutical marketing or working in regulated industries Familiarity with motion design, AR/VR, or front-end development (HTML/CSS/JavaScript) This role is largely remote, however it will require being local to the NJ/tristate area location. Fingerpaint Group provides equal employment [and affirmative action] opportunities to applicants and employees without regard to race, color, religion, sex, sexual orientation, gender identity, national origin, protected veteran status, or disability. Don’t meet every single requirement? That’s okay! Studies have shown that women and people of color are less likely to apply to jobs unless they meet every single qualification. At Fingerpaint, we are dedicated to building a diverse, inclusive, and authentic workplace, so if you’re excited about this role but your experience doesn’t align perfectly, we encourage you to apply anyway. You may be just the right candidate for this or other roles! Our team of qualified recruiters can match you with the right opportunity. Fingerpaint Group provides equal employment [and affirmative action] opportunities to all applicants and employees. We are proud to recruit qualified applicants without regard to race, color, religion, gender, age, ethnic or national origin, protected veteran status, physical or mental disability, sexual orientation, gender identity, marital status, or citizenship status. Show more Show less",,"Mid-Senior level","Design, Art/Creative, and Information Technology","Full-time","Advertising Services",,"https://www.linkedin.com/company/fingerpaintgroup?trk=public_jobs_topcard-org-name","1 week ago",28,"{""location"":""NJ"",""keyword"":""UIUX"",""country"":""US"",""time_range"":"""",""job_type"":"""",""experience_level"":"""",""remote"":"""",""company"":"""",""location_radius"":""""}","https://www.linkedin.com/jobs/view/externalApply/4256828035?url=https%3A%2F%2Fjobs%2Ejobvite%2Ecom%2Ffingerpaint%2Fjob%2FoIPfwfw2%3F__jvst%3DJob%2BBoard%26__jvsd%3DLinkedIn&urlHash=ruEL","US","10352","https://media.licdn.com/dms/image/v2/D4E0BAQHwnTyuquNdfQ/company-logo_100_100/B4EZVy0ZAmG0AU-/0/1741388091706/fingerpaintgroup_logo?e=**********&v=beta&t=muqnvh0_qzhQwP2tS0GVlgacd7ftSJ7N2clS2sKAi6M","2025-07-16T16:59:42.466Z","{""name"":null,""title"":null,""url"":null}",true,"<section class=""show-more-less-html"" data-max-lines=""5"">
        <div class=""show-more-less-html__markup show-more-less-html__markup--clamp-after-5
            relative overflow-hidden"">
          <strong>Description<br><br></strong><em>Position at Fingerpaint Marketing<br><br></em>Imagine a workplace where your expertise is valued, your ideas spark change, and creativity fuels innovation. Fingerpaint Group is a vibrant blend of individuals, a full spectrum of life science solutions, and a culture that thrives on originality and a shared commitment to pushing the boundaries of what&#x2019;s possible. Never Paint by Number isn&apos;t just our motto&#x2014;it&apos;s how we work&#x2014;collaboratively and creatively. If you&apos;re ready to bring your passion to a team that sees beyond limitations, we want to hear from you.<br><br>We are seeking an Associate Creative Director (UI/UX) to lead the creative strategy and execution of user-centered digital experiences. This role blends hands-on design work with creative direction and team leadership. The ideal candidate is an experienced digital designer who can deliver exceptional web and mobile interfaces while mentoring a team and collaborating across disciplines.<br><br><strong>Key Responsibilities<br><br></strong><ul><li>Lead the concepting, design, and execution of user experiences across digital products including websites, platforms, and apps.</li><li>Create and maintain scalable design systems and component libraries.</li><li>Translate complex problems into intuitive and elegant solutions.</li><li>Collaborate with creative directors, UX strategists, developers, and project managers to ensure cohesive and high-quality delivery.</li><li>Guide and mentor designers through reviews, feedback, and career development.</li><li>Present design concepts and rationale to internal stakeholders and external clients.</li><li>Use data, user research, and testing to inform design decisions and drive performance.</li><li>Stay current with digital design trends, accessibility standards, and emerging technologies.<br><br></li></ul><strong>Qualifications<br><br></strong><ul><li>7&#x2013;10+ years of experience in digital design, with at least 3 years in a leadership role at an agency.</li><li>A portfolio showcasing exceptional UI/UX design work across a range of digital experiences.</li><li>Expertise in tools such as Figma, Adobe Creative Suite, and other key software and tools relevant to the industry.</li><li>Strong understanding of responsive design, design systems, accessibility (WCAG), and user flows.</li><li>Experience mentoring and managing designers.</li><li>Excellent presentation, communication, and collaboration skills.</li><li>Bachelor&#x2019;s degree in Design, Human-Computer Interaction, or a related field preferred.<br><br></li></ul><strong>Nice To Have<br><br></strong><ul><li>Experience working in pharmaceutical marketing or working in regulated industries</li><li>Familiarity with motion design, AR/VR, or front-end development (HTML/CSS/JavaScript)<br><br></li></ul><em><strong>This role is largely remote, however it will require being local to the NJ/tristate area location. <br><br></strong></em>Fingerpaint Group provides equal employment [and affirmative action] opportunities to applicants and employees without regard to race, color, religion, sex, sexual orientation, gender identity, national origin, protected veteran status, or disability.<br><br>Don&#x2019;t meet every single requirement? That&#x2019;s okay! Studies have shown that women and people of color are less likely to apply to jobs unless they meet every single qualification. At Fingerpaint, we are dedicated to building a diverse, inclusive, and authentic workplace, so if you&#x2019;re excited about this role but your experience doesn&#x2019;t align perfectly, we encourage you to apply anyway. You may be just the right candidate for this or other roles! Our team of qualified recruiters can match you with the right opportunity. Fingerpaint Group provides equal employment [and affirmative action] opportunities to all applicants and employees. We are proud to recruit qualified applicants without regard to race, color, religion, gender, age, ethnic or national origin, protected veteran status, physical or mental disability, sexual orientation, gender identity, marital status, or citizenship status.
        </div>

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--more
        ml-0.5"" data-tracking-control-name=""public_jobs_show-more-html-btn"" aria-label=""Show more"" aria-expanded=""false"">
<!---->
        
            Show more
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/cyolgscd0imw2ldqppkrb84vo""></icon>
    </button>
  

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--less
        ml-0.5"" data-tracking-control-name=""public_jobs_show-less-html-btn"" aria-label=""Show less"" aria-expanded=""true"">
<!---->
        
            Show less
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/4chtt12k98xwnba1nimld2oyg""></icon>
    </button>
  
<!---->    </section>",,"{""min_amount"":null,""max_amount"":null,""currency"":null,""payment_period"":null}",,"2025-07-23T16:59:42.622Z","{""url"":""https://www.linkedin.com/jobs/view/art-supervisor-uiux-at-fingerpaint-group-4256828035"",""discovery_input"":{""location"":""NJ"",""keyword"":""UIUX"",""country"":""US"",""time_range"":null,""job_type"":null,""experience_level"":null,""remote"":null}}",,,,
"https://www.linkedin.com/jobs/view/director-digital-experience-at-dallas-college-**********?_l=en","**********","Director, Digital Experience","Dallas College","9382","Mesquite, TX","All Job Postings will close at 12:01 a.m. CT on the specified Closing Date. To view the job posting closing date please return to the search for jobs page. Weekly Work Hours 40 Compensation Range Administrator Range E06 Salary Minimum $95,000.00 Annually FLSA United States of America (Exempt) Position Type Administrator Job Summary This position will oversee High-quality content, crucial in meeting the increasing demands of our organization, enhancing our relationships with future students, current students, the community, and our employees, and ultimately, driving meaningful engagement and interactions. The ideal candidate will possess exceptional leadership skills, a deep understanding of marketing principles and trends, and a track record of success in strategic marketing management. Required Knowledge, Skills, And Abilities Deep understanding and proficiency in HTML and CSS with at least a basic understanding of JavaScript and front-end frameworks. Proven experience managing large-scale content management systems in complex organizations with multiple stakeholders, authors, and editors. Proven experience in marketing, with a focus on strategic marketing leadership and management within the digital space. Strong leadership and team management skills, with a track record of leading and developing high-performing teams. Deep understanding of user-centered web design techniques and strategies, online accessibility and current standards, principles of visual design, especially as it relates to web design and digital content, as well as information architecture and crafting user journeys. Experience in market research, including customer segmentation, competitive analysis, and market trends. Ability to take initiative, solve problems and resolve issues. Ability to see problems from a variety of angles and viewpoints. Strong analytical mindset with the ability to interpret data, analyze market trends, and draw actionable insights. Exceptional communication and interpersonal skills, with the ability to collaborate effectively with cross-functional teams and influence stakeholders at all levels. Proven ability to drive results, meet deadlines, and manage multiple priorities in a fast-paced environment. Strong business acumen and strategic thinking abilities. Demonstrated ability to navigate ambiguity, adapt to change, and thrive in a dynamic business environment. Physical Requirements Normal physical job functions performed within a standard office environment. Reasonable accommodation may be made for individuals with physical challenges to perform the essential duties and responsibilities. Minimum Requirements Bachelor’s degree or higher in marketing, digital media, user experience design, business, or related field. Master’s degree preferred. Official transcripts will be required. Five (5) years of experience in web design principles and best practices, including responsive design, user interface (UI) and user experience (UX) design, accessibility standards, and modern web technologies, with a proven track record of overseeing the creation and optimization of engaging, user-centric digital experiences. Three to Five (3 - 5) years of experience in marketing, communications or related field or equivalent related work experience Demonstrated experience in strategic planning, resource allocation, timeline management, project management and personnel supervision. Working knowledge of all traditional and non-traditional marketing media, including broadcast, radio, digital, paid, and social media campaigns and how the college website supports the broader marketing initiatives. Has managed team of five or more employees. Preference for candidates with experience in higher education or similar complex, multi-stakeholder environments. Bilingual or multilingual preferred. ***Will be subject to a criminal background check. Some positions may be subject to a fingerprint check. *** Key Responsibilities Content Generation: Develop and execute content strategies that drive engagement, conversion, and brand awareness. Digital Experience Strategy - Create seamless, engaging, and user-centric websites that strengthen our brand, drive customer satisfaction, and maximize digital performance. Website Management – Develops processes and guidelines for the management of the college websites. Responsible for effectively communicating the district’s messaging through the district website, which is the primary marketing tool for the college. Oversee the design, functionality, and user experience of our primary website to enhance engagement, drive conversions, and support our business objectives. Routinely reviews and recommends improvements to the layout and flow of the college’s websites, based on user behavior and analytics. Brand Alignment - Establishes and improves the visual style of the college’s website and digital content and ensures compliance with accessibility guidelines, web design parameters, branding guidelines, style guides, policies and procedures. Develops the patterns and guidelines of the online digital style guide in alignment with the college brand. User-Centered Design - Create innovative solutions with a strong focus on understanding and meeting the needs of our end users. Lead and conduct user research efforts and use empathy and data-driven insights to inform design decisions. Plays a pivotal role in crafting products and experiences that deliver exceptional usability and customer satisfaction. Exceptional Understanding Of And Ability To Apply User-centered design techniques and strategies Online accessibility and current standards Principles of visual design, especially as it relates to web design and digital content. Information architecture and user journey mapping SEO – Manages the online visibility and organic search ranking of our website. Implement strategies to optimize website content, structure, and technical elements, aligning them with search engine algorithms and user intent. Play a key role in driving increased organic traffic and ensuring our digital presence effectively reaches our target audiences. Collaboration - Collaborate with cross-functional teams, including marketing activation, creative services, and communications to ensure the website reflects the brand's messaging, values, and offerings. Collaborate with developers and IT teams to ensure the website is technically sound, responsive, and secure, and address any technical issues promptly. Compliance – Works with stakeholders across the college to ensure the website and marketing platforms meet all federal, state, local and college guidelines, including accessibility of the college’s website. Strategic Planning - Develop long-term vision and goals for the organization, in alignment with its mission and values. By formulating actionable plans and initiatives for the department, you will play a key role in guiding the College’s growth and success. Strategy and Analytics - Utilize data-driven insights and analytical methodologies to inform and guide key decision-making processes within the organization. Project Management - Plan, execute and oversee the successful completion of various projects within the organization. Defining project objectives, create detailed project plans and allocate resources effectively to ensure project goals are met on time and within budget. Process Management - Through data analysis, collaboration with cross-functional teams and the implementation of best practices, drive continuous improvement initiatives that lead to sustained organizational growth and success. Streamline marketing operations, enhance efficiency, and eliminate inefficiencies to achieve increased productivity, increased employee satisfaction and increased client satisfaction. Budget Management – Monitor budgets for various areas and projects, ensuring adherence to college guidelines and targets. Client Relations - Build and maintain strong, positive relationships with internal clients. Responsibilities include being the primary point of contact for clients, addressing their inquiries and concerns promptly, and always ensuring exceptional customer service. Team Development & Management – Build and lead a world class digital web and marketing team. Instill best practices, ensure the right people are in the right roles, and inspire teams (both internal and external agency partners) to achieve what they never thought possible. Nurture and empower employees to reach their full potential within the organization. Identify individual strengths and areas for growth, create personalized development plans and provide coaching and mentorship to foster professional growth with the goal of contributing to the overall success and retention of top talent. Agency Management - This position will be responsible for enhancing the Marketing and program execution, and organizational Marketing capabilities, as a critical driver of enhanced, more tightly integrated Marketing & Communication, across the entire organization. This includes assessing the collection of agencies, suppliers, and consultants who are engaged with Dallas College, to ensure the Company is getting the expertise it needs to maximize Marketing impact. Completes required Dallas College Professional Development training hours per academic year. Performs other duties as assigned. The intent of this job description is to provide a representative summary of the major duties and responsibilities performed by incumbents of this job and shall not be construed as a declaration of the total of the specific duties and responsibilities of any particular position. Incumbents may be directed to perform job-related tasks other than those specifically presented in this description. Position requires regular and predictable attendance. About Us Since 1965, Dallas College, formerly Dallas County Community College District, has served more than three (3) million students. Comprising seven campuses located around the Dallas/Fort Worth area, we are one of the largest community college systems in the state of Texas. We strive to be a leader in the community college space, placing students at the center of everything we do. Dallas College is committed to cultivating an environment of opportunity and belonging for all students and employees. We recognize that the Dallas College workforce, and the diverse talent that stems from it, is directly linked to our success. We are part of an equal opportunity system that provides education and employment opportunities without discrimination on the basis of any protected attribute, including race, color, religion, national origin, sex, disability, age, sexual orientation, gender identity or gender expression, veteran status, pregnancy or any other basis protected under applicable law. In accordance with applicable law, Dallas College will make reasonable accommodations for applicants and employees’ religious practices and beliefs, as well as any mental health or physical disability needs. Applications Deadline August 1, 2025 Show more Show less",,"Director","Marketing and Sales","Full-time","Higher Education",,"https://www.linkedin.com/school/dallascollege/?trk=public_jobs_topcard-org-name","1 week ago",36,"{""location"":""TX"",""keyword"":""UIUX"",""country"":""US"",""time_range"":"""",""job_type"":"""",""experience_level"":"""",""remote"":"""",""company"":"""",""location_radius"":""""}","https://www.linkedin.com/jobs/view/externalApply/**********?url=https%3A%2F%2Fcareers%2Edallascollege%2Eedu%2Fus%2Fen%2Fjob%2FDAJDCLUSR9778EXTERNALENUS%2FDirector-Digital-Experience%3Futm_source%3Dlinkedin%26utm_medium%3Dphenom-feeds&urlHash=-5oT","US","3212","https://media.licdn.com/dms/image/v2/C4E0BAQFwk6Gu498WhQ/company-logo_100_100/company-logo_100_100/0/1630615950400/dcccd_logo?e=**********&v=beta&t=JBH0_X1EOQpxcRZjSUBuGJlcx5reOmNI_PyXHJrQjBE","2025-07-16T16:59:50.187Z","{""name"":null,""title"":null,""url"":null}",true,"<section class=""show-more-less-html"" data-max-lines=""5"">
        <div class=""show-more-less-html__markup show-more-less-html__markup--clamp-after-5
            relative overflow-hidden"">
          All Job Postings will close at 12:01 a.m. CT on the specified Closing Date. <strong>To view the job posting closing date please return to the search for jobs page.<br><br></strong><strong>Weekly Work Hours<br><br></strong>40<br><br><strong>Compensation Range<br><br></strong>Administrator Range E06<br><br><strong>Salary Minimum<br><br></strong>$95,000.00 Annually<br><br><strong>FLSA<br><br></strong>United States of America (Exempt)<br><br><strong>Position Type<br><br></strong>Administrator<br><br><strong>Job Summary<br><br></strong>This position will oversee High-quality content, crucial in meeting the increasing demands of our organization, enhancing our relationships with future students, current students, the community, and our employees, and ultimately, driving meaningful engagement and interactions. The ideal candidate will possess exceptional leadership skills, a deep understanding of marketing principles and trends, and a track record of success in strategic marketing management.<br><br><strong>Required Knowledge, Skills, And Abilities<br><br></strong><ul><li>Deep understanding and proficiency in HTML and CSS with at least a basic understanding of JavaScript and front-end frameworks.</li><li>Proven experience managing large-scale content management systems in complex organizations with multiple stakeholders, authors, and editors.</li><li>Proven experience in marketing, with a focus on strategic marketing leadership and management within the digital space.</li><li>Strong leadership and team management skills, with a track record of leading and developing high-performing teams. Deep understanding of user-centered web design techniques and strategies, online accessibility and current standards, principles of visual design, especially as it relates to web design and digital content, as well as information architecture and crafting user journeys.</li><li>Experience in market research, including customer segmentation, competitive analysis, and market trends.</li><li>Ability to take initiative, solve problems and resolve issues.</li><li>Ability to see problems from a variety of angles and viewpoints.</li><li>Strong analytical mindset with the ability to interpret data, analyze market trends, and draw actionable insights.</li><li>Exceptional communication and interpersonal skills, with the ability to collaborate effectively with cross-functional teams and influence stakeholders at all levels.</li><li>Proven ability to drive results, meet deadlines, and manage multiple priorities in a fast-paced environment.</li><li>Strong business acumen and strategic thinking abilities.</li><li>Demonstrated ability to navigate ambiguity, adapt to change, and thrive in a dynamic business environment.<br><br></li></ul><strong>Physical Requirements<br><br></strong>Normal physical job functions performed within a standard office environment. Reasonable accommodation may be made for individuals with physical challenges to perform the essential duties and responsibilities.<br><br><strong>Minimum Requirements<br><br></strong><ul><li>Bachelor&#x2019;s degree or higher in marketing, digital media, user experience design, business, or related field. Master&#x2019;s degree preferred. Official transcripts will be required.</li><li>Five (5) years of experience in web design principles and best practices, including responsive design, user interface (UI) and user experience (UX) design, accessibility standards, and modern web technologies, with a proven track record of overseeing the creation and optimization of engaging, user-centric digital experiences.</li><li>Three to Five (3 - 5) years of experience in marketing, communications or related field or equivalent related work experience</li><li>Demonstrated experience in strategic planning, resource allocation, timeline management, project management and personnel supervision.</li><li>Working knowledge of all traditional and non-traditional marketing media, including broadcast, radio, digital, paid, and social media campaigns and how the college website supports the broader marketing initiatives.</li><li>Has managed team of five or more employees.</li><li>Preference for candidates with experience in higher education or similar complex, multi-stakeholder environments.</li><li>Bilingual or multilingual preferred.<br><br></li></ul>***Will be subject to a criminal background check. Some positions may be subject to a fingerprint check. ***<br><br><strong>Key Responsibilities<br><br></strong><ul><li>Content Generation: Develop and execute content strategies that drive engagement, conversion, and brand awareness.</li><li>Digital Experience Strategy - Create seamless, engaging, and user-centric websites that strengthen our brand, drive customer satisfaction, and maximize digital performance.</li><li>Website Management &#x2013; Develops processes and guidelines for the management of the college websites. Responsible for effectively communicating the district&#x2019;s messaging through the district website, which is the primary marketing tool for the college. Oversee the design, functionality, and user experience of our primary website to enhance engagement, drive conversions, and support our business objectives. Routinely reviews and recommends improvements to the layout and flow of the college&#x2019;s websites, based on user behavior and analytics.</li><li>Brand Alignment - Establishes and improves the visual style of the college&#x2019;s website and digital content and ensures compliance with accessibility guidelines, web design parameters, branding guidelines, style guides, policies and procedures. Develops the patterns and guidelines of the online digital style guide in alignment with the college brand.</li><li>User-Centered Design - Create innovative solutions with a strong focus on understanding and meeting the needs of our end users. Lead and conduct user research efforts and use empathy and data-driven insights to inform design decisions. Plays a pivotal role in crafting products and experiences that deliver exceptional usability and customer satisfaction.<br><br></li></ul><strong>Exceptional Understanding Of And Ability To Apply<br><br></strong><ul><li>User-centered design techniques and strategies</li><li>Online accessibility and current standards</li><li>Principles of visual design, especially as it relates to web design and digital content.</li><li>Information architecture and user journey mapping</li><li>SEO &#x2013; Manages the online visibility and organic search ranking of our website. Implement strategies to optimize website content, structure, and technical elements, aligning them with search engine algorithms and user intent. Play a key role in driving increased organic traffic and ensuring our digital presence effectively reaches our target audiences.</li><li>Collaboration - Collaborate with cross-functional teams, including marketing activation, creative services, and communications to ensure the website reflects the brand&apos;s messaging, values, and offerings. Collaborate with developers and IT teams to ensure the website is technically sound, responsive, and secure, and address any technical issues promptly.</li><li>Compliance &#x2013; Works with stakeholders across the college to ensure the website and marketing platforms meet all federal, state, local and college guidelines, including accessibility of the college&#x2019;s website.</li><li>Strategic Planning - Develop long-term vision and goals for the organization, in alignment with its mission and values. By formulating actionable plans and initiatives for the department, you will play a key role in guiding the College&#x2019;s growth and success.</li><li>Strategy and Analytics - Utilize data-driven insights and analytical methodologies to inform and guide key decision-making processes within the organization.</li><li>Project Management - Plan, execute and oversee the successful completion of various projects within the organization. Defining project objectives, create detailed project plans and allocate resources effectively to ensure project goals are met on time and within budget.</li><li>Process Management - Through data analysis, collaboration with cross-functional teams and the implementation of best practices, drive continuous improvement initiatives that lead to sustained organizational growth and success. Streamline marketing operations, enhance efficiency, and eliminate inefficiencies to achieve increased productivity, increased employee satisfaction and increased client satisfaction.</li><li>Budget Management &#x2013; Monitor budgets for various areas and projects, ensuring adherence to college guidelines and targets.</li><li>Client Relations - Build and maintain strong, positive relationships with internal clients. Responsibilities include being the primary point of contact for clients, addressing their inquiries and concerns promptly, and always ensuring exceptional customer service.</li><li>Team Development &amp; Management &#x2013; Build and lead a world class digital web and marketing team. Instill best practices, ensure the right people are in the right roles, and inspire teams (both internal and external agency partners) to achieve what they never thought possible. Nurture and empower employees to reach their full potential within the organization. Identify individual strengths and areas for growth, create personalized development plans and provide coaching and mentorship to foster professional growth with the goal of contributing to the overall success and retention of top talent.</li><li>Agency Management - This position will be responsible for enhancing the Marketing and program execution, and organizational Marketing capabilities, as a critical driver of enhanced, more tightly integrated Marketing &amp; Communication, across the entire organization. This includes assessing the collection of agencies, suppliers, and consultants who are engaged with Dallas College, to ensure the Company is getting the expertise it needs to maximize Marketing impact.</li><li>Completes required Dallas College Professional Development training hours per academic year.</li><li>Performs other duties as assigned.<br><br></li></ul><em>The intent of this job description is to provide a representative summary of the major duties and responsibilities performed by incumbents of this job and shall not be construed as a declaration of the total of the specific duties and responsibilities of any particular position. Incumbents may be directed to perform job-related tasks other than those specifically presented in this description. Position requires regular and predictable attendance.<br><br></em><strong>About Us<br><br></strong>Since 1965, Dallas College, formerly Dallas County Community College District, has served more than three (3) million students. Comprising seven campuses located around the Dallas/Fort Worth area, we are one of the largest community college systems in the state of Texas. We strive to be a leader in the community college space, placing students at the center of everything we do.<br><br>Dallas College is committed to cultivating an environment of opportunity and belonging for all students and employees. We recognize that the Dallas College workforce, and the diverse talent that stems from it, is directly linked to our success. We are part of an equal opportunity system that provides education and employment opportunities without discrimination on the basis of any protected attribute, including race, color, religion, national origin, sex, disability, age, sexual orientation, gender identity or gender expression,<br><br>veteran status, pregnancy or any other basis protected under applicable law. In accordance with applicable law, Dallas College will make reasonable accommodations for applicants and employees&#x2019; religious practices and beliefs, as well as any mental health or physical disability needs.<br><br><strong>Applications Deadline<br><br></strong>August 1, 2025
        </div>

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--more
        ml-0.5"" data-tracking-control-name=""public_jobs_show-more-html-btn"" aria-label=""Show more"" aria-expanded=""false"">
<!---->
        
            Show more
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/cyolgscd0imw2ldqppkrb84vo""></icon>
    </button>
  

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--less
        ml-0.5"" data-tracking-control-name=""public_jobs_show-less-html-btn"" aria-label=""Show less"" aria-expanded=""true"">
<!---->
        
            Show less
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/4chtt12k98xwnba1nimld2oyg""></icon>
    </button>
  
<!---->    </section>",,"{""min_amount"":null,""max_amount"":null,""currency"":null,""payment_period"":null}",,"2025-07-23T16:59:50.221Z","{""url"":""https://www.linkedin.com/jobs/view/director-digital-experience-at-dallas-college-**********"",""discovery_input"":{""location"":""TX"",""keyword"":""UIUX"",""country"":""US"",""time_range"":null,""job_type"":null,""experience_level"":null,""remote"":null}}",,,,
"https://www.linkedin.com/jobs/view/ux-visual-designer-at-ptr-global-4267266791?_l=en","4267266791","UX / Visual Designer","PTR Global","104085226","Austin, TX","Tasks and Responsibilities DUTIES: Collaborate closely with product owners, process analysts, internal UIUX designers, and solution architects to create innovative and engaging user experiences for various personas, for multiple capabilities on the transformation program roadmap. Design user interface mockups, user flows, and prototypes. Create design specifications, other documentation and sample codes for engineering implementation. Design UI and visual design language while adhering to existing design standards. Create journey maps, flow diagrams, wireframes, mockups, style guides, prototypes, site maps, and other design artifacts to communicate design approach and to gain alignment across stakeholders. Gather input from stakeholders and draw on your extensive UX design knowledge and industry experience to distill project requirements into intuitive design that addresses clearly defined user needs. Present UX and visual designs to team and management while clearly articulating design rationale. Make reasonable tradeoffs with considerations on technology limitations. Key Qualifications 5+ years of UX experience. Extensive expertise in a broad range of UX skills, including interaction design, visual design, branding, UX research, and more. Mastery of Sketch, Photoshop, Illustrator, and other modern design and prototyping tools. Excellent collaboration skills cross-functionally with internal and external team to deliver a quality product while advocating for the user every step of the way. Expert problem-solving skills and creativity to find clear solutions that help users achieve their goals. Experience with user research, conducting user testing, and distilling feedback into meaningful design improvements. Experience designing for web and iOS, including responsive design. Detail-oriented, down to the pixel. Ability to adapt to a constantly changing environment and tight timelines. Familiar with Agile development approach. High energy and a positive attitude. A strong portfolio of work that demonstrates the above Highly skilled in Sketch, Figma, Keynote and other design tools. Excellent communication and presentation skills; both written and verbal. Familiarity with cloud-based CRM technology and platform is a plus. Fluent in HTML, CSS and JavaScript is a plus. Hands-on HTML, CSS and JavaScript programming skills with a good understanding of these core languages’ concepts is a plus. Experience with at least one modern MVC based JavaScript app framework is plus. Show more Show less",,"Mid-Senior level","Design","Contract","IT Services and IT Consulting",,"https://www.linkedin.com/company/ptrglobal?trk=public_jobs_topcard-org-name","1 week ago",129,"{""location"":""TX"",""keyword"":""UIUX"",""country"":""US"",""time_range"":"""",""job_type"":"""",""experience_level"":"""",""remote"":"""",""company"":"""",""location_radius"":""""}",,"US","3114","https://media.licdn.com/dms/image/v2/D560BAQExn1rRDDf8dg/company-logo_100_100/company-logo_100_100/0/*************/ptrglobal_logo?e=**********&v=beta&t=RKCY7yVRCsHuYs58mqQ2N08N9a717kUHilq9_hyekzA","2025-07-16T16:59:54.749Z","{""name"":""Matt Gantt"",""title"":""Senior Account / Delivery Manager at PTR Global"",""url"":""https://www.linkedin.com/in/mattgantt""}",true,"<section class=""show-more-less-html"" data-max-lines=""5"">
        <div class=""show-more-less-html__markup show-more-less-html__markup--clamp-after-5
            relative overflow-hidden"">
          <p><strong>Tasks and Responsibilities</strong></p><p><br></p><p>DUTIES:</p><p><br></p><ul><li>Collaborate closely with product owners, process analysts, internal UIUX designers, and solution architects to create innovative and engaging user experiences for various personas, for multiple capabilities on the transformation program roadmap.</li><li>Design user interface mockups, user flows, and prototypes.</li><li>Create design specifications, other documentation and sample codes for engineering implementation.</li><li>Design UI and visual design language while adhering to existing design standards.</li><li>Create journey maps, flow diagrams, wireframes, mockups, style guides, prototypes, site maps, and other design artifacts to communicate design approach and to gain alignment across stakeholders.</li><li>Gather input from stakeholders and draw on your extensive UX design knowledge and industry experience to distill project requirements into intuitive design that addresses clearly defined user needs.</li><li>Present UX and visual designs to team and management while clearly articulating design rationale. Make reasonable tradeoffs with considerations on technology limitations.</li></ul><p><br></p><p>Key Qualifications</p><p><br></p><ul><li>5+ years of UX experience.</li><li>Extensive expertise in a broad range of UX skills, including interaction design, visual design, branding, UX research, and more.</li><li>Mastery of Sketch, Photoshop, Illustrator, and other modern design and prototyping tools.</li><li>Excellent collaboration skills cross-functionally with internal and external team to deliver a quality product while advocating for the user every step of the way.</li><li>Expert problem-solving skills and creativity to find clear solutions that help users achieve their goals.</li><li>Experience with user research, conducting user testing, and distilling feedback into meaningful design improvements.</li><li>Experience designing for web and iOS, including responsive design.</li><li>Detail-oriented, down to the pixel.</li><li>Ability to adapt to a constantly changing environment and tight timelines.</li><li>Familiar with Agile development approach.</li><li>High energy and a positive attitude.</li><li>A strong portfolio of work that demonstrates the above</li><li>Highly skilled in Sketch, Figma, Keynote and other design tools.</li><li>Excellent communication and presentation skills; both written and verbal.</li><li>Familiarity with cloud-based CRM technology and platform is a plus.</li><li>Fluent in HTML, CSS and JavaScript is a plus.</li><li>Hands-on HTML, CSS and JavaScript programming skills with a good understanding of these core languages&#x2019; concepts is a plus.</li><li>Experience with at least one modern MVC based JavaScript app framework is plus.</li></ul><p></p>
        </div>

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--more
        ml-0.5"" data-tracking-control-name=""public_jobs_show-more-html-btn"" aria-label=""Show more"" aria-expanded=""false"">
<!---->
        
            Show more
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/cyolgscd0imw2ldqppkrb84vo""></icon>
    </button>
  

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--less
        ml-0.5"" data-tracking-control-name=""public_jobs_show-less-html-btn"" aria-label=""Show less"" aria-expanded=""true"">
<!---->
        
            Show less
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/4chtt12k98xwnba1nimld2oyg""></icon>
    </button>
  
<!---->    </section>",,"{""min_amount"":null,""max_amount"":null,""currency"":null,""payment_period"":null}",,"2025-07-23T16:59:54.771Z","{""url"":""https://www.linkedin.com/jobs/view/ux-visual-designer-at-ptr-global-4267266791"",""discovery_input"":{""location"":""TX"",""keyword"":""UIUX"",""country"":""US"",""time_range"":null,""job_type"":null,""experience_level"":null,""remote"":null}}",,,,
"https://www.linkedin.com/jobs/view/principal-network-operations-site-reliability-systems-engineer-at-hpe-aruba-networking-4233345923?_l=en","4233345923","Principal Network Operations Site Reliability Systems Engineer","HPE Aruba Networking","162533","Spring, TX","This role has been designed as ‘Hybrid’ with an expectation that you will work on average 2 days per week from an HPE office. Who We Are: Hewlett Packard Enterprise is the global edge-to-cloud company advancing the way people live and work. We help companies connect, protect, analyze, and act on their data and applications wherever they live, from edge to cloud, so they can turn insights into outcomes at the speed required to thrive in today’s complex world. Our culture thrives on finding new and better ways to accelerate what’s next. We know varied backgrounds are valued and succeed here. We have the flexibility to manage our work and personal needs. We make bold moves, together, and are a force for good. If you are looking to stretch and grow your career our culture will embrace you. Open up opportunities with HPE. Job Description: Who We Are: Hewlett Packard Enterprise is the global edge-to-cloud company advancing the way people live and work. We help companies connect, protect, analyze, and act on their data and applications wherever they live, from edge to cloud, so they can turn insights into outcomes at the speed required to thrive in today’s complex world. Our culture thrives on finding new and better ways to accelerate what’s next. We know varied backgrounds are valued and succeed here. We have the flexibility to manage our work and personal needs. We make bold moves, together, and are a force for good. If you are looking to stretch and grow your career our culture will embrace you. Open up opportunities with HPE. The primary work location is as listed; however, remote work options will be considered. Job Description: Contributions have a visible technical impact on a services product. Apply in-depth professional knowledge and innovative ideas to solve complex network and software problems. Visible contributions improve service performance and network availability, achieve cost reductions, or satisfy current and future unmet customer needs. Recognized internal authority on key technology area applying innovative principles and ideas. Provides technical leadership for significant project/program work. Leads or participates in cross-functional initiatives and contributes to mentorship and knowledge sharing across the organization. Responsibilities: Develop strategies and implement plans to incorporate SRE concepts into network, tool, and process designs and leads execution of those strategies and plans Evaluates LAN, WLAN, SD-WAN, AAA, Private 5G, and other network designs for fit-for-use criteria, and designs prototype analysis tools to facilitate rapid iteration of network delivery service enhancements Identifies and engineers new ways to leverage data from multiple platforms to identify network performance trends and detect anomalies Prototypes machine learning anomaly detection, event signature identification, and trend identification Automates common incident management and problem management procedures Develops organization-wide architectures, methodologies, and prototypes for software systems design and development across multiple platforms and organizations within the Global Business Unit. Identifies and evaluates new technologies and innovations for alignment with technology roadmap and business value; creates plans for prototyping and prototype iteration. Reviews and evaluates designs and project activities for compliance with development guidelines and standards; provides tangible feedback to improve product quality and mitigate failure risk. Education And Experience Required: Bachelor’s or master’s degree in computer science, Computer Engineering, Information Systems, or equivalent. Typically, 10+ years’ experience. Knowledge And Skills: Experience with cloud platforms Experience with software development languages for console and web-based applications Experience in User Interface (UI/UX) design Understanding of and experience with common network infrastructure devices such as switches, routers, access points, authentication, authorization, and accounting systems and protocols, and network management utilities Experience with network monitoring protocols Ability to design and implement relational database solutions, time-series databases, and NoSQL database solutions Excellent analytical and problem-solving skills Experience in the overall architecture of software systems for products and solutions Designing and integrating software systems running on multiple platform types into overall architecture Evaluating and selecting forms and processes for software systems testing and methodology, including writing and execution of test plans, debugging, and testing scripts and tools History of innovation with multiple patents or deployed solutions in the field of software design Excellent written and verbal communication skills; mastery of English language; Ability to effectively communicate product architectures and design proposals and negotiate options at business unit and executive levels #unitedstates LI-Remote #network #relationaldatabase #SRE #sitereliability #architecture #10years+ #prototype #UIUX #design Additional Skills: Cloud Architectures, Cross Domain Knowledge, Design Thinking, Development Fundamentals, DevOps, Distributed Computing, Microservices Fluency, Full Stack Development, Security-First Mindset, User Experience (UX) What We Can Offer You: Health & Wellbeing We strive to provide our team members and their loved ones with a comprehensive suite of benefits that supports their physical, financial and emotional wellbeing. Personal & Professional Development We also invest in your career because the better you are, the better we all are. We have specific programs catered to helping you reach any career goals you have — whether you want to become a knowledge expert in your field or apply your skills to another division. Unconditional Inclusion We are unconditionally inclusive in the way we work and celebrate individual uniqueness. We know varied backgrounds are valued and succeed here. We have the flexibility to manage our work and personal needs. We make bold moves, together, and are a force for good. Let's Stay Connected: Follow @HPECareers on Instagram to see the latest on people, culture and tech at HPE. #unitedstates #aruba Job: Engineering Job Level: TCP_05 States with Pay Range Requirement The expected salary/wage range for a U.S.-based hire filling this position is provided below. Actual offer may vary from this range based upon geographic location, work experience, education/training, and/or skill level. If this is a sales role, then the listed salary range reflects combined base salary and target-level sales compensation pay. If this is a non-sales role, then the listed salary range reflects base salary only. Variable incentives may also be offered. Information about employee benefits offered can be found at https://myhperewards.com/main/new-hire-enrollment.html. USD Annual Salary: $115,500.00 - $266,000.00 HPE is an Equal Employment Opportunity/ Veterans/Disabled/LGBT employer. We do not discriminate on the basis of race, gender, or any other protected category, and all decisions we make are made on the basis of qualifications, merit, and business need. Our goal is to be one global team that is representative of our customers, in an inclusive environment where we can continue to innovate and grow together. Please click here: Equal Employment Opportunity. Hewlett Packard Enterprise is EEO Protected Veteran/ Individual with Disabilities. HPE will comply with all applicable laws related to employer use of arrest and conviction records, including laws requiring employers to consider for employment qualified applicants with criminal histories. Show more Show less",,"Not Applicable","Engineering and Information Technology","Full-time","IT Services and IT Consulting","$115,500.00/yr - $266,000.00/yr","https://www.linkedin.com/company/aruba-a-hewlett-packard-enterprise-company?trk=public_jobs_topcard-org-name","2 months ago",25,"{""location"":""TX"",""keyword"":""UIUX"",""country"":""US"",""time_range"":"""",""job_type"":"""",""experience_level"":"""",""remote"":"""",""company"":"""",""location_radius"":""""}","https://www.linkedin.com/jobs/view/externalApply/4233345923?url=https%3A%2F%2Fcareers%2Ehpe%2Ecom%2Fus%2Fen%2Fjob%2F1187888%2FPrincipal-Network-Operations-Site-Reliability-Systems-Engineer%3Futm_source%3Dlinkedin&urlHash=X0hS","US","22848","https://media.licdn.com/dms/image/v2/D4E0BAQGDnmjOQlmgtg/company-logo_100_100/B4EZdk_QyCHsAU-/0/1749745995186/aruba_a_hewlett_packard_enterprise_company_logo?e=**********&v=beta&t=Jew_bDgyEZD6bjlkWumu32nNXI8BFKNx7cbTlFGQPKE","2025-05-24T16:59:58.166Z","{""name"":null,""title"":null,""url"":null}",true,"<section class=""show-more-less-html"" data-max-lines=""5"">
        <div class=""show-more-less-html__markup show-more-less-html__markup--clamp-after-5
            relative overflow-hidden"">
          This role has been designed as &#x2018;Hybrid&#x2019; with an expectation that you will work on average 2 days per week from an HPE office.<br><br><strong><strong>Who We Are:<br><br></strong></strong>Hewlett Packard Enterprise is the global edge-to-cloud company advancing the way people live and work. We help companies connect, protect, analyze, and act on their data and applications wherever they live, from edge to cloud, so they can turn insights into outcomes at the speed required to thrive in today&#x2019;s complex world. Our culture thrives on finding new and better ways to accelerate what&#x2019;s next. We know varied backgrounds are valued and succeed here. We have the flexibility to manage our work and personal needs. We make bold moves, together, and are a force for good. If you are looking to stretch and grow your career our culture will embrace you. Open up opportunities with HPE.<br><br><strong><strong>Job Description:<br><br></strong></strong><strong>Who We Are:<br><br></strong>Hewlett Packard Enterprise is the global edge-to-cloud company advancing the way people live and work. We help companies connect, protect, analyze, and act on their data and applications wherever they live, from edge to cloud, so they can turn insights into outcomes at the speed required to thrive in today&#x2019;s complex world. Our culture thrives on finding new and better ways to accelerate what&#x2019;s next. We know varied backgrounds are valued and succeed here. We have the flexibility to manage our work and personal needs. We make bold moves, together, and are a force for good. If you are looking to stretch and grow your career our culture will embrace you. Open up opportunities with HPE.<br><br><strong>The primary work location is as listed; however, remote work options will be considered. <br><br></strong><strong><strong>Job Description:<br><br></strong></strong>Contributions have a visible technical impact on a services product. Apply in-depth professional knowledge and innovative ideas to solve complex network and software problems. Visible contributions improve service performance and network availability, achieve cost reductions, or satisfy current and future unmet customer needs. Recognized internal authority on key technology area applying innovative principles and ideas. Provides technical leadership for significant project/program work. Leads or participates in cross-functional initiatives and contributes to mentorship and knowledge sharing across the organization.<br><br><strong><strong>Responsibilities: <br><br></strong></strong><ul><li>Develop strategies and implement plans to incorporate SRE concepts into network, tool, and process designs and leads execution of those strategies and plans</li><li>Evaluates LAN, WLAN, SD-WAN, AAA, Private 5G, and other network designs for fit-for-use criteria, and designs prototype analysis tools to facilitate rapid iteration of network delivery service enhancements</li><li>Identifies and engineers new ways to leverage data from multiple platforms to identify network performance trends and detect anomalies</li><li>Prototypes machine learning anomaly detection, event signature identification, and trend identification</li><li>Automates common incident management and problem management procedures</li><li>Develops organization-wide architectures, methodologies, and prototypes for software systems design and development across multiple platforms and organizations within the Global Business Unit.</li><li>Identifies and evaluates new technologies and innovations for alignment with technology roadmap and business value; creates plans for prototyping and prototype iteration.</li><li>Reviews and evaluates designs and project activities for compliance with development guidelines and standards; provides tangible feedback to improve product quality and mitigate failure risk.<br><br></li></ul><strong><strong><em>Education And Experience Required: <br><br></em></strong></strong><ul><li>Bachelor&#x2019;s or master&#x2019;s degree in computer science, Computer Engineering, Information Systems, or equivalent.</li><li>Typically, 10+ years&#x2019; experience.<br><br></li></ul><strong><strong><em>Knowledge And Skills: <br><br></em></strong></strong><ul><li>Experience with cloud platforms</li><li>Experience with software development languages for console and web-based applications</li><li>Experience in User Interface (UI/UX) design</li><li>Understanding of and experience with common network infrastructure devices such as switches, routers, access points, authentication, authorization, and accounting systems and protocols, and network management utilities</li><li>Experience with network monitoring protocols</li><li>Ability to design and implement relational database solutions, time-series databases, and NoSQL database solutions</li><li>Excellent analytical and problem-solving skills</li><li>Experience in the overall architecture of software systems for products and solutions</li><li>Designing and integrating software systems running on multiple platform types into overall architecture</li><li>Evaluating and selecting forms and processes for software systems testing and methodology, including writing and execution of test plans, debugging, and testing scripts and tools</li><li>History of innovation with multiple patents or deployed solutions in the field of software design</li><li>Excellent written and verbal communication skills; mastery of English language; Ability to effectively communicate product architectures and design proposals and negotiate options at business unit and executive levels<br><br></li></ul>#unitedstates LI-Remote #network #relationaldatabase #SRE #sitereliability #architecture #10years+ #prototype #UIUX #design<br><br><strong><strong>Additional Skills:<br><br></strong></strong>Cloud Architectures, Cross Domain Knowledge, Design Thinking, Development Fundamentals, DevOps, Distributed Computing, Microservices Fluency, Full Stack Development, Security-First Mindset, User Experience (UX)<br><br><strong><strong>What We Can Offer You:<br><br></strong></strong><strong>Health &amp; Wellbeing<br><br></strong>We strive to provide our team members and their loved ones with a comprehensive suite of benefits that supports their physical, financial and emotional wellbeing.<br><br><strong>Personal &amp; Professional Development<br><br></strong>We also invest in your career because the better you are, the better we all are. We have specific programs catered to helping you reach any career goals you have &#x2014; whether you want to become a knowledge expert in your field or apply your skills to another division.<br><br><strong>Unconditional Inclusion<br><br></strong>We are unconditionally inclusive in the way we work and celebrate individual uniqueness. We know varied backgrounds are valued and succeed here. We have the flexibility to manage our work and personal needs. We make bold moves, together, and are a force for good.<br><br><strong><strong>Let&apos;s Stay Connected:<br><br></strong></strong>Follow @HPECareers on Instagram to see the latest on people, culture and tech at HPE.<br><br>#unitedstates<br><br>#aruba<br><br><strong><strong>Job:<br><br></strong></strong>Engineering<br><br><strong><strong>Job Level:<br><br></strong></strong>TCP_05<br><br><strong>States with Pay Range Requirement<br><br></strong>The expected salary/wage range for a U.S.-based hire filling this position is provided below. Actual offer may vary from this range based upon geographic location, work experience, education/training, and/or skill level. If this is a sales role, then the listed salary range reflects combined base salary and target-level sales compensation pay. If this is a non-sales role, then the listed salary range reflects base salary only. Variable incentives may also be offered. Information about employee benefits offered can be found at https://myhperewards.com/main/new-hire-enrollment.html.<br><br>USD Annual Salary: $115,500.00 - $266,000.00<br><br>HPE is an Equal Employment Opportunity/ Veterans/Disabled/LGBT employer. We do not discriminate on the basis of race, gender, or any other protected category, and all decisions we make are made on the basis of qualifications, merit, and business need. Our goal is to be one global team that is representative of our customers, in an inclusive environment where we can continue to innovate and grow together. Please click here: Equal Employment Opportunity.<br><br><strong>Hewlett Packard Enterprise is EEO Protected Veteran/ Individual with Disabilities.<br><br></strong>HPE will comply with all applicable laws related to employer use of arrest and conviction records, including laws requiring employers to consider for employment qualified applicants with criminal histories.
        </div>

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--more
        ml-0.5"" data-tracking-control-name=""public_jobs_show-more-html-btn"" aria-label=""Show more"" aria-expanded=""false"">
<!---->
        
            Show more
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/cyolgscd0imw2ldqppkrb84vo""></icon>
    </button>
  

        

    
    
    

    <button class=""show-more-less-html__button show-more-less-button
        show-more-less-html__button--less
        ml-0.5"" data-tracking-control-name=""public_jobs_show-less-html-btn"" aria-label=""Show less"" aria-expanded=""true"">
<!---->
        
            Show less
          

          <icon class=""show-more-less-html__button-icon show-more-less-button-icon"" data-delayed-url=""https://static.licdn.com/aero-v1/sc/h/4chtt12k98xwnba1nimld2oyg""></icon>
    </button>
  
<!---->    </section>",,"{""min_amount"":115500,""max_amount"":266000,""currency"":""$"",""payment_period"":""yr""}","Retrieved from the description.","2025-07-23T16:59:58.196Z","{""url"":""https://www.linkedin.com/jobs/view/principal-network-operations-site-reliability-systems-engineer-at-hpe-aruba-networking-4233345923"",""discovery_input"":{""location"":""TX"",""keyword"":""UIUX"",""country"":""US"",""time_range"":null,""job_type"":null,""experience_level"":null,""remote"":null}}",,,,
