"""
Handles Bright Data API interactions for job scraping.
"""

import requests
import datetime
import json
import os
import time
from typing import Dict, List, Any, Optional

# Fix import to work both as package and standalone
try:
    from ..shared.logger import logger
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
    from shared.logger import logger


class BrightDataAPI:
    """
    BrightData API scraper for Indeed and LinkedIn job data.
    Dynamically creates separate API calls for each input category.
    """
    
    def __init__(self, config_path: str = "config/scraping_inputs.json"):
        """
        Initialize the scraper with configuration.
        
        Args:
            config_path: Path to JSON file with input configurations
        """
        self.config_file = config_path
        self.api_config = {
            "url": "https://api.brightdata.com/datasets/v3/trigger",
            "headers": {
                "Authorization": "Bearer e3f3f5a1c1e6aeb7830744473d5ceb8e4f7c7399e2f5ac08527a8b853de8f321",
                "Content-Type": "application/json",
            },
            "aws_credentials": {
                "aws-access-key": "********************",
                "aws-secret-key": "n+6MxKRIu03f4kN4/EvZc6fy5ZAB98CKKd/wLWzW"
            },
            "bucket": "bright-data-api"
        }
        
        # Dataset IDs - Update these with your actual dataset IDs
        self.dataset_ids = {
            "indeed": "gd_lpfll7v5hcqtkxl6l",  # Replace with actual Indeed dataset ID
            "linkedin": "gd_lpfll7v5hcqtkxl6l"  # Replace with actual LinkedIn dataset ID
        }
        
        # Limits per platform
        self.limits = {
            "indeed": 50,    # 200 results per input for Indeed
            "linkedin": 150   # 300 results per input for LinkedIn
        }
        
        # Load configurations
        self.load_configurations()
    
    def load_configurations(self):
        """Load input configurations from JSON file."""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            self.indeed_inputs = config_data.get('indeed', {})
            self.linkedin_inputs = config_data.get('linkedin', {})
            self.metadata = config_data.get('metadata', {})
            
            logger.info(f"📂 Loaded configurations from '{self.config_file}'")
            logger.info(f"   • Indeed categories: {len(self.indeed_inputs)}")
            logger.info(f"   • LinkedIn categories: {len(self.linkedin_inputs)}")
            logger.info(f"   • Total Indeed inputs: {sum(len(inputs) for inputs in self.indeed_inputs.values())}")
            logger.info(f"   • Total LinkedIn inputs: {sum(len(inputs) for inputs in self.linkedin_inputs.values())}")
            
        except FileNotFoundError:
            logger.error(f"❌ Configuration file '{self.config_file}' not found")
            self.indeed_inputs = {}
            self.linkedin_inputs = {}
            self.metadata = {}
        except Exception as e:
            logger.error(f"❌ Error loading configurations: {str(e)}")
            self.indeed_inputs = {}
            self.linkedin_inputs = {}
            self.metadata = {}
    
    def create_s3_directory_path(self, platform: str, category: str) -> str:
        """
        Create S3 directory path for storing results.
        
        Args:
            platform: 'indeed' or 'linkedin'
            category: Category name
            
        Returns:
            str: S3 directory path
        """
        today = datetime.datetime.now().strftime("%Y-%m-%d")
        return f"{today}/{platform}/{category}/"
    
    def clean_inputs_for_platform(self, platform: str, inputs: List[Dict]) -> List[Dict]:
        """
        Clean inputs based on platform requirements.
        
        Args:
            platform: 'indeed' or 'linkedin'
            inputs: Raw input configurations
            
        Returns:
            List[Dict]: Cleaned inputs for the platform
        """
        cleaned_inputs = []
        
        for input_dict in inputs:
            cleaned_input = input_dict.copy()
            
            if platform.lower() == 'indeed':
                # Indeed allowed fields based on BrightData documentation
                allowed_fields = {
                    'keyword', 'location', 'country', 'time_range', 'job_type', 
                    'experience_level', 'remote', 'company', 'location_radius'
                }
                
                # Remove disallowed fields
                disallowed_fields = ['keyword_search', 'domain', 'date_posted', 'posted_by']
                for field in disallowed_fields:
                    if field in cleaned_input:
                        del cleaned_input[field]
                
                # Convert keyword_search to keyword if needed
                if 'keyword_search' in input_dict and 'keyword' not in cleaned_input:
                    cleaned_input['keyword'] = input_dict['keyword_search']
                
                # Keep only allowed fields
                cleaned_input = {k: v for k, v in cleaned_input.items() if k in allowed_fields}
                
            elif platform.lower() == 'linkedin':
                # LinkedIn allowed fields (keep as is, seems to work)
                allowed_fields = {
                    'keyword', 'location', 'country', 'time_range', 'job_type',
                    'experience_level', 'remote', 'company', 'location_radius'
                }
                
                # Keep only allowed fields
                cleaned_input = {k: v for k, v in cleaned_input.items() if k in allowed_fields}
            
            # Remove empty string values to clean up the input
            cleaned_input = {k: v for k, v in cleaned_input.items() if v != ""}
            
            cleaned_inputs.append(cleaned_input)
        
        return cleaned_inputs

    def trigger_single_category_scraping(self, platform: str, category: str, inputs: List[Dict]) -> Dict[str, Any]:
        """
        Trigger scraping for a single category (one API call).
        
        Args:
            platform: 'indeed' or 'linkedin'
            category: Category name
            inputs: List of input configurations for this category
            
        Returns:
            Dict: API response
        """
        if not inputs:
            return {"error": "No inputs provided"}
        
        # Clean inputs for the specific platform
        cleaned_inputs = self.clean_inputs_for_platform(platform, inputs)
        
        if not cleaned_inputs:
            return {"error": "No valid inputs after cleaning"}
        
        limit_per_input = self.limits[platform]
        directory = self.create_s3_directory_path(platform, category)
        
        # Show sample of cleaned input for debugging
        if cleaned_inputs:
            sample_input = cleaned_inputs[0]
            logger.info(f"   🔍 Sample cleaned input: {sample_input}")
        
        # Prepare API request
        params = {
            "dataset_id": self.dataset_ids[platform],
            "include_errors": "true",
            "type": "discover_new",
            "discover_by": "keyword",
            "limit_per_input": str(limit_per_input),
        }
        
        data = {
            "deliver": {
                "type": "s3",
                "filename": {"template": "{[snapshot_id]}", "extension": "csv"},
                "bucket": self.api_config["bucket"],
                "credentials": self.api_config["aws_credentials"],
                "directory": directory
            },
            "input": cleaned_inputs
        }
        
        try:
            # Make API request
            response = requests.post(
                self.api_config["url"],
                headers=self.api_config["headers"],
                params=params,
                json=data
            )
            
            result = response.json()
            
            # Log the request
            logger.info(f"✅ {platform.upper()} - {category}")
            logger.info(f"   📊 {len(inputs)} inputs × {limit_per_input} results = ~{len(inputs) * limit_per_input} total results")
            logger.info(f"   📁 S3 Path: {directory}")
            logger.info(f"   🆔 Snapshot ID: {result.get('snapshot_id', 'N/A')}")
            
            # Add metadata to result
            result['category'] = category
            result['platform'] = platform
            result['input_count'] = len(inputs)
            result['expected_results'] = len(inputs) * limit_per_input
            result['s3_directory'] = directory
            
            return result
            
        except Exception as e:
            error_result = {
                "error": str(e),
                "category": category,
                "platform": platform,
                "input_count": len(inputs)
            }
            logger.error(f"❌ Error scraping {platform} - {category}: {str(e)}")
            return error_result
    
    def scrape_indeed_all_categories(self, delay_seconds: int = 2) -> Dict[str, Dict[str, Any]]:
        """
        Scrape all Indeed categories (separate API call for each category).
        
        Args:
            delay_seconds: Delay between API calls to avoid rate limiting
            
        Returns:
            Dict: Results for each category
        """
        logger.info(f"🎯 STARTING INDEED SCRAPING - {len(self.indeed_inputs)} CATEGORIES")
        logger.info("=" * 60)
        
        results = {}
        
        for i, (category, inputs) in enumerate(self.indeed_inputs.items(), 1):
            logger.info(f"\n📋 Category {i}/{len(self.indeed_inputs)}: {category}")
            logger.info("-" * 40)
            
            result = self.trigger_single_category_scraping('indeed', category, inputs)
            results[category] = result
            
            # Add delay between requests (except for the last one)
            if i < len(self.indeed_inputs) and delay_seconds > 0:
                logger.info(f"⏳ Waiting {delay_seconds} seconds before next request...")
                time.sleep(delay_seconds)
        
        # Summary
        successful = sum(1 for r in results.values() if 'snapshot_id' in r)
        total_expected = sum(r.get('expected_results', 0) for r in results.values())
        
        logger.info(f"\n📊 INDEED SCRAPING SUMMARY:")
        logger.info(f"   • Total categories: {len(self.indeed_inputs)}")
        logger.info(f"   • Successful API calls: {successful}")
        logger.info(f"   • Failed API calls: {len(results) - successful}")
        logger.info(f"   • Expected total results: ~{total_expected:,}")
        
        return results
    
    def scrape_linkedin_all_categories(self, delay_seconds: int = 2) -> Dict[str, Dict[str, Any]]:
        """
        Scrape all LinkedIn categories (separate API call for each category).
        
        Args:
            delay_seconds: Delay between API calls to avoid rate limiting
            
        Returns:
            Dict: Results for each category
        """
        logger.info(f"🔗 STARTING LINKEDIN SCRAPING - {len(self.linkedin_inputs)} CATEGORIES")
        logger.info("=" * 60)
        
        results = {}
        
        for i, (category, inputs) in enumerate(self.linkedin_inputs.items(), 1):
            logger.info(f"\n📋 Category {i}/{len(self.linkedin_inputs)}: {category}")
            logger.info("-" * 40)
            
            result = self.trigger_single_category_scraping('linkedin', category, inputs)
            results[category] = result
            
            # Add delay between requests (except for the last one)
            if i < len(self.linkedin_inputs) and delay_seconds > 0:
                logger.info(f"⏳ Waiting {delay_seconds} seconds before next request...")
                time.sleep(delay_seconds)
        
        # Summary
        successful = sum(1 for r in results.values() if 'snapshot_id' in r)
        total_expected = sum(r.get('expected_results', 0) for r in results.values())
        
        logger.info(f"\n📊 LINKEDIN SCRAPING SUMMARY:")
        logger.info(f"   • Total categories: {len(self.linkedin_inputs)}")
        logger.info(f"   • Successful API calls: {successful}")
        logger.info(f"   • Failed API calls: {len(results) - successful}")
        logger.info(f"   • Expected total results: ~{total_expected:,}")
        
        return results
    
    def scrape_specific_categories(self, platform: str, categories: List[str], delay_seconds: int = 2) -> Dict[str, Dict[str, Any]]:
        """
        Scrape specific categories from a platform.
        
        Args:
            platform: 'indeed' or 'linkedin'
            categories: List of category names to scrape
            delay_seconds: Delay between API calls
            
        Returns:
            Dict: Results for each category
        """
        logger.info(f"🎯 SCRAPING SPECIFIC {platform.upper()} CATEGORIES")
        logger.info(f"Categories: {categories}")
        logger.info("=" * 50)
        
        if platform.lower() == 'indeed':
            available_inputs = self.indeed_inputs
        elif platform.lower() == 'linkedin':
            available_inputs = self.linkedin_inputs
        else:
            logger.error(f"❌ Invalid platform: {platform}")
            return {}
        
        results = {}
        
        for i, category in enumerate(categories, 1):
            if category not in available_inputs:
                logger.warning(f"⚠️  Category '{category}' not found in {platform} configurations")
                results[category] = {"error": f"Category not found"}
                continue
            
            logger.info(f"\n📋 Category {i}/{len(categories)}: {category}")
            logger.info("-" * 40)
            
            inputs = available_inputs[category]
            result = self.trigger_single_category_scraping(platform, category, inputs)
            results[category] = result
            
            # Add delay between requests (except for the last one)
            if i < len(categories) and delay_seconds > 0:
                logger.info(f"⏳ Waiting {delay_seconds} seconds before next request...")
                time.sleep(delay_seconds)
        
        return results
    
    def scrape_all_platforms(self, delay_seconds: int = 5) -> Dict[str, Dict[str, Dict[str, Any]]]:
        """
        Scrape all categories from both platforms.
        
        Args:
            delay_seconds: Delay between platform switches
            
        Returns:
            Dict: Results organized by platform and category
        """
        logger.info("🚀 STARTING COMPLETE SCRAPING - ALL PLATFORMS")
        logger.info("=" * 70)
        
        all_results = {}
        
        # Scrape Indeed
        if self.indeed_inputs:
            indeed_results = self.scrape_indeed_all_categories(delay_seconds=2)
            all_results['indeed'] = indeed_results
            
            if self.linkedin_inputs and delay_seconds > 0:
                logger.info(f"\n⏳ Waiting {delay_seconds} seconds before switching to LinkedIn...")
                time.sleep(delay_seconds)
        
        # Scrape LinkedIn
        if self.linkedin_inputs:
            linkedin_results = self.scrape_linkedin_all_categories(delay_seconds=2)
            all_results['linkedin'] = linkedin_results
        
        # Overall summary
        total_categories = len(self.indeed_inputs) + len(self.linkedin_inputs)
        total_successful = 0
        total_expected_results = 0
        
        for platform_results in all_results.values():
            total_successful += sum(1 for r in platform_results.values() if 'snapshot_id' in r)
            total_expected_results += sum(r.get('expected_results', 0) for r in platform_results.values())
        
        logger.info(f"\n🎉 COMPLETE SCRAPING FINISHED!")
        logger.info(f"📊 OVERALL SUMMARY:")
        logger.info(f"   • Total categories scraped: {total_categories}")
        logger.info(f"   • Successful API calls: {total_successful}")
        logger.info(f"   • Expected total results: ~{total_expected_results:,}")
        logger.info(f"   • Data saved to S3 in date-organized folders")
        
        # Save results summary
        self.save_scraping_results(all_results)
        
        return all_results
    
    def save_scraping_results(self, results: Dict[str, Dict[str, Dict[str, Any]]]):
        """
        Save scraping results summary to JSON file.
        
        Args:
            results: Complete scraping results
        """
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"scraping_results_{timestamp}.json"
        
        summary_data = {
            "metadata": {
                "scraping_timestamp": datetime.datetime.now().isoformat(),
                "config_file_used": self.config_file,
                "total_api_calls": sum(len(platform_results) for platform_results in results.values()),
                "platforms_scraped": list(results.keys())
            },
            "results": results
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(summary_data, f, indent=2, ensure_ascii=False)
            logger.info(f"💾 Scraping results saved to '{filename}'")
        except Exception as e:
            logger.warning(f"⚠️  Could not save results: {str(e)}")
    
    def get_scraping_status(self) -> Dict[str, Any]:
        """
        Get current scraping configuration status.
        
        Returns:
            Dict: Status information
        """
        return {
            "config_file": self.config_file,
            "indeed_categories": len(self.indeed_inputs),
            "linkedin_categories": len(self.linkedin_inputs),
            "total_indeed_inputs": sum(len(inputs) for inputs in self.indeed_inputs.values()),
            "total_linkedin_inputs": sum(len(inputs) for inputs in self.linkedin_inputs.values()),
            "expected_api_calls": len(self.indeed_inputs) + len(self.linkedin_inputs),
            "limits": self.limits,
            "dataset_ids": self.dataset_ids
        }


# Convenience functions for easy usage
def create_scraper(config_file: str = "config/scraping_inputs.json") -> BrightDataAPI:
    """Create and return a BrightDataAPI instance."""
    return BrightDataAPI(config_file)

def scrape_everything(config_file: str = "config/scraping_inputs.json", delay_seconds: int = 5) -> Dict[str, Dict[str, Dict[str, Any]]]:
    """
    One-command function to scrape everything.
    
    Args:
        config_file: Path to input configurations JSON
        delay_seconds: Delay between platform switches
        
    Returns:
        Dict: Complete scraping results
    """
    scraper = BrightDataAPI(config_file)
    return scraper.scrape_all_platforms(delay_seconds)

def scrape_platform(platform: str, config_file: str = "config/scraping_inputs.json", delay_seconds: int = 2) -> Dict[str, Dict[str, Any]]:
    """
    Scrape all categories from a specific platform.
    
    Args:
        platform: 'indeed' or 'linkedin'
        config_file: Path to input configurations JSON
        delay_seconds: Delay between API calls
        
    Returns:
        Dict: Platform scraping results
    """
    scraper = BrightDataAPI(config_file)
    
    if platform.lower() == 'indeed':
        return scraper.scrape_indeed_all_categories(delay_seconds)
    elif platform.lower() == 'linkedin':
        return scraper.scrape_linkedin_all_categories(delay_seconds)
    else:
        logger.error(f"❌ Invalid platform: {platform}")
        return {} 