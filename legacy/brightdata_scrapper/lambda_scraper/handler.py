"""
Lambda 1: Triggers Bright Data scraping jobs for Indeed/LinkedIn using the config file.
"""

import json

# Fix import to work both as package and standalone
try:
    from .brightdata_api import BrightDataAPI
    from ..shared.logger import logger
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
    from lambda_scraper.brightdata_api import BrightDataAP<PERSON>
    from shared.logger import logger


def lambda_handler(event, context):
    """
    Entrypoint for AWS Lambda. Triggers Bright Data API jobs and logs/saves job metadata.
    
    Args:
        event: Lambda event (can contain platform, categories, delay_seconds)
        context: Lambda context
        
    Returns:
        Dict: Scraping results and status
    """
    try:
        logger.info("🚀 Starting Bright Data scraping Lambda")
        
        # Parse event parameters
        platform = event.get('platform', 'all')  # 'all', 'indeed', 'linkedin'
        categories = event.get('categories', [])  # Specific categories to scrape
        delay_seconds = event.get('delay_seconds', 5)
        config_file = event.get('config_file', 'config/scraping_inputs.json')
        
        logger.info(f"📋 Parameters: platform={platform}, categories={categories}, delay={delay_seconds}")
        
        # Create scraper instance
        scraper = BrightDataAPI(config_file)
        
        # Get scraping status
        status = scraper.get_scraping_status()
        logger.info(f"📊 Scraping Status: {status}")
        
        # Execute scraping based on parameters
        if platform == 'all':
            logger.info("🎯 Scraping all platforms")
            results = scraper.scrape_all_platforms(delay_seconds)
        elif platform in ['indeed', 'linkedin']:
            if categories:
                logger.info(f"🎯 Scraping specific categories from {platform}: {categories}")
                results = scraper.scrape_specific_categories(platform, categories, delay_seconds)
            else:
                logger.info(f"🎯 Scraping all categories from {platform}")
                if platform == 'indeed':
                    results = scraper.scrape_indeed_all_categories(delay_seconds)
                else:
                    results = scraper.scrape_linkedin_all_categories(delay_seconds)
        else:
            raise ValueError(f"Invalid platform: {platform}")
        
        # Calculate summary
        total_successful = 0
        total_expected = 0
        
        if isinstance(results, dict):
            for platform_results in results.values():
                if isinstance(platform_results, dict):
                    total_successful += sum(1 for r in platform_results.values() if 'snapshot_id' in r)
                    total_expected += sum(r.get('expected_results', 0) for r in platform_results.values())
        
        response = {
            "status": "success",
            "platform": platform,
            "categories_requested": categories,
            "total_successful_api_calls": total_successful,
            "total_expected_results": total_expected,
            "results": results
        }
        
        logger.info(f"✅ Scraping completed successfully: {total_successful} successful calls, ~{total_expected} expected results")
        return response
        
    except Exception as e:
        logger.error(f"❌ Error in scraping Lambda: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "platform": event.get('platform', 'unknown'),
            "categories_requested": event.get('categories', [])
        } 