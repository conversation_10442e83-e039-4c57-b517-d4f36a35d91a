#!/usr/bin/env python3
"""
Simple script to test the scraper functionality.
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_scraper():
    """Test the scraper functionality."""
    try:
        from lambda_scraper.handler import lambda_handler as scraper_handler
        
        # Test with minimal event
        event = {
            "platform": "all",
            "delay_seconds": 1,  # Short delay for testing
            "config_file": "lambda_scraper/config/scraping_inputs.json"
        }
        
        print("🧪 Testing scraper...")
        result = scraper_handler(event, None)
        print(f"✅ Scraper test completed: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Scraper test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Testing BrightData Scrapper")
    print("=" * 40)
    
    if test_scraper():
        print("🎉 All tests passed!")
    else:
        print("⚠️ Tests failed!")