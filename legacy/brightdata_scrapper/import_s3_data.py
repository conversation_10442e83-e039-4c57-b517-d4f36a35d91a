#!/usr/bin/env python3
"""
S3 Data Import Script
Downloads and extracts all S3 data into linkedin_data and indeed_data folders.
"""

import os
import sys
import boto3
import pandas as pd
from datetime import datetime
from pathlib import Path
import logging

# Add the project root to the path for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from shared.logger import logger
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the brightdata_scrapper directory")
    sys.exit(1)


class S3DataImporter:
    """
    Downloads and extracts all S3 data into organized folders.
    """
    
    def __init__(self, aws_access_key: str = None, aws_secret_key: str = None, bucket_name: str = "bright-data-api"):
        """
        Initialize S3 data importer.
        
        Args:
            aws_access_key: AWS access key (if None, uses default credentials)
            aws_secret_key: AWS secret key (if None, uses default credentials)
            bucket_name: S3 bucket name
        """
        self.bucket_name = bucket_name
        
        # Initialize S3 client
        if aws_access_key and aws_secret_key:
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=aws_access_key,
                aws_secret_access_key=aws_secret_key
            )
        else:
            # Use default credentials (from environment, IAM role, etc.)
            self.s3_client = boto3.client('s3')
        
        # Get today's date for filtering
        self.today = datetime.now().strftime("%Y-%m-%d")
        
        # Create output directories
        self.linkedin_dir = Path("linkedin_data")
        self.indeed_dir = Path("indeed_data")
        self.linkedin_dir.mkdir(exist_ok=True)
        self.indeed_dir.mkdir(exist_ok=True)
        
        logger.info(f"🪣 S3 Bucket: {bucket_name}")
        logger.info(f"📅 Filtering for date: {self.today}")
        logger.info(f"📁 LinkedIn data directory: {self.linkedin_dir}")
        logger.info(f"📁 Indeed data directory: {self.indeed_dir}")
    
    def list_todays_s3_files(self) -> dict:
        """
        List today's files in S3 bucket organized by platform.
        
        Returns:
            dict: Dictionary with 'linkedin' and 'indeed' keys containing lists of S3 keys
        """
        try:
            logger.info(f"🔍 Listing today's files ({self.today}) in S3 bucket...")
            
            # Get all objects in the bucket
            paginator = self.s3_client.get_paginator('list_objects_v2')
            pages = paginator.paginate(Bucket=self.bucket_name)
            
            linkedin_files = []
            indeed_files = []
            
            for page in pages:
                if 'Contents' in page:
                    for obj in page['Contents']:
                        key = obj['Key']
                        
                        # Filter for today's date
                        if self.today in key:
                            # Categorize files by platform
                            if 'linkedin' in key.lower():
                                linkedin_files.append(key)
                            elif 'indeed' in key.lower():
                                indeed_files.append(key)
            
            result = {
                'linkedin': linkedin_files,
                'indeed': indeed_files
            }
            
            logger.info(f"📊 Found {len(linkedin_files)} LinkedIn files and {len(indeed_files)} Indeed files for {self.today}")
            return result
            
        except Exception as e:
            logger.error(f"Error listing S3 files: {e}")
            return {'linkedin': [], 'indeed': []}
    
    def download_file_from_s3(self, s3_key: str, local_path: Path) -> bool:
        """
        Download a single file from S3 to local path.
        
        Args:
            s3_key: S3 object key
            local_path: Local file path
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"⬇️ Downloading: {s3_key}")
            self.s3_client.download_file(self.bucket_name, s3_key, str(local_path))
            logger.info(f"✅ Downloaded: {local_path}")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to download {s3_key}: {e}")
            return False
    
    def download_platform_data(self, platform: str, s3_files: list) -> int:
        """
        Download all files for a specific platform.
        
        Args:
            platform: Platform name ('linkedin' or 'indeed')
            s3_files: List of S3 file keys
            
        Returns:
            int: Number of successfully downloaded files
        """
        if platform == 'linkedin':
            output_dir = self.linkedin_dir
        elif platform == 'indeed':
            output_dir = self.indeed_dir
        else:
            logger.error(f"Invalid platform: {platform}")
            return 0
        
        logger.info(f"📥 Downloading {len(s3_files)} {platform} files...")
        
        success_count = 0
        for s3_key in s3_files:
            # Create filename from S3 key
            filename = s3_key.split('/')[-1]
            local_path = output_dir / filename
            
            if self.download_file_from_s3(s3_key, local_path):
                success_count += 1
        
        logger.info(f"✅ Successfully downloaded {success_count}/{len(s3_files)} {platform} files")
        return success_count
    
    def import_todays_data(self) -> dict:
        """
        Import today's data from S3 into local folders.
        
        Returns:
            dict: Summary of import results
        """
        logger.info(f"🚀 Starting S3 data import for {self.today}...")
        
        # List today's files in S3
        s3_files = self.list_todays_s3_files()
        
        # Download LinkedIn data
        linkedin_count = self.download_platform_data('linkedin', s3_files['linkedin'])
        
        # Download Indeed data
        indeed_count = self.download_platform_data('indeed', s3_files['indeed'])
        
        # Generate summary
        summary = {
            'date': self.today,
            'total_linkedin_files': len(s3_files['linkedin']),
            'downloaded_linkedin_files': linkedin_count,
            'total_indeed_files': len(s3_files['indeed']),
            'downloaded_indeed_files': indeed_count,
            'linkedin_directory': str(self.linkedin_dir),
            'indeed_directory': str(self.indeed_dir),
            'import_timestamp': datetime.now().isoformat()
        }
        
        logger.info("📊 Import Summary:")
        logger.info(f"   Date: {self.today}")
        logger.info(f"   LinkedIn: {linkedin_count}/{len(s3_files['linkedin'])} files")
        logger.info(f"   Indeed: {indeed_count}/{len(s3_files['indeed'])} files")
        logger.info(f"   LinkedIn directory: {self.linkedin_dir}")
        logger.info(f"   Indeed directory: {self.indeed_dir}")
        
        return summary


def main():
    """Main function to import S3 data."""
    
    logger.info("Starting S3 data import script")
    
    try:
        # Initialize the S3 importer
        importer = S3DataImporter()
        
        # Import today's data
        summary = importer.import_todays_data()
        
        # Display summary
        print("\n" + "="*60)
        print("S3 DATA IMPORT SUMMARY")
        print("="*60)
        print(f"Date: {summary['date']}")
        print(f"LinkedIn files: {summary['downloaded_linkedin_files']}/{summary['total_linkedin_files']}")
        print(f"Indeed files: {summary['downloaded_indeed_files']}/{summary['total_indeed_files']}")
        print(f"LinkedIn directory: {summary['linkedin_directory']}")
        print(f"Indeed directory: {summary['indeed_directory']}")
        print(f"Import timestamp: {summary['import_timestamp']}")
        print("="*60)
        
        logger.info("S3 data import completed successfully")
        
    except Exception as e:
        logger.error(f"Error during S3 data import: {e}")
        raise


if __name__ == "__main__":
    main() 