#!/usr/bin/env python3
"""
Separate Sheets Creator
Loads combined job data CSV and creates an Excel spreadsheet with multiple sheets:
- All Jobs: Complete dataset
- Keyword-specific sheets: Separate sheets for each unique keyword
"""

import pandas as pd
import os
import sys
from datetime import datetime
from pathlib import Path
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows


def load_combined_data(csv_path):
    """
    Load the combined job data from CSV file
    """
    try:
        df = pd.read_csv(csv_path)
        print(f"✅ Loaded {len(df)} jobs from {csv_path}")
        return df
    except FileNotFoundError:
        print(f"❌ File not found: {csv_path}")
        return None
    except Exception as e:
        print(f"❌ Error loading file: {str(e)}")
        return None


def beautify_worksheet(worksheet, df):
    """
    Apply beautiful formatting to the worksheet
    """
    # Define styles
    header_font = Font(bold=True, color="FFFFFF", size=12, name="Arial")
    header_fill = PatternFill(start_color="20705B", end_color="20705B", fill_type="solid")
    data_font = Font(size=10, name="Arial")
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # Get the maximum row and column
    max_row = worksheet.max_row
    max_col = worksheet.max_column
    
    # Get column headers to identify specific columns
    headers = [worksheet.cell(row=1, column=col).value for col in range(1, max_col + 1)]
    
    # Format headers (first row) - apply color to entire header row
    for col in range(1, max_col + 1):
        cell = worksheet.cell(row=1, column=col)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = Alignment(horizontal='left', vertical='center')
        cell.border = border
    
    # Apply header color to the entire first row
    worksheet.row_dimensions[1].fill = header_fill
    
    # Set fixed row height for all data rows to prevent excessive height
    for row in range(2, max_row + 1):
        worksheet.row_dimensions[row].height = 30  # Fixed height in points
    
    # Format data rows with specific handling for different column types
    for row in range(2, max_row + 1):
        for col in range(1, max_col + 1):
            cell = worksheet.cell(row=row, column=col)
            cell.font = data_font
            cell.border = border
            
            # Get column header to determine formatting
            col_header = headers[col - 1] if col <= len(headers) else ""
            
            # Special handling for apply_link column - allow horizontal overflow
            if col_header and 'apply_link' in col_header.lower():
                cell.alignment = Alignment(horizontal='left', vertical='top', wrap_text=False)
            # Special handling for job description - limit wrapping
            elif col_header and any(keyword in col_header.lower() for keyword in ['description', 'jd', 'job_description']):
                cell.alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
            else:
                # Default formatting for other columns
                cell.alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
    
    # Set column widths based on header text length + 20
    for col in range(1, max_col + 1):
        column_letter = worksheet.cell(row=1, column=col).column_letter
        col_header = headers[col - 1] if col <= len(headers) else ""
        
        # Calculate width as header text length + 10 (reduced padding)
        header_length = len(str(col_header)) if col_header else 10
        column_width = header_length + 10
        
        # Special handling for apply_link - double the width
        if col_header and 'apply_link' in col_header.lower():
            # Double the width for apply_link columns
            column_width = column_width * 2
        
        worksheet.column_dimensions[column_letter].width = column_width
    
    # Freeze the first row
    worksheet.freeze_panes = "A2"


def create_excel_with_sheets(df, output_path, sheet_name_prefix="Jobs"):
    """
    Create Excel file with multiple sheets:
    - All Jobs: Complete dataset
    - Keyword sheets: Separate sheets for each unique keyword
    """
    if df.empty:
        print("❌ No data to process")
        return False
    
    try:
        # Create output directory if it doesn't exist
        output_dir = os.path.dirname(output_path)
        if output_dir:
            Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        # Create Excel writer
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            
            # 1. All Jobs sheet
            print(f"📊 Creating 'All Jobs' sheet with {len(df)} rows...")
            df.to_excel(writer, sheet_name='All Jobs', index=False)
            
            # Beautify the All Jobs sheet
            workbook = writer.book
            worksheet = writer.sheets['All Jobs']
            beautify_worksheet(worksheet, df)
            
            # 2. Keyword-specific sheets
            if 'keyword' in df.columns:
                # Get unique keywords (excluding None/NaN) and normalize them
                keywords = df['keyword'].dropna().unique()
                print(f"🔍 Found {len(keywords)} unique keywords")
                
                # Create a mapping of normalized keywords to handle duplicates
                keyword_mapping = {}
                for keyword in keywords:
                    if pd.isna(keyword) or keyword == "":
                        continue
                    
                    # Normalize keyword (lowercase and strip whitespace)
                    normalized_keyword = str(keyword).strip().lower()
                    
                    if normalized_keyword not in keyword_mapping:
                        keyword_mapping[normalized_keyword] = []
                    keyword_mapping[normalized_keyword].append(keyword)
                
                print(f"🔍 After normalization: {len(keyword_mapping)} unique keyword groups")
                
                # Create sheets for each normalized keyword group
                for normalized_keyword, original_keywords in keyword_mapping.items():
                    # Filter data for all variations of this keyword
                    keyword_mask = df['keyword'].isin(original_keywords)
                    keyword_df = df[keyword_mask].copy()
                    
                    if not keyword_df.empty:
                        # Use the first original keyword as the sheet name
                        sheet_name = str(original_keywords[0])[:31]  # Excel sheet name limit
                        sheet_name = sheet_name.replace('/', '_').replace('\\', '_').replace('*', '_').replace('?', '_').replace('[', '_').replace(']', '_')
                        
                        print(f"📋 Creating '{sheet_name}' sheet with {len(keyword_df)} jobs (combining: {', '.join(original_keywords)})")
                        keyword_df.to_excel(writer, sheet_name=sheet_name, index=False)
                        
                        # Beautify the keyword sheet
                        keyword_worksheet = writer.sheets[sheet_name]
                        beautify_worksheet(keyword_worksheet, keyword_df)
                    else:
                        print(f"⚠️ No jobs found for keyword group: {original_keywords}")
            else:
                print("⚠️ No 'keyword' column found in data")
        
        print(f"✅ Excel file created successfully: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating Excel file: {str(e)}")
        return False


def process_csv_to_excel(csv_path, output_dir="combined_data"):
    """
    Process a CSV file and create Excel sheets programmatically
    """
    print("🚀 Starting Excel sheet creation...")
    print("=" * 50)
    
    # Load the data
    df = load_combined_data(csv_path)
    if df is None:
        return None
    
    # Show data overview
    print(f"\n📊 Data Overview:")
    print(f"  Total jobs: {len(df)}")
    if 'keyword' in df.columns:
        unique_keywords = df['keyword'].dropna().nunique()
        print(f"  Unique keywords: {unique_keywords}")
    if 'source' in df.columns:
        sources = df['source'].unique()
        print(f"  Sources: {', '.join(sources)}")
    
    # Generate output filename
    output_filename = f"job_sheets_final.xlsx"
    output_path = os.path.join(output_dir, output_filename)
    
    # Create Excel file with sheets
    success = create_excel_with_sheets(df, output_path)
    
    if success:
        print(f"\n✅ Excel file created: {output_path}")
        print(f"📁 File size: {os.path.getsize(output_path) / 1024:.1f} KB")
        
        # Show what sheets were created
        print(f"\n📋 Sheets created:")
        print(f"  - All Jobs ({len(df)} rows)")
        if 'keyword' in df.columns:
            keywords = df['keyword'].dropna().unique()
            for keyword in keywords:
                if pd.notna(keyword) and keyword != "":
                    keyword_count = len(df[df['keyword'] == keyword])
                    print(f"  - {keyword} ({keyword_count} rows)")
        
        return output_path
    
    return None


def main():
    """
    Main function to process combined data and create Excel sheets
    """
    csv_path = "/Users/<USER>/Documents/scrapper/src/combined_data/combined_jobs.csv"
    print(f"Using provided CSV path: {csv_path}")
    process_csv_to_excel(csv_path)
        
    print("\n🎉 Process completed!")


if __name__ == "__main__":
    main()
