"""
Combine Platform Data

This script combines all Indeed data files into one CSV (indeed_combined.csv)
and all LinkedIn data files into another CSV (linkedin_combined.csv).
"""
import os
import pandas as pd
import glob
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def combine_indeed_data(indeed_folder='indeed_data', output_file='indeed_combined.csv'):
    """
    Combine all Indeed CSV files into one CSV
    
    Args:
        indeed_folder (str): Folder containing Indeed data files
        output_file (str): Output CSV filename
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("=== Combining Indeed Data ===")
        
        # Get all CSV files in Indeed folder
        indeed_pattern = os.path.join(indeed_folder, '*.csv')
        indeed_files = glob.glob(indeed_pattern)
        
        if not indeed_files:
            logger.warning(f"No CSV files found in {indeed_folder}")
            return False
        
        logger.info(f"Found {len(indeed_files)} Indeed files:")
        for file in indeed_files:
            logger.info(f"  - {os.path.basename(file)}")
        
        # Combine all Indeed files
        combined_indeed = []
        total_rows = 0
        
        for file_path in indeed_files:
            try:
                df = pd.read_csv(file_path)
                combined_indeed.append(df)
                total_rows += len(df)
                logger.info(f"  Loaded {len(df)} rows from {os.path.basename(file_path)}")
            except Exception as e:
                logger.error(f"  Error loading {file_path}: {str(e)}")
                continue
        
        if not combined_indeed:
            logger.error("No Indeed data could be loaded")
            return False
        
        # Concatenate all DataFrames
        indeed_df = pd.concat(combined_indeed, ignore_index=True)
        
        # Remove duplicates if any
        initial_count = len(indeed_df)
        indeed_df = indeed_df.drop_duplicates()
        final_count = len(indeed_df)
        
        if initial_count != final_count:
            logger.info(f"Removed {initial_count - final_count} duplicate rows")
        
        # Save combined Indeed data
        indeed_df.to_csv(output_file, index=False)
        logger.info(f"✅ Indeed data combined: {final_count} rows saved to {output_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error combining Indeed data: {str(e)}")
        return False

def combine_linkedin_data(linkedin_folder='linkedin_data', output_file='linkedin_combined.csv'):
    """
    Combine all LinkedIn CSV files into one CSV
    
    Args:
        linkedin_folder (str): Folder containing LinkedIn data files
        output_file (str): Output CSV filename
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("=== Combining LinkedIn Data ===")
        
        # Get all CSV files in LinkedIn folder
        linkedin_pattern = os.path.join(linkedin_folder, '*.csv')
        linkedin_files = glob.glob(linkedin_pattern)
        
        if not linkedin_files:
            logger.warning(f"No CSV files found in {linkedin_folder}")
            return False
        
        logger.info(f"Found {len(linkedin_files)} LinkedIn files:")
        for file in linkedin_files:
            logger.info(f"  - {os.path.basename(file)}")
        
        # Combine all LinkedIn files
        combined_linkedin = []
        total_rows = 0
        
        for file_path in linkedin_files:
            try:
                df = pd.read_csv(file_path)
                combined_linkedin.append(df)
                total_rows += len(df)
                logger.info(f"  Loaded {len(df)} rows from {os.path.basename(file_path)}")
            except Exception as e:
                logger.error(f"  Error loading {file_path}: {str(e)}")
                continue
        
        if not combined_linkedin:
            logger.error("No LinkedIn data could be loaded")
            return False
        
        # Concatenate all DataFrames
        linkedin_df = pd.concat(combined_linkedin, ignore_index=True)
        
        # Remove duplicates if any
        initial_count = len(linkedin_df)
        linkedin_df = linkedin_df.drop_duplicates()
        final_count = len(linkedin_df)
        
        if initial_count != final_count:
            logger.info(f"Removed {initial_count - final_count} duplicate rows")
        
        # Save combined LinkedIn data
        linkedin_df.to_csv(output_file, index=False)
        logger.info(f"✅ LinkedIn data combined: {final_count} rows saved to {output_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error combining LinkedIn data: {str(e)}")
        return False

def combine_all_platform_data(indeed_folder='indeed_data', linkedin_folder='linkedin_data'):
    """
    Combine all platform data into separate CSV files
    
    Args:
        indeed_folder (str): Folder containing Indeed data files
        linkedin_folder (str): Folder containing LinkedIn data files
        
    Returns:
        tuple: (indeed_success, linkedin_success)
    """
    logger.info("=" * 50)
    logger.info("🔄 COMBINING PLATFORM DATA")
    logger.info("=" * 50)
    
    start_time = datetime.now()
    
    # Combine Indeed data
    indeed_success = combine_indeed_data(indeed_folder, 'indeed_combined.csv')
    
    # Combine LinkedIn data
    linkedin_success = combine_linkedin_data(linkedin_folder, 'linkedin_combined.csv')
    
    # Summary
    end_time = datetime.now()
    duration = end_time - start_time
    
    logger.info("=" * 50)
    logger.info("📊 COMBINATION SUMMARY")
    logger.info("=" * 50)
    logger.info(f"Indeed data: {'✅ Success' if indeed_success else '❌ Failed'}")
    logger.info(f"LinkedIn data: {'✅ Success' if linkedin_success else '❌ Failed'}")
    logger.info(f"Duration: {duration}")
    
    if indeed_success and linkedin_success:
        logger.info("🎉 All platform data combined successfully!")
    elif indeed_success or linkedin_success:
        logger.warning("⚠️ Some platform data combined successfully")
    else:
        logger.error("❌ Failed to combine platform data")
    
    logger.info("=" * 50)
    
    return indeed_success, linkedin_success

def get_data_summary():
    """Get summary of combined data files"""
    try:
        logger.info("=== DATA SUMMARY ===")
        
        files_to_check = ['indeed_combined.csv', 'linkedin_combined.csv']
        
        for filename in files_to_check:
            if os.path.exists(filename):
                df = pd.read_csv(filename)
                logger.info(f"{filename}: {len(df)} rows, {len(df.columns)} columns")
                
                # Show column names
                logger.info(f"  Columns: {', '.join(df.columns.tolist()[:5])}{'...' if len(df.columns) > 5 else ''}")
            else:
                logger.info(f"{filename}: File not found")
        
    except Exception as e:
        logger.error(f"Error getting data summary: {str(e)}")

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Combine platform data into separate CSV files')
    parser.add_argument('--indeed-folder', default='indeed_data', 
                       help='Folder containing Indeed data files')
    parser.add_argument('--linkedin-folder', default='linkedin_data', 
                       help='Folder containing LinkedIn data files')
    parser.add_argument('--summary', action='store_true',
                       help='Show summary of existing combined files')
    
    args = parser.parse_args()
    
    if args.summary:
        get_data_summary()
        return
    
    # Combine platform data
    indeed_success, linkedin_success = combine_all_platform_data(
        args.indeed_folder, 
        args.linkedin_folder
    )
    
    # Show summary
    get_data_summary()
    
    # Exit with appropriate code
    if indeed_success and linkedin_success:
        exit(0)
    else:
        exit(1)

if __name__ == "__main__":
    main()