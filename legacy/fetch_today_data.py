import boto3
import os
import datetime

class S3DataFetcher:
    """
    Fetches raw job data files from S3 and saves them locally.
    """
    
    def __init__(self):
        """Initialize S3 client with credentials."""
        self.s3_client = boto3.client(
            's3',
            aws_access_key_id='********************',
            aws_secret_access_key='n+6MxKRIu03f4kN4/EvZc6fy5ZAB98CKKd/wLWzW'
        )
        self.bucket_name = 'bright-data-api'
        
        # Create local directories if they don't exist
        self.local_dirs = {
            'indeed': 'indeed_data',
            'linkedin': 'linkedin_data'
        }
        for directory in self.local_dirs.values():
            os.makedirs(directory, exist_ok=True)
    
    def get_today_s3_prefix(self, platform: str) -> str:
        """Get the S3 prefix for today's data."""
        # today = datetime.datetime.now().strftime("%Y-%m-%d")
        today='2025-09-11'
        return f"{today}/{platform.lower()}/"
    
    def list_s3_files(self, prefix: str) -> list:
        """List all CSV files in the S3 bucket with given prefix."""
        try:
            paginator = self.s3_client.get_paginator('list_objects_v2')
            files = []
            
            for page in paginator.paginate(Bucket=self.bucket_name, Prefix=prefix):
                if 'Contents' in page:
                    files.extend([
                        obj['Key'] for obj in page['Contents']
                        if obj['Key'].endswith('.csv')
                    ])
            
            return files
        except Exception as e:
            print(f"❌ Error listing S3 files: {str(e)}")
            return []
    
    def download_file(self, s3_key: str, local_dir: str):
        """Download a single file from S3."""
        try:
            # Generate local filename from S3 key
            filename = os.path.basename(s3_key)
            local_path = os.path.join(local_dir, filename)
            
            # Download file
            print(f"📥 Downloading {filename}...")
            self.s3_client.download_file(self.bucket_name, s3_key, local_path)
            print(f"✅ Successfully downloaded {filename}")
            
        except Exception as e:
            print(f"❌ Error downloading {s3_key}: {str(e)}")
    
    def fetch_platform_data(self, platform: str):
        """Download all files for a specific platform."""
        print(f"\n🔄 Fetching {platform.upper()} data...")
        
        # Get S3 prefix for today
        prefix = self.get_today_s3_prefix(platform)
        print(f"📂 Looking in S3 path: {prefix}")
        
        # List all CSV files
        s3_files = self.list_s3_files(prefix)
        
        if not s3_files:
            print(f"⚠️  No CSV files found for {platform} today")
            return
        
        print(f"📊 Found {len(s3_files)} CSV files")
        
        # Download each file
        local_dir = self.local_dirs[platform]
        for s3_key in s3_files:
            self.download_file(s3_key, local_dir)

def fetch_today_data():
    """Main function to fetch today's data for both platforms."""
    fetcher = S3DataFetcher()
    
    print("🚀 FETCHING TODAY'S JOB DATA")
    print("=" * 50)
    
    # Download Indeed data
    fetcher.fetch_platform_data('indeed')
    
    # Download LinkedIn data
    fetcher.fetch_platform_data('linkedin')
    
    print("\n🎉 DATA FETCHING COMPLETE!")

if __name__ == "__main__":
    fetch_today_data() 