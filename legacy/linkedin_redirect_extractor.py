#!/usr/bin/env python3
"""
LinkedIn Apply Link Redirect Extractor

This script extracts the final company apply links from LinkedIn job postings
by following redirects from the initial LinkedIn apply_link URLs.
"""

import requests
import pandas as pd
import time
import logging
from urllib.parse import urljoin, urlparse
from datetime import datetime
import os

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LinkedInRedirectExtractor:
    """
    Extracts final apply links from LinkedIn job postings by following redirects.
    """
    
    def __init__(self, delay_between_requests=2, max_redirects=10, timeout=15):
        """
        Initialize the extractor with configuration.
        
        Args:
            delay_between_requests: Seconds to wait between requests (rate limiting)
            max_redirects: Maximum number of redirects to follow
            timeout: Request timeout in seconds
        """
        self.delay_between_requests = delay_between_requests
        self.max_redirects = max_redirects
        self.timeout = timeout
        
        # Session for connection reuse
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def get_final_url(self, url):
        """
        Follow redirects to get the final destination URL.
        
        Args:
            url: Initial LinkedIn apply link
            
        Returns:
            str: Final destination URL or original URL if failed
        """
        if not url or pd.isna(url) or url.strip() == '':
            return ''
        
        try:
            # Clean the URL
            url = url.strip()
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            logger.info(f"🔍 Following redirects for: {url}")
            
            # Follow redirects
            response = self.session.get(
                url, 
                allow_redirects=True, 
                timeout=self.timeout,
                stream=True  # Don't download content, just headers
            )
            
            final_url = response.url
            
            # Check if we got a different URL
            if final_url != url:
                logger.info(f"✅ Redirect successful: {url} → {final_url}")
                return final_url
            else:
                logger.info(f"ℹ️ No redirect: {url}")
                return url
                
        except requests.exceptions.Timeout:
            logger.warning(f"⏰ Timeout for: {url}")
            return url
        except requests.exceptions.TooManyRedirects:
            logger.warning(f"🔄 Too many redirects for: {url}")
            return url
        except requests.exceptions.RequestException as e:
            logger.warning(f"❌ Request failed for {url}: {str(e)}")
            return url
        except Exception as e:
            logger.error(f"💥 Unexpected error for {url}: {str(e)}")
            return url
    
    def extract_final_links(self, df, apply_link_column='apply_link'):
        """
        Extract final apply links for all LinkedIn jobs in the DataFrame.
        
        Args:
            df: DataFrame with LinkedIn job data
            apply_link_column: Name of the column containing apply links
            
        Returns:
            DataFrame: Original DataFrame with added 'final_apply_link' column
        """
        if df.empty:
            logger.warning("⚠️ Empty DataFrame provided")
            return df
        
        if apply_link_column not in df.columns:
            logger.error(f"❌ Column '{apply_link_column}' not found in DataFrame")
            return df
        
        logger.info(f"🚀 Starting LinkedIn redirect extraction for {len(df)} jobs")
        logger.info(f"⏱️ Delay between requests: {self.delay_between_requests} seconds")
        
        final_links = []
        successful_redirects = 0
        failed_redirects = 0
        
        for idx, row in df.iterrows():
            original_url = row.get(apply_link_column, '')
            
            if original_url and str(original_url).strip() != '':
                final_url = self.get_final_url(original_url)
                final_links.append(final_url)
                
                if final_url != original_url:
                    successful_redirects += 1
                else:
                    failed_redirects += 1
            else:
                final_links.append('')
                failed_redirects += 1
            
            # Rate limiting
            if idx < len(df) - 1:  # Don't delay after the last request
                time.sleep(self.delay_between_requests)
            
            # Progress update every 10 jobs
            if (idx + 1) % 10 == 0:
                logger.info(f"📊 Progress: {idx + 1}/{len(df)} jobs processed")
        
        # Add the final links column
        df = df.copy()
        df['final_apply_link'] = final_links
        
        # Summary
        logger.info(f"✅ LinkedIn redirect extraction completed!")
        logger.info(f"📊 Results: {successful_redirects} successful redirects, {failed_redirects} failed/no redirects")
        logger.info(f"📈 Success rate: {(successful_redirects / len(df)) * 100:.1f}%")
        
        return df
    
    def save_results(self, df, output_file=None):
        """
        Save the results to a CSV file.
        
        Args:
            df: DataFrame with final apply links
            output_file: Output file path (optional)
        """
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"linkedin_with_final_links_{timestamp}.csv"
        
        try:
            df.to_csv(output_file, index=False)
            logger.info(f"💾 Results saved to: {output_file}")
            return output_file
        except Exception as e:
            logger.error(f"❌ Failed to save results: {str(e)}")
            return None

def main():
    """
    Main function to demonstrate usage.
    """
    print("🚀 LinkedIn Apply Link Redirect Extractor")
    print("=" * 50)
    
    # Load LinkedIn data
    linkedin_file = "linkedin_combined.csv"
    if not os.path.exists(linkedin_file):
        print(f"❌ LinkedIn file not found: {linkedin_file}")
        print("Please run the data preprocessor first to create linkedin_combined.csv")
        return
    
    try:
        # Load data
        print(f"📂 Loading LinkedIn data from {linkedin_file}...")
        df = pd.read_csv(linkedin_file)
        print(f"✅ Loaded {len(df)} LinkedIn jobs")
        
        # Create extractor
        extractor = LinkedInRedirectExtractor(
            delay_between_requests=2,  # 2 seconds between requests
            max_redirects=10,
            timeout=15
        )
        
        # Extract final links
        print("\n🔄 Extracting final apply links...")
        df_with_final_links = extractor.extract_final_links(df)
        
        # Save results
        print("\n💾 Saving results...")
        output_file = extractor.save_results(df_with_final_links)
        
        if output_file:
            print(f"\n✅ Process completed successfully!")
            print(f"📁 Results saved to: {output_file}")
            print(f"📊 Total jobs processed: {len(df_with_final_links)}")
            
            # Show some sample results
            print(f"\n📋 Sample results:")
            sample_df = df_with_final_links[['job_title', 'company_name', 'apply_link', 'final_apply_link']].head(5)
            for idx, row in sample_df.iterrows():
                print(f"  {idx+1}. {row['job_title']} at {row['company_name']}")
                print(f"     Original: {row['apply_link'][:50]}...")
                print(f"     Final:    {row['final_apply_link'][:50]}...")
                print()
        else:
            print("❌ Failed to save results")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        logger.error(f"Main function error: {str(e)}")

if __name__ == "__main__":
    main()
