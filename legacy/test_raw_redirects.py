#!/usr/bin/env python3
"""
Test LinkedIn Redirect Extractor on Raw LinkedIn Data

This script tests the redirect extractor directly on the raw LinkedIn CSV file
since the combined file seems to have only company data.
"""

import pandas as pd
import os
from linkedin_redirect_extractor import LinkedInRedirectExtractor

def test_raw_redirects():
    """
    Test the redirect extractor on raw LinkedIn data.
    """
    print("🧪 Testing LinkedIn Redirect Extractor on Raw Data")
    print("=" * 60)
    
    # Check if raw LinkedIn data exists
    linkedin_file = "linkedin_data/s_mffbl84c2flosm1xg.csv"
    if not os.path.exists(linkedin_file):
        print(f"❌ LinkedIn file not found: {linkedin_file}")
        return
    
    try:
        # Load raw LinkedIn data
        print(f"📂 Loading raw LinkedIn data from {linkedin_file}...")
        df = pd.read_csv(linkedin_file)
        print(f"✅ Loaded {len(df)} LinkedIn jobs")
        
        # Show available columns
        print(f"\n📋 Available columns:")
        for i, col in enumerate(df.columns):
            print(f"  {i+1:2d}. {col}")
        
        # Take a small sample (first 3 jobs)
        sample_size = min(3, len(df))
        sample_df = df.head(sample_size).copy()
        print(f"\n🔬 Testing on {sample_size} sample jobs")
        
        # Show sample data
        print(f"\n📋 Sample jobs to test:")
        for idx, row in sample_df.iterrows():
            print(f"  {idx+1}. {row.get('job_title', 'N/A')} at {row.get('company_name', 'N/A')}")
            print(f"     Apply link: {row.get('apply_link', 'N/A')}")
            print()
        
        # Create extractor with faster settings for testing
        extractor = LinkedInRedirectExtractor(
            delay_between_requests=1,  # Faster for testing
            max_redirects=5,
            timeout=10
        )
        
        # Extract final links
        print("🔄 Testing redirect extraction...")
        sample_with_final_links = extractor.extract_final_links(sample_df)
        
        # Show results
        print(f"\n📊 Test Results:")
        print("=" * 30)
        
        for idx, row in sample_with_final_links.iterrows():
            original = row.get('apply_link', '')
            final = row.get('final_apply_link', '')
            
            print(f"\n{idx+1}. {row.get('job_title', 'N/A')} at {row.get('company_name', 'N/A')}")
            print(f"   Original: {original}")
            print(f"   Final:    {final}")
            
            if final != original and final != '':
                print("   ✅ Redirect successful")
            else:
                print("   ⚠️ No redirect or failed")
        
        # Save test results
        test_output = "test_raw_redirects_sample.csv"
        sample_with_final_links.to_csv(test_output, index=False)
        print(f"\n💾 Test results saved to: {test_output}")
        
        # Count successful redirects
        successful = sum(1 for _, row in sample_with_final_links.iterrows() 
                        if row.get('final_apply_link', '') != row.get('apply_link', '') 
                        and row.get('final_apply_link', '') != '')
        
        print(f"\n📈 Test Summary:")
        print(f"   Total jobs tested: {sample_size}")
        print(f"   Successful redirects: {successful}")
        print(f"   Success rate: {(successful / sample_size) * 100:.1f}%")
        
        if successful > 0:
            print(f"\n✅ Test successful! The redirect extractor is working.")
        else:
            print(f"\n⚠️ No successful redirects in test sample.")
            print(f"   This might be due to:")
            print(f"   - Network issues")
            print(f"   - LinkedIn blocking requests")
            print(f"   - All test URLs already being final URLs")
            print(f"   - Rate limiting")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_raw_redirects()
