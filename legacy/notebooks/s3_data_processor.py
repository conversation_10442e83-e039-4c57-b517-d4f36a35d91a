import pandas as pd
import json
import numpy as np
import boto3
import os
import datetime
from typing import List, Dict, Any, Optional
from io import StringIO
import warnings

# Import preprocessing functions
from preprocessing_functions import extract_indeed_keyword, extract_linkedin_keyword

# Suppress pandas warnings
warnings.filterwarnings('ignore', category=FutureWarning)

class S3DataProcessor:
    """
    Process job data from S3 bucket for today's date.
    Loads, processes, and combines Indeed and LinkedIn data.
    """
    
    def __init__(self, aws_access_key: str = None, aws_secret_key: str = None, bucket_name: str = "bright-data-api"):
        """
        Initialize S3 data processor.
        
        Args:
            aws_access_key: AWS access key (if None, uses default credentials)
            aws_secret_key: AWS secret key (if None, uses default credentials)
            bucket_name: S3 bucket name
        """
        self.bucket_name = bucket_name
        
        # Initialize S3 client
        if aws_access_key and aws_secret_key:
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=aws_access_key,
                aws_secret_access_key=aws_secret_key
            )
        else:
            # Use default credentials (from environment, IAM role, etc.)
            self.s3_client = boto3.client('s3')
        
        # Get today's date for S3 path
        self.today = datetime.datetime.now().strftime("%Y-%m-%d")
        # self.today='2025-07-18'
        print(f"📅 Processing data for date: {self.today}")
        print(f"🪣 S3 Bucket: {bucket_name}")
    
    # Keyword extraction functions are now imported from preprocessing_functions.py

    def process_indeed_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Process Indeed DataFrame using the actual S3 data structure.
        Note: S3 data uses LinkedIn column names for both platforms.
        
        Args:
            df: Raw Indeed DataFrame
            
        Returns:
            pd.DataFrame: Processed Indeed DataFrame
        """
        if df.empty:
            return df
        
        processed_df = df.copy()
        
        # 1. Extract keyword using LinkedIn-specific extractor (S3 data uses LinkedIn format for both platforms)
        if 'discovery_input' in processed_df.columns:
            processed_df['keyword'] = processed_df['discovery_input'].apply(extract_linkedin_keyword)

        # 2. If 'apply_link' is empty, use the 'url'
        if 'apply_link' in processed_df.columns and 'url' in processed_df.columns:
            processed_df['apply_link'] = processed_df['apply_link'].replace('', np.nan)
            processed_df['apply_link'] = processed_df['apply_link'].fillna(processed_df['url'])

        # 3. Select and rename the columns - ACTUAL S3 DATA MAPPING (LinkedIn structure for both platforms)
        final_columns = {
            'keyword': 'keyword',
            'company_name': 'company_name',
            'job_posted_date': 'date_posted',  # S3 uses 'job_posted_date'
            'job_title': 'job_title',
            'job_summary': 'description',  # S3 uses 'job_summary' for both platforms
            'job_description_formatted': 'benefits',  # Using formatted description as benefits
            'job_function': 'qualification',  # Using job_function as qualification
            'job_employment_type': 'job_type',  # S3 uses 'job_employment_type'
            'job_location': 'location',  # S3 uses 'job_location'
            'job_base_pay_range': 'salary',  # S3 uses 'job_base_pay_range'
            'country_code': 'region',  # Using country_code as region
            'apply_link': 'apply_link'
        }

        # Select the original columns that are available in the dataframe
        columns_to_select = [col for col in final_columns.keys() if col in processed_df.columns]
        
        # Create the processed dataframe from the available columns
        processed_df = processed_df[columns_to_select].rename(columns=final_columns)
        
        # Reorder columns to match expected output: keyword, company_name, date_posted, job_title, description, benefits, qualification, job_type, location, salary, region, apply_link
        desired_order = ['keyword', 'company_name', 'date_posted', 'job_title', 'description', 'benefits', 'qualification', 'job_type', 'location', 'salary', 'region', 'apply_link']
        available_ordered_columns = [col for col in desired_order if col in processed_df.columns]
        processed_df = processed_df[available_ordered_columns]

        print(f"   📊 Indeed columns found: {columns_to_select}")
        print(f"   📋 Indeed processed: {len(processed_df)} rows with {len(processed_df.columns)} columns")
        print(f"   📋 Column order: {list(processed_df.columns)}")

        return processed_df

    def process_linkedin_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Process LinkedIn DataFrame using the actual S3 data structure.
        
        Args:
            df: Raw LinkedIn DataFrame
            
        Returns:
            pd.DataFrame: Processed LinkedIn DataFrame
        """
        if df.empty:
            return df
        
        processed_df = df.copy()
        
        # 1. Extract keyword using LinkedIn-specific extractor
        if 'discovery_input' in processed_df.columns:
            processed_df['keyword'] = processed_df['discovery_input'].apply(extract_linkedin_keyword)

        # 2. If 'apply_link' is empty, use the 'url'
        if 'apply_link' in processed_df.columns and 'url' in processed_df.columns:
            processed_df['apply_link'] = processed_df['apply_link'].replace('', np.nan)
            processed_df['apply_link'] = processed_df['apply_link'].fillna(processed_df['url'])

        # 3. Select and rename the columns - ACTUAL S3 DATA MAPPING
        final_columns = {
            'keyword': 'keyword',
            'company_name': 'company_name',
            'job_posted_date': 'date_posted',  # S3 uses 'job_posted_date'
            'job_title': 'job_title',
            'job_summary': 'description',  # S3 uses 'job_summary'
            'job_description_formatted': 'benefits',  # Using formatted description as benefits
            'job_function': 'qualification',  # Using job_function as qualification
            'job_employment_type': 'job_type',  # S3 uses 'job_employment_type'
            'job_location': 'location',  # S3 uses 'job_location'
            'job_base_pay_range': 'salary',  # S3 uses 'job_base_pay_range'
            'country_code': 'region',  # Using country_code as region
            'apply_link': 'apply_link'
        }

        # Select the original columns that are available in the dataframe
        columns_to_select = [col for col in final_columns.keys() if col in processed_df.columns]
        
        # Create the processed dataframe from the available columns
        processed_df = processed_df[columns_to_select].rename(columns=final_columns)
        
        # Reorder columns to match expected output: keyword, company_name, date_posted, job_title, description, benefits, qualification, job_type, location, salary, region, apply_link
        desired_order = ['keyword', 'company_name', 'date_posted', 'job_title', 'description', 'benefits', 'qualification', 'job_type', 'location', 'salary', 'region', 'apply_link']
        available_ordered_columns = [col for col in desired_order if col in processed_df.columns]
        processed_df = processed_df[available_ordered_columns]

        print(f"   📊 LinkedIn columns found: {columns_to_select}")
        print(f"   📋 LinkedIn processed: {len(processed_df)} rows with {len(processed_df.columns)} columns")
        print(f"   📋 Column order: {list(processed_df.columns)}")

        return processed_df

    def list_s3_files(self, platform: str) -> List[str]:
        """
        List all CSV files in S3 for today's date and platform.
        
        Args:
            platform: 'indeed' or 'linkedin'
            
        Returns:
            List[str]: List of S3 object keys
        """
        prefix = f"{self.today}/{platform}/"
        
        try:
            response = self.s3_client.list_objects_v2(
                Bucket=self.bucket_name,
                Prefix=prefix
            )
            
            if 'Contents' not in response:
                print(f"⚠️  No files found for {platform} on {self.today}")
                return []
            
            csv_files = [
                obj['Key'] for obj in response['Contents'] 
                if obj['Key'].endswith('.csv')
            ]
            
            print(f"📂 Found {len(csv_files)} CSV files for {platform}")
            return csv_files
            
        except Exception as e:
            print(f"❌ Error listing S3 files for {platform}: {str(e)}")
            return []

    def load_csv_from_s3(self, s3_key: str) -> pd.DataFrame:
        """
        Load a single CSV file from S3.
        
        Args:
            s3_key: S3 object key
            
        Returns:
            pd.DataFrame: Loaded DataFrame
        """
        try:
            response = self.s3_client.get_object(Bucket=self.bucket_name, Key=s3_key)
            csv_content = response['Body'].read().decode('utf-8')
            df = pd.read_csv(StringIO(csv_content))
            
            print(f"   ✅ {s3_key}: {len(df)} rows loaded")
            return df
            
        except Exception as e:
            print(f"   ❌ Error loading {s3_key}: {str(e)}")
            return pd.DataFrame()

    def load_platform_data_from_s3(self, platform: str) -> pd.DataFrame:
        """
        Load all CSV files for a platform from S3 and concatenate them.
        
        Args:
            platform: 'indeed' or 'linkedin'
            
        Returns:
            pd.DataFrame: Concatenated DataFrame
        """
        print(f"📂 Loading {platform.upper()} data from S3...")
        
        csv_files = self.list_s3_files(platform)
        
        if not csv_files:
            return pd.DataFrame()
        
        all_dfs = []
        for s3_key in csv_files:
            df = self.load_csv_from_s3(s3_key)
            if not df.empty:
                all_dfs.append(df)
        
        if all_dfs:
            combined_df = pd.concat(all_dfs, ignore_index=True)
            print(f"📊 {platform.upper()} total: {len(combined_df)} rows from {len(all_dfs)} files")
            return combined_df
        else:
            print(f"⚠️  No valid data found for {platform}")
            return pd.DataFrame()

    def process_and_combine_data(self, save_individual: bool = True, save_combined: bool = True) -> pd.DataFrame:
        """
        Complete workflow: Load, process, and combine data from both platforms.
        
        Args:
            save_individual: Save individual processed files
            save_combined: Save combined file
            
        Returns:
            pd.DataFrame: Final combined DataFrame
        """
        print("🚀 STARTING COMPLETE DATA PROCESSING WORKFLOW")
        print("=" * 60)
        
        # Load raw data from S3
        indeed_raw = self.load_platform_data_from_s3('indeed')
        linkedin_raw = self.load_platform_data_from_s3('linkedin')
        
        print("\n" + "-" * 40)
        
        # Process data
        print("\n⚙️  PROCESSING DATA...")
        
        indeed_processed = pd.DataFrame()
        linkedin_processed = pd.DataFrame()
        
        if not indeed_raw.empty:
            indeed_processed = self.process_indeed_data(indeed_raw)
            print(f"✅ Indeed processed: {len(indeed_processed)} rows")
            
            if save_individual:
                indeed_filename = f"indeed_processed_{self.today.replace('-', '')}.csv"
                indeed_processed.to_csv(indeed_filename, index=False)
                print(f"💾 Indeed data saved to: {indeed_filename}")
        else:
            print("⚠️  No Indeed data to process")
        
        if not linkedin_raw.empty:
            linkedin_processed = self.process_linkedin_data(linkedin_raw)
            print(f"✅ LinkedIn processed: {len(linkedin_processed)} rows")
            
            if save_individual:
                linkedin_filename = f"linkedin_processed_{self.today.replace('-', '')}.csv"
                linkedin_processed.to_csv(linkedin_filename, index=False)
                print(f"💾 LinkedIn data saved to: {linkedin_filename}")
        else:
            print("⚠️  No LinkedIn data to process")
        
        print("\n" + "-" * 40)
        
        # Combine data
        print("\n🔗 COMBINING DATA...")
        
        dfs_to_combine = []
        if not indeed_processed.empty:
            dfs_to_combine.append(indeed_processed)
        if not linkedin_processed.empty:
            dfs_to_combine.append(linkedin_processed)
        
        if dfs_to_combine:
            combined_df = pd.concat(dfs_to_combine, ignore_index=True)
            
            print(f"✅ Combined dataset: {len(combined_df)} rows")
            
            # Show keyword distribution
            if 'keyword' in combined_df.columns:
                keyword_counts = combined_df['keyword'].value_counts().head(5)
                print(f"📋 Top keywords:")
                for keyword, count in keyword_counts.items():
                    print(f"   • {keyword}: {count} jobs")
            
            # Show platform distribution by counting source files
            indeed_count = len(indeed_processed) if not indeed_processed.empty else 0
            linkedin_count = len(linkedin_processed) if not linkedin_processed.empty else 0
            print(f"📊 Platform distribution:")
            if indeed_count > 0:
                print(f"   • indeed: {indeed_count} jobs")
            if linkedin_count > 0:
                print(f"   • linkedin: {linkedin_count} jobs")
            
            if save_combined:
                combined_filename = f"combined_jobs_{self.today.replace('-', '')}.csv"
                combined_df.to_csv(combined_filename, index=False)
                print(f"💾 Combined data saved to: {combined_filename}")
            
            return combined_df
        else:
            print("⚠️  No data to combine")
            return pd.DataFrame()

    def get_data_summary(self) -> Dict[str, Any]:
        """
        Get summary of available data in S3 for today.
        
        Returns:
            Dict: Summary information
        """
        print("📊 DATA SUMMARY FOR TODAY")
        print("=" * 30)
        
        indeed_files = self.list_s3_files('indeed')
        linkedin_files = self.list_s3_files('linkedin')
        
        summary = {
            "date": self.today,
            "bucket": self.bucket_name,
            "indeed": {
                "files_count": len(indeed_files),
                "files": indeed_files
            },
            "linkedin": {
                "files_count": len(linkedin_files),
                "files": linkedin_files
            },
            "total_files": len(indeed_files) + len(linkedin_files)
        }
        
        print(f"📅 Date: {self.today}")
        print(f"🪣 Bucket: {self.bucket_name}")
        print(f"🎯 Indeed files: {len(indeed_files)}")
        print(f"🔗 LinkedIn files: {len(linkedin_files)}")
        print(f"📁 Total files: {summary['total_files']}")
        
        return summary

    def download_and_process_locally(self, local_dir: str = "s3_data") -> pd.DataFrame:
        """
        Download S3 data locally and then process it.
        
        Args:
            local_dir: Local directory to save files
            
        Returns:
            pd.DataFrame: Combined processed data
        """
        print(f"📥 DOWNLOADING S3 DATA TO LOCAL DIRECTORY: {local_dir}")
        print("=" * 50)
        
        # Create local directories
        os.makedirs(f"{local_dir}/indeed", exist_ok=True)
        os.makedirs(f"{local_dir}/linkedin", exist_ok=True)
        
        # Download Indeed files
        indeed_files = self.list_s3_files('indeed')
        for s3_key in indeed_files:
            local_path = os.path.join(local_dir, s3_key)
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            
            try:
                self.s3_client.download_file(self.bucket_name, s3_key, local_path)
                print(f"   ✅ Downloaded: {s3_key}")
            except Exception as e:
                print(f"   ❌ Error downloading {s3_key}: {str(e)}")
        
        # Download LinkedIn files
        linkedin_files = self.list_s3_files('linkedin')
        for s3_key in linkedin_files:
            local_path = os.path.join(local_dir, s3_key)
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            
            try:
                self.s3_client.download_file(self.bucket_name, s3_key, local_path)
                print(f"   ✅ Downloaded: {s3_key}")
            except Exception as e:
                print(f"   ❌ Error downloading {s3_key}: {str(e)}")
        
        print(f"\n📁 Files downloaded to: {local_dir}")
        
        # Now process the downloaded data
        return self.process_and_combine_data()

# Convenience functions
def process_todays_data(aws_access_key: str = None, aws_secret_key: str = None, 
                       bucket_name: str = "bright-data-api") -> pd.DataFrame:
    """
    One-command function to process today's job data from S3.
    
    Args:
        aws_access_key: AWS access key (optional)
        aws_secret_key: AWS secret key (optional)
        bucket_name: S3 bucket name
        
    Returns:
        pd.DataFrame: Combined processed data
    """
    processor = S3DataProcessor(aws_access_key, aws_secret_key, bucket_name)
    return processor.process_and_combine_data()

def get_todays_data_summary(aws_access_key: str = None, aws_secret_key: str = None,
                           bucket_name: str = "bright-data-api") -> Dict[str, Any]:
    """
    Get summary of today's available data in S3.
    
    Args:
        aws_access_key: AWS access key (optional)
        aws_secret_key: AWS secret key (optional)
        bucket_name: S3 bucket name
        
    Returns:
        Dict: Summary information
    """
    processor = S3DataProcessor(aws_access_key, aws_secret_key, bucket_name)
    return processor.get_data_summary()

if __name__ == "__main__":
    # Example usage
    print("🚀 S3 Data Processor - Example Usage")
    print("=" * 50)
    
    # Use the credentials from brightdata_scraper.py
    AWS_ACCESS_KEY = "********************"
    AWS_SECRET_KEY = "n+6MxKRIu03f4kN4/EvZc6fy5ZAB98CKKd/wLWzW"
    
    # Create processor
    processor = S3DataProcessor(AWS_ACCESS_KEY, AWS_SECRET_KEY)
    
    # Get summary first
    summary = processor.get_data_summary()
    
    if summary['total_files'] > 0:
        print("\n💡 Files found! Processing data...")
        # Process and combine data
        combined_df = processor.process_and_combine_data()
        
        if not combined_df.empty:
            print(f"\n🎉 SUCCESS! Processed {len(combined_df)} total job records")
            print(f"📊 Sample data:")
            print(combined_df.head())
        else:
            print("\n⚠️  No data was processed")
    else:
        print("\n⚠️  No files found for today. Make sure scraping has completed.")
        print("💡 You can run the scraper first using brightdata_scraper.py")