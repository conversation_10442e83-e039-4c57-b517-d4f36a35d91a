import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import warnings

# Suppress pandas warnings
warnings.filterwarnings('ignore', category=FutureWarning)

class JobDateFilter:
    """
    Filter job listings to keep only recent entries (within 1 day of today).
    Removes listings that are more than 1 day old.
    """
    
    def __init__(self, max_days_old: int = 1):
        """
        Initialize the job date filter.
        
        Args:
            max_days_old: Maximum number of days old to keep (default: 1)
        """
        self.max_days_old = max_days_old
        self.today = datetime.now().date()
        self.cutoff_date = self.today - timedelta(days=max_days_old)
        
        print(f"📅 Today's date: {self.today}")
        print(f"🗓️  Cutoff date: {self.cutoff_date}")
        print(f"⏰ Keeping jobs from {self.cutoff_date} onwards")
    
    def parse_date(self, date_str):
        """
        Parse various date formats to datetime.date object.
        
        Args:
            date_str: Date string in various formats
            
        Returns:
            datetime.date or None if parsing fails
        """
        if pd.isna(date_str) or not isinstance(date_str, str):
            return None
        
        # Common date formats to try
        date_formats = [
            '%Y-%m-%d',           # 2025-07-19
            '%Y-%m-%dT%H:%M:%S.%fZ',  # 2025-07-19T10:30:45.123Z
            '%Y-%m-%dT%H:%M:%SZ',     # 2025-07-19T10:30:45Z
            '%Y-%m-%dT%H:%M:%S',      # 2025-07-19T10:30:45
            '%m/%d/%Y',           # 07/19/2025
            '%d/%m/%Y',           # 19/07/2025
            '%Y/%m/%d',           # 2025/07/19
            '%B %d, %Y',          # July 19, 2025
            '%b %d, %Y',          # Jul 19, 2025
            '%d %B %Y',           # 19 July 2025
            '%d %b %Y',           # 19 Jul 2025
        ]
        
        for fmt in date_formats:
            try:
                parsed_date = datetime.strptime(date_str.strip(), fmt).date()
                return parsed_date
            except ValueError:
                continue
        
        # Try to extract just the date part if it's a datetime string
        try:
            # Handle ISO format with timezone
            if 'T' in date_str:
                date_part = date_str.split('T')[0]
                return datetime.strptime(date_part, '%Y-%m-%d').date()
        except ValueError:
            pass
        
        return None
    
    def filter_recent_jobs(self, df: pd.DataFrame, date_column: str = 'date_posted') -> pd.DataFrame:
        """
        Filter DataFrame to keep only recent job listings.
        
        Args:
            df: Input DataFrame with job listings
            date_column: Name of the date column to filter on
            
        Returns:
            pd.DataFrame: Filtered DataFrame with recent jobs only
        """
        if df.empty:
            print("⚠️  Input DataFrame is empty")
            return df
        
        if date_column not in df.columns:
            print(f"⚠️  Column '{date_column}' not found in DataFrame")
            print(f"📋 Available columns: {list(df.columns)}")
            return df
        
        print(f"\n🔍 FILTERING RECENT JOBS")
        print("=" * 40)
        print(f"📊 Original dataset: {len(df)} jobs")
        
        # Parse dates
        print(f"📅 Parsing dates from '{date_column}' column...")
        df_filtered = df.copy()
        df_filtered['parsed_date'] = df_filtered[date_column].apply(self.parse_date)
        
        # Check parsing success
        parsed_count = df_filtered['parsed_date'].notna().sum()
        unparsed_count = df_filtered['parsed_date'].isna().sum()
        
        print(f"✅ Successfully parsed: {parsed_count} dates")
        print(f"❌ Failed to parse: {unparsed_count} dates")
        
        if unparsed_count > 0:
            print(f"\n🔍 Sample unparsed dates:")
            unparsed_samples = df_filtered[df_filtered['parsed_date'].isna()][date_column].head(5)
            for i, sample in enumerate(unparsed_samples, 1):
                print(f"  {i}. {sample}")
        
        # Filter by date
        print(f"\n🗓️  Filtering jobs posted on or after {self.cutoff_date}...")
        
        # Keep jobs with valid dates that are recent, and jobs with unparsed dates (to be safe)
        recent_jobs_mask = (
            (df_filtered['parsed_date'].isna()) |  # Keep unparsed dates
            (df_filtered['parsed_date'] >= self.cutoff_date)  # Keep recent dates
        )
        
        df_recent = df_filtered[recent_jobs_mask].copy()
        
        # Remove the temporary parsed_date column
        df_recent = df_recent.drop('parsed_date', axis=1)
        
        # Show results
        removed_count = len(df) - len(df_recent)
        print(f"✅ Kept: {len(df_recent)} jobs")
        print(f"🗑️  Removed: {removed_count} jobs (older than {self.cutoff_date})")
        
        # Show date distribution of kept jobs
        if parsed_count > 0:
            df_temp = df_filtered[recent_jobs_mask].copy()
            valid_dates = df_temp['parsed_date'].dropna()
            if len(valid_dates) > 0:
                date_counts = valid_dates.value_counts().sort_index()
                print(f"\n📊 Date distribution of kept jobs:")
                for date, count in date_counts.items():
                    print(f"   • {date}: {count} jobs")
        
        return df_recent
    
    def process_file(self, input_file: str, output_file: str = None, date_column: str = 'date_posted') -> pd.DataFrame:
        """
        Process a CSV file to filter recent jobs.
        
        Args:
            input_file: Path to input CSV file
            output_file: Path to output CSV file (optional)
            date_column: Name of the date column to filter on
            
        Returns:
            pd.DataFrame: Filtered DataFrame
        """
        print(f"📂 PROCESSING FILE: {input_file}")
        print("=" * 50)
        
        # Check if file exists
        if not os.path.exists(input_file):
            print(f"❌ File not found: {input_file}")
            return pd.DataFrame()
        
        # Load data
        try:
            df = pd.read_csv(input_file)
            print(f"✅ Loaded {len(df)} rows from {input_file}")
        except Exception as e:
            print(f"❌ Error loading file: {str(e)}")
            return pd.DataFrame()
        
        # Filter recent jobs
        df_filtered = self.filter_recent_jobs(df, date_column)
        
        # Save filtered data
        if output_file:
            try:
                df_filtered.to_csv(output_file, index=False)
                print(f"💾 Filtered data saved to: {output_file}")
            except Exception as e:
                print(f"❌ Error saving file: {str(e)}")
        
        return df_filtered

def filter_recent_jobs_from_file(input_file: str, output_file: str = None, 
                                max_days_old: int = 1, date_column: str = 'date_posted') -> pd.DataFrame:
    """
    Convenience function to filter recent jobs from a CSV file.
    
    Args:
        input_file: Path to input CSV file
        output_file: Path to output CSV file (optional, auto-generated if None)
        max_days_old: Maximum number of days old to keep (default: 1)
        date_column: Name of the date column to filter on
        
    Returns:
        pd.DataFrame: Filtered DataFrame
    """
    # Auto-generate output filename if not provided
    if output_file is None:
        base_name = os.path.splitext(input_file)[0]
        today_str = datetime.now().strftime("%Y%m%d")
        output_file = f"{base_name}_recent_{today_str}.csv"
    
    # Create filter and process file
    filter_obj = JobDateFilter(max_days_old)
    return filter_obj.process_file(input_file, output_file, date_column)

def filter_all_recent_files(max_days_old: int = 1):
    """
    Filter all combined job files in the current directory to keep only recent jobs.
    
    Args:
        max_days_old: Maximum number of days old to keep (default: 1)
    """
    print("🔍 SEARCHING FOR COMBINED JOB FILES")
    print("=" * 50)
    
    # Look for combined job files
    combined_files = []
    for file in os.listdir('.'):
        if file.startswith('combined_jobs_') and file.endswith('.csv'):
            combined_files.append(file)
    
    if not combined_files:
        print("⚠️  No combined job files found in current directory")
        print("💡 Looking for files matching pattern: combined_jobs_*.csv")
        return
    
    print(f"📂 Found {len(combined_files)} combined job files:")
    for file in combined_files:
        print(f"   • {file}")
    
    # Process each file
    for file in combined_files:
        print(f"\n" + "=" * 60)
        filter_recent_jobs_from_file(file, max_days_old=max_days_old)

if __name__ == "__main__":
    print("🚀 Job Date Filter - Recent Jobs Only")
    print("=" * 50)
    
    # Example usage - filter the most recent combined file
    today_str = datetime.now().strftime("%Y-%m-%d")
    yesterday_str = '2025-07-18'
    
    # Try to find today's or yesterday's combined file
    possible_files = [
        f"combined_jobs_{today_str}.csv",
        f"combined_jobs_{yesterday_str}.csv",
        "combined_jobs_20250718.csv"  # Your current file
    ]
    
    input_file = None
    for file in possible_files:
        if os.path.exists(file):
            input_file = file
            break
    
    if input_file:
        print(f"📂 Processing file: {input_file}")
        filtered_df = filter_recent_jobs_from_file(input_file)
        
        if not filtered_df.empty:
            print(f"\n🎉 SUCCESS! Filtered to {len(filtered_df)} recent jobs")
            print(f"📊 Sample of filtered data:")
            print(filtered_df[['keyword', 'company_name', 'date_posted', 'job_title']].head())
        else:
            print("\n⚠️  No data after filtering")
    else:
        print("⚠️  No combined job files found")
        print("💡 Available files in current directory:")
        csv_files = [f for f in os.listdir('.') if f.endswith('.csv')]
        for file in csv_files[:10]:  # Show first 10 CSV files
            print(f"   • {file}")
        
        if len(csv_files) > 10:
            print(f"   ... and {len(csv_files) - 10} more CSV files")
        
        print(f"\n💡 You can also run:")
        print(f"filter_all_recent_files()  # To process all combined_jobs_*.csv files")
        print(f"filter_recent_jobs_from_file('your_file.csv')  # To process a specific file")