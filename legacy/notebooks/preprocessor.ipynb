{"cells": [{"cell_type": "code", "execution_count": 11, "id": "67fa759a", "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "\n", "def load_linkedin_data(folder_path='/Users/<USER>/Documents/scrapper/src/linkedin_data'):\n", "    \"\"\"\n", "    Load and concatenate all CSV files from the linkedin_data folder.\n", "    Returns a pandas DataFrame.\n", "    \"\"\"\n", "    all_dfs = []\n", "    if not os.path.exists(folder_path):\n", "        print(f\"⚠️ Folder {folder_path} does not exist\")\n", "        return pd.DataFrame()\n", "    csv_files = [f for f in os.listdir(folder_path) if f.endswith('.csv')]\n", "    if not csv_files:\n", "        print(f\"⚠️ No CSV files found in {folder_path}\")\n", "        return pd.DataFrame()\n", "    for filename in csv_files:\n", "        file_path = os.path.join(folder_path, filename)\n", "        try:\n", "            df = pd.read_csv(file_path)\n", "            all_dfs.append(df)\n", "            print(f\"✅ {filename}: {len(df)} rows loaded\")\n", "        except Exception as e:\n", "            print(f\"❌ Error loading {filename}: {str(e)}\")\n", "    if all_dfs:\n", "        combined_df = pd.concat(all_dfs, ignore_index=True)\n", "        print(f\"📊 LinkedIn: {len(combined_df)} rows from {len(all_dfs)} files\")\n", "        return combined_df\n", "    else:\n", "        return pd.DataFrame()\n", "\n", "def load_indeed_data(folder_path='/Users/<USER>/Documents/scrapper/src/indeed_data'):\n", "    \"\"\"\n", "    Load and concatenate all CSV files from the indeed_data folder.\n", "    Returns a pandas DataFrame.\n", "    \"\"\"\n", "    all_dfs = []\n", "    if not os.path.exists(folder_path):\n", "        print(f\"⚠️ Folder {folder_path} does not exist\")\n", "        return pd.DataFrame()\n", "    csv_files = [f for f in os.listdir(folder_path) if f.endswith('.csv')]\n", "    if not csv_files:\n", "        print(f\"⚠️ No CSV files found in {folder_path}\")\n", "        return pd.DataFrame()\n", "    for filename in csv_files:\n", "        file_path = os.path.join(folder_path, filename)\n", "        try:\n", "            df = pd.read_csv(file_path)\n", "            all_dfs.append(df)\n", "            print(f\"✅ {filename}: {len(df)} rows loaded\")\n", "        except Exception as e:\n", "            print(f\"❌ Error loading {filename}: {str(e)}\")\n", "    if all_dfs:\n", "        combined_df = pd.concat(all_dfs, ignore_index=True)\n", "        print(f\"📊 Indeed: {len(combined_df)} rows from {len(all_dfs)} files\")\n", "        return combined_df\n", "    else:\n", "        return pd.DataFrame()"]}, {"cell_type": "code", "execution_count": 12, "id": "d7925a42", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ bd_20250710_054848_0.csv: 2336 rows loaded\n", "📊 LinkedIn: 2336 rows from 1 files\n"]}], "source": ["linkedin_df=load_linkedin_data()"]}, {"cell_type": "code", "execution_count": 13, "id": "0d726351", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>url</th>\n", "      <th>job_posting_id</th>\n", "      <th>job_title</th>\n", "      <th>company_name</th>\n", "      <th>company_id</th>\n", "      <th>job_location</th>\n", "      <th>job_summary</th>\n", "      <th>applay_link</th>\n", "      <th>job_seniority_level</th>\n", "      <th>job_function</th>\n", "      <th>...</th>\n", "      <th>job_description_formatted</th>\n", "      <th>selective_search</th>\n", "      <th>base_salary</th>\n", "      <th>salary_standards</th>\n", "      <th>timestamp</th>\n", "      <th>input</th>\n", "      <th>error</th>\n", "      <th>error_code</th>\n", "      <th>warning</th>\n", "      <th>warning_code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>https://www.linkedin.com/jobs/view/crm-custome...</td>\n", "      <td>4263744251</td>\n", "      <td>CRM Customer &amp; Data Analyst</td>\n", "      <td><PERSON></td>\n", "      <td>40715.0</td>\n", "      <td>New York, NY</td>\n", "      <td>Who You Are: The CRM Customer and Data Analyst...</td>\n", "      <td>NaN</td>\n", "      <td>Associate</td>\n", "      <td>Analyst, Marketing, and Strategy/Planning</td>\n", "      <td>...</td>\n", "      <td>&lt;section class=\"show-more-less-html\" data-max-...</td>\n", "      <td>NaN</td>\n", "      <td>{\"min_amount\":90000,\"max_amount\":115000,\"curre...</td>\n", "      <td>This range is provided by <PERSON>. Your act...</td>\n", "      <td>2025-07-10T05:56:24.651Z</td>\n", "      <td>{\"url\":\"https://www.linkedin.com/jobs/view/crm...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>https://www.linkedin.com/jobs/view/director-of...</td>\n", "      <td>4264720338</td>\n", "      <td>Director of Digital Marketing</td>\n", "      <td>UNIQLO</td>\n", "      <td>66256.0</td>\n", "      <td>New York City Metropolitan Area</td>\n", "      <td>Company Overview: Apparel that comes from the ...</td>\n", "      <td>NaN</td>\n", "      <td>Director</td>\n", "      <td>Other and Marketing</td>\n", "      <td>...</td>\n", "      <td>&lt;section class=\"show-more-less-html\" data-max-...</td>\n", "      <td>NaN</td>\n", "      <td>{\"min_amount\":165000,\"max_amount\":170000,\"curr...</td>\n", "      <td>Retrieved from the description.</td>\n", "      <td>2025-07-10T05:56:25.913Z</td>\n", "      <td>{\"url\":\"https://www.linkedin.com/jobs/view/dir...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>https://www.linkedin.com/jobs/view/business-an...</td>\n", "      <td>4263386500</td>\n", "      <td>Business Analyst – Fixed Income</td>\n", "      <td>iPivot</td>\n", "      <td>28387428.0</td>\n", "      <td>New York, NY</td>\n", "      <td>Job Description: We are seeking a Technical Bu...</td>\n", "      <td>NaN</td>\n", "      <td>Mid-Senior level</td>\n", "      <td>Finance</td>\n", "      <td>...</td>\n", "      <td>&lt;section class=\"show-more-less-html\" data-max-...</td>\n", "      <td>NaN</td>\n", "      <td>{\"min_amount\":null,\"max_amount\":null,\"currency...</td>\n", "      <td>NaN</td>\n", "      <td>2025-07-10T05:56:26.256Z</td>\n", "      <td>{\"url\":\"https://www.linkedin.com/jobs/view/bus...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>https://www.linkedin.com/jobs/view/business-an...</td>\n", "      <td>4261140156</td>\n", "      <td>Business Analyst</td>\n", "      <td>City of New York</td>\n", "      <td>2904.0</td>\n", "      <td>Manhattan, NY</td>\n", "      <td>Note: This position is open only to current em...</td>\n", "      <td>NaN</td>\n", "      <td>Mid-Senior level</td>\n", "      <td>Research, Analyst, and Information Technology</td>\n", "      <td>...</td>\n", "      <td>&lt;section class=\"show-more-less-html\" data-max-...</td>\n", "      <td>NaN</td>\n", "      <td>{\"min_amount\":null,\"max_amount\":null,\"currency...</td>\n", "      <td>NaN</td>\n", "      <td>2025-07-10T05:56:26.269Z</td>\n", "      <td>{\"url\":\"https://www.linkedin.com/jobs/view/bus...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>https://www.linkedin.com/jobs/view/business-an...</td>\n", "      <td>4261152575</td>\n", "      <td>Business Analyst</td>\n", "      <td>ASK Consulting</td>\n", "      <td>388480.0</td>\n", "      <td>New York, NY</td>\n", "      <td>\"All candidates must be directly contracted by...</td>\n", "      <td>NaN</td>\n", "      <td>Mid-Senior level</td>\n", "      <td>Information Technology</td>\n", "      <td>...</td>\n", "      <td>&lt;section class=\"show-more-less-html\" data-max-...</td>\n", "      <td>NaN</td>\n", "      <td>{\"min_amount\":70,\"max_amount\":75,\"currency\":\"$...</td>\n", "      <td>Retrieved from the description.</td>\n", "      <td>2025-07-10T05:56:26.357Z</td>\n", "      <td>{\"url\":\"https://www.linkedin.com/jobs/view/bus...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 34 columns</p>\n", "</div>"], "text/plain": ["                                                 url  job_posting_id  \\\n", "0  https://www.linkedin.com/jobs/view/crm-custome...      4263744251   \n", "1  https://www.linkedin.com/jobs/view/director-of...      4264720338   \n", "2  https://www.linkedin.com/jobs/view/business-an...      4263386500   \n", "3  https://www.linkedin.com/jobs/view/business-an...      4261140156   \n", "4  https://www.linkedin.com/jobs/view/business-an...      4261152575   \n", "\n", "                         job_title      company_name  company_id  \\\n", "0      CRM Customer & Data Analyst        Jimmy <PERSON>     40715.0   \n", "1    Director of Digital Marketing            UNIQLO     66256.0   \n", "2  Business Analyst – Fixed Income            iPivot  28387428.0   \n", "3                 Business Analyst  City of New York      2904.0   \n", "4                 Business Analyst    ASK Consulting    388480.0   \n", "\n", "                      job_location  \\\n", "0                     New York, NY   \n", "1  New York City Metropolitan Area   \n", "2                     New York, NY   \n", "3                    Manhattan, NY   \n", "4                     New York, NY   \n", "\n", "                                         job_summary  applay_link  \\\n", "0  Who You Are: The CRM Customer and Data Analyst...          NaN   \n", "1  Company Overview: Apparel that comes from the ...          NaN   \n", "2  Job Description: We are seeking a Technical Bu...          NaN   \n", "3  Note: This position is open only to current em...          NaN   \n", "4  \"All candidates must be directly contracted by...          NaN   \n", "\n", "  job_seniority_level                                   job_function  ...  \\\n", "0           Associate      Analyst, Marketing, and Strategy/Planning  ...   \n", "1            Director                            Other and Marketing  ...   \n", "2    Mid-Senior level                                        Finance  ...   \n", "3    Mid-Senior level  Research, Analyst, and Information Technology  ...   \n", "4    Mid-Senior level                         Information Technology  ...   \n", "\n", "                           job_description_formatted selective_search  \\\n", "0  <section class=\"show-more-less-html\" data-max-...              NaN   \n", "1  <section class=\"show-more-less-html\" data-max-...              NaN   \n", "2  <section class=\"show-more-less-html\" data-max-...              NaN   \n", "3  <section class=\"show-more-less-html\" data-max-...              NaN   \n", "4  <section class=\"show-more-less-html\" data-max-...              NaN   \n", "\n", "                                         base_salary  \\\n", "0  {\"min_amount\":90000,\"max_amount\":115000,\"curre...   \n", "1  {\"min_amount\":165000,\"max_amount\":170000,\"curr...   \n", "2  {\"min_amount\":null,\"max_amount\":null,\"currency...   \n", "3  {\"min_amount\":null,\"max_amount\":null,\"currency...   \n", "4  {\"min_amount\":70,\"max_amount\":75,\"currency\":\"$...   \n", "\n", "                                    salary_standards  \\\n", "0  This range is provided by <PERSON>. Your act...   \n", "1                    Retrieved from the description.   \n", "2                                                NaN   \n", "3                                                NaN   \n", "4                    Retrieved from the description.   \n", "\n", "                  timestamp  \\\n", "0  2025-07-10T05:56:24.651Z   \n", "1  2025-07-10T05:56:25.913Z   \n", "2  2025-07-10T05:56:26.256Z   \n", "3  2025-07-10T05:56:26.269Z   \n", "4  2025-07-10T05:56:26.357Z   \n", "\n", "                                               input error error_code warning  \\\n", "0  {\"url\":\"https://www.linkedin.com/jobs/view/crm...   NaN        NaN     NaN   \n", "1  {\"url\":\"https://www.linkedin.com/jobs/view/dir...   NaN        NaN     NaN   \n", "2  {\"url\":\"https://www.linkedin.com/jobs/view/bus...   NaN        NaN     NaN   \n", "3  {\"url\":\"https://www.linkedin.com/jobs/view/bus...   NaN        NaN     NaN   \n", "4  {\"url\":\"https://www.linkedin.com/jobs/view/bus...   NaN        NaN     NaN   \n", "\n", "   warning_code  \n", "0           NaN  \n", "1           NaN  \n", "2           NaN  \n", "3           NaN  \n", "4           NaN  \n", "\n", "[5 rows x 34 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["linkedin_df.head()"]}, {"cell_type": "code", "execution_count": 14, "id": "b293f45a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 2336 entries, 0 to 2335\n", "Data columns (total 34 columns):\n", " #   Column                     Non-Null Count  Dtype  \n", "---  ------                     --------------  -----  \n", " 0   url                        2336 non-null   object \n", " 1   job_posting_id             2336 non-null   int64  \n", " 2   job_title                  2336 non-null   object \n", " 3   company_name               2335 non-null   object \n", " 4   company_id                 2335 non-null   float64\n", " 5   job_location               2336 non-null   object \n", " 6   job_summary                2336 non-null   object \n", " 7   applay_link                0 non-null      float64\n", " 8   job_seniority_level        2336 non-null   object \n", " 9   job_function               2327 non-null   object \n", " 10  job_employment_type        2336 non-null   object \n", " 11  job_industries             2336 non-null   object \n", " 12  job_base_pay_range         1545 non-null   object \n", " 13  company_url                2335 non-null   object \n", " 14  job_posted_time            2336 non-null   object \n", " 15  job_num_applicants         2336 non-null   int64  \n", " 16  discovery_input            2336 non-null   object \n", " 17  apply_link                 1677 non-null   object \n", " 18  country_code               2324 non-null   object \n", " 19  title_id                   2323 non-null   float64\n", " 20  company_logo               2334 non-null   object \n", " 21  job_posted_date            2336 non-null   object \n", " 22  job_poster                 2336 non-null   object \n", " 23  application_availability   2336 non-null   bool   \n", " 24  job_description_formatted  2336 non-null   object \n", " 25  selective_search           0 non-null      float64\n", " 26  base_salary                2336 non-null   object \n", " 27  salary_standards           1545 non-null   object \n", " 28  timestamp                  2336 non-null   object \n", " 29  input                      2336 non-null   object \n", " 30  error                      0 non-null      float64\n", " 31  error_code                 0 non-null      float64\n", " 32  warning                    0 non-null      float64\n", " 33  warning_code               0 non-null      float64\n", "dtypes: bool(1), float64(8), int64(2), object(23)\n", "memory usage: 604.7+ KB\n"]}], "source": ["linkedin_df.info()"]}, {"cell_type": "code", "execution_count": 21, "id": "68110786", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"location\":\"New York,Massachusetts,District of Columbia,New Jersey,California,Texas,Washington\",\"keyword\":\"Marketing\",\"country\":\"US\",\"time_range\":\"Past 24 hours\",\"job_type\":\"\",\"experience_level\":\"\",\"remote\":\"\",\"company\":\"\",\"location_radius\":\"\"}\n"]}], "source": ["print(linkedin_df['discovery_input'][0])"]}, {"cell_type": "code", "execution_count": 22, "id": "3687e2d0", "metadata": {}, "outputs": [], "source": ["import json\n", "linkedin_df['keyword'] = linkedin_df['discovery_input'].apply(lambda x: json.loads(x).get('keyword') if pd.notna(x) and isinstance(x, str) else None)"]}, {"cell_type": "code", "execution_count": 31, "id": "5303918e", "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "def process_linkedin_jobs(df):\n", "    \"\"\"\n", "    Processes LinkedIn job DataFrame to extract and rename required columns,\n", "    fill missing apply_link with url, and add work_authorization and work_mode columns using regex.\n", "    \"\"\"\n", "    # 1. Extract keyword from discovery_input\n", "    if 'discovery_input' in df.columns:\n", "        df['keyword'] = df['discovery_input'].apply(\n", "            lambda x: json.loads(x).get('keyword') if pd.notna(x) and isinstance(x, str) else None\n", "        )\n", "    else:\n", "        df['keyword'] = None\n", "\n", "    # 2. Fill missing apply_link with url\n", "    if 'apply_link' in df.columns and 'url' in df.columns:\n", "        df['apply_link'] = df['apply_link'].replace('', np.nan)\n", "        df['apply_link'] = df['apply_link'].fillna(df['url'])\n", "    elif 'url' in df.columns:\n", "        df['apply_link'] = df['url']\n", "\n", "    # 3. Add work_authorization column using regex\n", "    def extract_work_auth(desc):\n", "        if pd.isna(desc):\n", "            return \"not sure\"\n", "        desc_lower = desc.lower()\n", "        # Negative patterns\n", "        if re.search(r'\\bno h1b\\b|\\bnot eligible for h1b\\b|\\bno work authorization\\b|\\bnot authorized\\b', desc_lower):\n", "            return \"no\"\n", "        # Positive patterns\n", "        if re.search(r'\\bh1b\\b|\\bwork authorization\\b|\\bvisa sponsorship\\b', desc_lower):\n", "            return \"yes\"\n", "        return \"not sure\"\n", "\n", "    df['work_authorization'] = df['job_summary'].apply(extract_work_auth) if 'job_summary' in df.columns else \"not sure\"\n", "\n", "    # 4. Add work_mode column using regex\n", "    def extract_work_mode(desc):\n", "        if pd.isna(desc):\n", "            return \"not sure\"\n", "        desc_lower = desc.lower()\n", "        if re.search(r'\\bremote\\b', desc_lower):\n", "            return \"Remote\"\n", "        elif re.search(r'\\bhybrid\\b', desc_lower):\n", "            return \"Hybrid\"\n", "        elif re.search(r'\\bonsite\\b|\\bon-site\\b', desc_lower):\n", "            return \"Onsite\"\n", "        return \"not sure\"\n", "\n", "    df['work_mode'] = df['job_summary'].apply(extract_work_mode) if 'job_summary' in df.columns else \"not sure\"\n", "\n", "    # 5. Select and rename columns\n", "    column_mapping = {\n", "        'keyword': 'keyword',\n", "        'job_title': 'job_title',\n", "        'job_posted_date': 'date',\n", "        'job_location': 'location',\n", "        'job_summary': 'job_description',\n", "        'job_base_pay_range': 'salary',\n", "        'job_seniority_level': 'job_seniority',\n", "        'job_industries': 'job_industry',\n", "        'job_function': 'job_functions',\n", "        'job_employment_type': 'employment_type',\n", "        'apply_link': 'apply_link',\n", "        'work_authorization': 'work_authorization',\n", "        'work_mode': 'work_mode'\n", "    }\n", "    selected_columns = [col for col in column_mapping.keys() if col in df.columns]\n", "    processed_df = df[selected_columns].rename(columns=column_mapping)\n", "\n", "    # Reorder columns\n", "    desired_order = [\n", "        'keyword', 'job_title', 'date', 'location', 'job_description', 'salary',\n", "        'job_seniority', 'job_industry', 'job_functions', 'employment_type',\n", "        'apply_link', 'work_authorization', 'work_mode'\n", "    ]\n", "    processed_df = processed_df[[col for col in desired_order if col in processed_df.columns]]\n", "\n", "    return processed_df"]}, {"cell_type": "code", "execution_count": 32, "id": "43b47fce", "metadata": {}, "outputs": [], "source": ["linkedin_processed = process_linkedin_jobs(linkedin_df)"]}, {"cell_type": "code", "execution_count": 33, "id": "cd551ca3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>keyword</th>\n", "      <th>job_title</th>\n", "      <th>date</th>\n", "      <th>location</th>\n", "      <th>job_description</th>\n", "      <th>salary</th>\n", "      <th>job_seniority</th>\n", "      <th>job_industry</th>\n", "      <th>job_functions</th>\n", "      <th>employment_type</th>\n", "      <th>apply_link</th>\n", "      <th>work_authorization</th>\n", "      <th>work_mode</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Marketing</td>\n", "      <td>CRM Customer &amp; Data Analyst</td>\n", "      <td>2025-07-09T20:56:24.302Z</td>\n", "      <td>New York, NY</td>\n", "      <td>Who You Are: The CRM Customer and Data Analyst...</td>\n", "      <td>$90,000.00/yr - $115,000.00/yr</td>\n", "      <td>Associate</td>\n", "      <td>Retail Luxury Goods and Jewelry, Wholesale Lux...</td>\n", "      <td>Analyst, Marketing, and Strategy/Planning</td>\n", "      <td>Full-time</td>\n", "      <td>https://www.linkedin.com/jobs/view/crm-custome...</td>\n", "      <td>not sure</td>\n", "      <td>not sure</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Marketing</td>\n", "      <td>Director of Digital Marketing</td>\n", "      <td>2025-07-09T12:56:25.881Z</td>\n", "      <td>New York City Metropolitan Area</td>\n", "      <td>Company Overview: Apparel that comes from the ...</td>\n", "      <td>$165,000.00/yr - $170,000.00/yr</td>\n", "      <td>Director</td>\n", "      <td>Retail</td>\n", "      <td>Other and Marketing</td>\n", "      <td>Full-time</td>\n", "      <td>https://www.linkedin.com/jobs/view/director-of...</td>\n", "      <td>not sure</td>\n", "      <td>not sure</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Business Analyst</td>\n", "      <td>Business Analyst – Fixed Income</td>\n", "      <td>2025-07-09T14:56:26.219Z</td>\n", "      <td>New York, NY</td>\n", "      <td>Job Description: We are seeking a Technical Bu...</td>\n", "      <td>NaN</td>\n", "      <td>Mid-Senior level</td>\n", "      <td>Financial Services</td>\n", "      <td>Finance</td>\n", "      <td>Full-time</td>\n", "      <td>https://www.linkedin.com/jobs/view/business-an...</td>\n", "      <td>not sure</td>\n", "      <td>not sure</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Business Analyst</td>\n", "      <td>Business Analyst</td>\n", "      <td>2025-07-09T13:56:26.239Z</td>\n", "      <td>Manhattan, NY</td>\n", "      <td>Note: This position is open only to current em...</td>\n", "      <td>NaN</td>\n", "      <td>Mid-Senior level</td>\n", "      <td>Government Administration</td>\n", "      <td>Research, Analyst, and Information Technology</td>\n", "      <td>Full-time</td>\n", "      <td>https://www.linkedin.com/jobs/view/externalApp...</td>\n", "      <td>not sure</td>\n", "      <td>not sure</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Business Analyst</td>\n", "      <td>Business Analyst</td>\n", "      <td>2025-07-09T16:56:26.333Z</td>\n", "      <td>New York, NY</td>\n", "      <td>\"All candidates must be directly contracted by...</td>\n", "      <td>$70.00/hr - $75.00/hr</td>\n", "      <td>Mid-Senior level</td>\n", "      <td>Investment Banking</td>\n", "      <td>Information Technology</td>\n", "      <td>Contract</td>\n", "      <td>https://www.linkedin.com/jobs/view/business-an...</td>\n", "      <td>not sure</td>\n", "      <td>Onsite</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            keyword                        job_title  \\\n", "0         Marketing      CRM Customer & Data Analyst   \n", "1         Marketing    Director of Digital Marketing   \n", "2  Business Analyst  Business Analyst – Fixed Income   \n", "3  Business Analyst                 Business Analyst   \n", "4  Business Analyst                 Business Analyst   \n", "\n", "                       date                         location  \\\n", "0  2025-07-09T20:56:24.302Z                     New York, NY   \n", "1  2025-07-09T12:56:25.881Z  New York City Metropolitan Area   \n", "2  2025-07-09T14:56:26.219Z                     New York, NY   \n", "3  2025-07-09T13:56:26.239Z                    Manhattan, NY   \n", "4  2025-07-09T16:56:26.333Z                     New York, NY   \n", "\n", "                                     job_description  \\\n", "0  Who You Are: The CRM Customer and Data Analyst...   \n", "1  Company Overview: Apparel that comes from the ...   \n", "2  Job Description: We are seeking a Technical Bu...   \n", "3  Note: This position is open only to current em...   \n", "4  \"All candidates must be directly contracted by...   \n", "\n", "                            salary     job_seniority  \\\n", "0   $90,000.00/yr - $115,000.00/yr         Associate   \n", "1  $165,000.00/yr - $170,000.00/yr          Director   \n", "2                              NaN  Mid-Senior level   \n", "3                              NaN  Mid-Senior level   \n", "4            $70.00/hr - $75.00/hr  Mid-Senior level   \n", "\n", "                                        job_industry  \\\n", "0  Retail Luxury Goods and Jewelry, Wholesale Lux...   \n", "1                                             Retail   \n", "2                                 Financial Services   \n", "3                          Government Administration   \n", "4                                 Investment Banking   \n", "\n", "                                   job_functions employment_type  \\\n", "0      Analyst, Marketing, and Strategy/Planning       Full-time   \n", "1                            Other and Marketing       Full-time   \n", "2                                        Finance       Full-time   \n", "3  Research, Analyst, and Information Technology       Full-time   \n", "4                         Information Technology        Contract   \n", "\n", "                                          apply_link work_authorization  \\\n", "0  https://www.linkedin.com/jobs/view/crm-custome...           not sure   \n", "1  https://www.linkedin.com/jobs/view/director-of...           not sure   \n", "2  https://www.linkedin.com/jobs/view/business-an...           not sure   \n", "3  https://www.linkedin.com/jobs/view/externalApp...           not sure   \n", "4  https://www.linkedin.com/jobs/view/business-an...           not sure   \n", "\n", "  work_mode  \n", "0  not sure  \n", "1  not sure  \n", "2  not sure  \n", "3  not sure  \n", "4    Onsite  "]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["linkedin_processed.head()"]}, {"cell_type": "code", "execution_count": 34, "id": "0daf3c66", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>keyword</th>\n", "      <th>job_title</th>\n", "      <th>date</th>\n", "      <th>location</th>\n", "      <th>job_description</th>\n", "      <th>salary</th>\n", "      <th>job_seniority</th>\n", "      <th>job_industry</th>\n", "      <th>job_functions</th>\n", "      <th>employment_type</th>\n", "      <th>apply_link</th>\n", "      <th>work_authorization</th>\n", "      <th>work_mode</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>2336</td>\n", "      <td>2336</td>\n", "      <td>2336</td>\n", "      <td>2336</td>\n", "      <td>2336</td>\n", "      <td>1545</td>\n", "      <td>2336</td>\n", "      <td>2336</td>\n", "      <td>2327</td>\n", "      <td>2336</td>\n", "      <td>2336</td>\n", "      <td>2336</td>\n", "      <td>2336</td>\n", "    </tr>\n", "    <tr>\n", "      <th>unique</th>\n", "      <td>14</td>\n", "      <td>1911</td>\n", "      <td>2336</td>\n", "      <td>144</td>\n", "      <td>2168</td>\n", "      <td>897</td>\n", "      <td>7</td>\n", "      <td>358</td>\n", "      <td>213</td>\n", "      <td>7</td>\n", "      <td>2336</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>top</th>\n", "      <td>Marketing</td>\n", "      <td>Manager Trainee - Operations (Travel Program; ...</td>\n", "      <td>2025-07-09T20:56:24.302Z</td>\n", "      <td>New York, NY</td>\n", "      <td>Overview At Intuit we believe everyone should ...</td>\n", "      <td>$110,000.00/yr - $130,000.00/yr</td>\n", "      <td>Mid-Senior level</td>\n", "      <td>Software Development</td>\n", "      <td>Engineering and Information Technology</td>\n", "      <td>Full-time</td>\n", "      <td>https://www.linkedin.com/jobs/view/crm-custome...</td>\n", "      <td>not sure</td>\n", "      <td>not sure</td>\n", "    </tr>\n", "    <tr>\n", "      <th>freq</th>\n", "      <td>300</td>\n", "      <td>16</td>\n", "      <td>1</td>\n", "      <td>1345</td>\n", "      <td>10</td>\n", "      <td>19</td>\n", "      <td>1013</td>\n", "      <td>293</td>\n", "      <td>303</td>\n", "      <td>2059</td>\n", "      <td>1</td>\n", "      <td>2239</td>\n", "      <td>1456</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          keyword                                          job_title  \\\n", "count        2336                                               2336   \n", "unique         14                                               1911   \n", "top     Marketing  Manager Trainee - Operations (Travel Program; ...   \n", "freq          300                                                 16   \n", "\n", "                            date      location  \\\n", "count                       2336          2336   \n", "unique                      2336           144   \n", "top     2025-07-09T20:56:24.302Z  New York, NY   \n", "freq                           1          1345   \n", "\n", "                                          job_description  \\\n", "count                                                2336   \n", "unique                                               2168   \n", "top     Overview At Intuit we believe everyone should ...   \n", "freq                                                   10   \n", "\n", "                                 salary     job_seniority  \\\n", "count                              1545              2336   \n", "unique                              897                 7   \n", "top     $110,000.00/yr - $130,000.00/yr  Mid-Senior level   \n", "freq                                 19              1013   \n", "\n", "                job_industry                           job_functions  \\\n", "count                   2336                                    2327   \n", "unique                   358                                     213   \n", "top     Software Development  Engineering and Information Technology   \n", "freq                     293                                     303   \n", "\n", "       employment_type                                         apply_link  \\\n", "count             2336                                               2336   \n", "unique               7                                               2336   \n", "top          Full-time  https://www.linkedin.com/jobs/view/crm-custome...   \n", "freq              2059                                                  1   \n", "\n", "       work_authorization work_mode  \n", "count                2336      2336  \n", "unique                  3         4  \n", "top              not sure  not sure  \n", "freq                 2239      1456  "]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["linkedin_processed.describe()"]}, {"cell_type": "code", "execution_count": null, "id": "2863f799", "metadata": {}, "outputs": [], "source": ["import re\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "\n", "def process_indeed_jobs(df):\n", "    \"\"\"\n", "    Processes Indeed job DataFrame to extract and rename required columns,\n", "    fill missing apply_link with url, and add work_authorization and work_mode columns using regex.\n", "    \"\"\"\n", "    # 1. Extract keyword from discovery_input\n", "    if 'discovery_input' in df.columns:\n", "        df['keyword'] = df['discovery_input'].apply(\n", "            lambda x: json.loads(x).get('keyword_search') if pd.notna(x) and isinstance(x, str) else None\n", "        )\n", "    else:\n", "        df['keyword'] = None\n", "\n", "    # 2. Fill missing apply_link with url\n", "    if 'apply_link' in df.columns and 'url' in df.columns:\n", "        df['apply_link'] = df['apply_link'].replace('', np.nan)\n", "        df['apply_link'] = df['apply_link'].fillna(df['url'])\n", "    elif 'url' in df.columns:\n", "        df['apply_link'] = df['url']\n", "\n", "    # 3. Add work_authorization column using regex on description_text\n", "    def extract_work_auth(desc):\n", "        if pd.isna(desc):\n", "            return \"not sure\"\n", "        desc_lower = desc.lower()\n", "        if re.search(r'\\bno h1b\\b|\\bnot eligible for h1b\\b|\\bno work authorization\\b|\\bnot authorized\\b', desc_lower):\n", "            return \"no\"\n", "        if re.search(r'\\bh1b\\b|\\bwork authorization\\b|\\bvisa sponsorship\\b', desc_lower):\n", "            return \"yes\"\n", "        return \"not sure\"\n", "\n", "    df['work_authorization'] = df['description_text'].apply(extract_work_auth) if 'description_text' in df.columns else \"not sure\"\n", "\n", "    # 4. Add work_mode column using job_location (priority) and fallback to description_text\n", "    def extract_work_mode(row):\n", "        # Priority: job_location\n", "        loc = row.get('job_location', None)\n", "        desc = row.get('description_text', None)\n", "        # Check job_location first\n", "        if pd.notna(loc):\n", "            loc_lower = str(loc).lower()\n", "            if re.search(r'\\bremote\\b', loc_lower):\n", "                return \"Remote\"\n", "            elif re.search(r'\\bhybrid\\b', loc_lower):\n", "                return \"Hybrid\"\n", "            elif re.search(r'\\bonsite\\b|\\bon-site\\b', loc_lower):\n", "                return \"Onsite\"\n", "        # Fallback to description_text\n", "        if pd.notna(desc):\n", "            desc_lower = str(desc).lower()\n", "            if re.search(r'\\bremote\\b', desc_lower):\n", "                return \"Remote\"\n", "            elif re.search(r'\\bhybrid\\b', desc_lower):\n", "                return \"Hybrid\"\n", "            elif re.search(r'\\bonsite\\b|\\bon-site\\b', desc_lower):\n", "                return \"Onsite\"\n", "        return \"not sure\"\n", "\n", "    df['work_mode'] = df.apply(extract_work_mode, axis=1)\n", "\n", "    # 5. Select and rename columns\n", "    column_mapping = {\n", "        'keyword': 'keyword',\n", "        'job_title': 'job_title',\n", "        'company_name': 'company',\n", "        'date_posted_parsed': 'job_post_date',\n", "        'description_text': 'job_description',\n", "        'location': 'location',\n", "        'salary_formatted': 'salary',\n", "        'job_type': 'job_type',\n", "        'apply_link': 'apply_link',\n", "        'work_authorization': 'work_authorization',\n", "        'work_mode': 'work_mode'\n", "    }\n", "    selected_columns = [col for col in column_mapping.keys() if col in df.columns]\n", "    processed_df = df[selected_columns].rename(columns=column_mapping)\n", "\n", "    # Reorder columns\n", "    desired_order = [\n", "        'keyword', 'job_title', 'job_post_date', 'job_description', 'location',\n", "        'salary', 'job_type', 'apply_link', 'work_authorization', 'work_mode'\n", "    ]\n", "    processed_df = processed_df[[col for col in desired_order if col in processed_df.columns]]\n", "\n", "    return processed_df"]}, {"cell_type": "code", "execution_count": 36, "id": "2488f1a7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ bd_20250710_060943_0.csv: 104 rows loaded\n", "✅ bd_20250710_061452_0.csv: 105 rows loaded\n", "✅ bd_20250710_061044_0.csv: 101 rows loaded\n", "✅ bd_20250710_061326_0.csv: 105 rows loaded\n", "✅ bd_20250710_060637_0.csv: 40 rows loaded\n", "✅ bd_20250710_061448_0.csv: 105 rows loaded\n", "✅ bd_20250710_061608_0.csv: 105 rows loaded\n", "✅ bd_20250710_061337_0.csv: 87 rows loaded\n", "✅ bd_20250710_060812_0.csv: 61 rows loaded\n", "✅ bd_20250710_060913_0.csv: 105 rows loaded\n", "✅ bd_20250710_061214_0.csv: 105 rows loaded\n", "✅ bd_20250710_061054_0.csv: 104 rows loaded\n", "✅ bd_20250710_061209_0.csv: 73 rows loaded\n", "✅ bd_20250710_060740_0.csv: 105 rows loaded\n", "📊 Indeed: 1305 rows from 14 files\n"]}], "source": ["indeed_df = load_indeed_data()\n", "indeed_processed = process_indeed_jobs(indeed_df)"]}, {"cell_type": "code", "execution_count": 37, "id": "28ea1777", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>keyword</th>\n", "      <th>job_title</th>\n", "      <th>job_post_date</th>\n", "      <th>job_description</th>\n", "      <th>location</th>\n", "      <th>salary</th>\n", "      <th>job_type</th>\n", "      <th>apply_link</th>\n", "      <th>work_authorization</th>\n", "      <th>work_mode</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Research Analyst</td>\n", "      <td>Counterintelligence Analyst</td>\n", "      <td>2025-07-10T06:10:34.327Z</td>\n", "      <td>Job #: ********** Location: Reston, Virginia  ...</td>\n", "      <td>11951 Freedom Dr, Reston, VA 20190</td>\n", "      <td>$112,450 - $203,275 a year</td>\n", "      <td>Full-time</td>\n", "      <td>https://www.indeed.com/applystart?jk=2ff94de19...</td>\n", "      <td>not sure</td>\n", "      <td>Remote</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Research Analyst</td>\n", "      <td>Division Order Analyst</td>\n", "      <td>2025-07-10T06:10:39.140Z</td>\n", "      <td>Job Description:   Permian Resources (NYSE: PR...</td>\n", "      <td>Midland, TX</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>https://www.indeed.com/applystart?jk=18778afff...</td>\n", "      <td>not sure</td>\n", "      <td>not sure</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Research Analyst</td>\n", "      <td>Junior Technology Analyst</td>\n", "      <td>2025-07-10T06:10:39.572Z</td>\n", "      <td><PERSON><PERSON><PERSON> is seeking a Junior Technology Analyst...</td>\n", "      <td>4600 Silver Hill Road, Suitland, MD 20746</td>\n", "      <td>NaN</td>\n", "      <td>Full-time</td>\n", "      <td>https://www.indeed.com/applystart?jk=03a466d5f...</td>\n", "      <td>not sure</td>\n", "      <td>not sure</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Research Analyst</td>\n", "      <td>Emerging Threats Analyst</td>\n", "      <td>2025-07-10T06:10:40.079Z</td>\n", "      <td>STS Systems Support, LLC. (SSS) is seeking an ...</td>\n", "      <td>San Antonio, TX</td>\n", "      <td>NaN</td>\n", "      <td>Full-time</td>\n", "      <td>https://www.indeed.com/applystart?jk=c4da62a72...</td>\n", "      <td>not sure</td>\n", "      <td>not sure</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Research Analyst</td>\n", "      <td>Data Privacy Compliance Risk Analyst</td>\n", "      <td>2025-07-10T06:10:40.457Z</td>\n", "      <td>TJX Companies\\n  At TJX Companies, every day b...</td>\n", "      <td>300 Value Way, Marlborough, MA 01752</td>\n", "      <td>$69,200 - $86,500 a year</td>\n", "      <td>Full-time</td>\n", "      <td>https://www.indeed.com/applystart?jk=4f4a13086...</td>\n", "      <td>not sure</td>\n", "      <td>Hybrid</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            keyword                             job_title  \\\n", "0  Research Analyst           Counterintelligence Analyst   \n", "1  Research Analyst                Division Order Analyst   \n", "2  Research Analyst             Junior Technology Analyst   \n", "3  Research Analyst              Emerging Threats Analyst   \n", "4  Research Analyst  Data Privacy Compliance Risk Analyst   \n", "\n", "              job_post_date  \\\n", "0  2025-07-10T06:10:34.327Z   \n", "1  2025-07-10T06:10:39.140Z   \n", "2  2025-07-10T06:10:39.572Z   \n", "3  2025-07-10T06:10:40.079Z   \n", "4  2025-07-10T06:10:40.457Z   \n", "\n", "                                     job_description  \\\n", "0  Job #: ********** Location: Reston, Virginia  ...   \n", "1  Job Description:   Permian Resources (NYSE: PR...   \n", "2  Amentum is seeking a Junior Technology Analyst...   \n", "3  STS Systems Support, LLC. (SSS) is seeking an ...   \n", "4  TJX Companies\\n  At TJX Companies, every day b...   \n", "\n", "                                    location                      salary  \\\n", "0         11951 Freedom Dr, Reston, VA 20190  $112,450 - $203,275 a year   \n", "1                                Midland, TX                         NaN   \n", "2  4600 Silver Hill Road, Suitland, MD 20746                         NaN   \n", "3                            San Antonio, TX                         NaN   \n", "4       300 Value Way, Marlborough, MA 01752    $69,200 - $86,500 a year   \n", "\n", "    job_type                                         apply_link  \\\n", "0  Full-time  https://www.indeed.com/applystart?jk=2ff94de19...   \n", "1        NaN  https://www.indeed.com/applystart?jk=18778afff...   \n", "2  Full-time  https://www.indeed.com/applystart?jk=03a466d5f...   \n", "3  Full-time  https://www.indeed.com/applystart?jk=c4da62a72...   \n", "4  Full-time  https://www.indeed.com/applystart?jk=4f4a13086...   \n", "\n", "  work_authorization work_mode  \n", "0           not sure    Remote  \n", "1           not sure  not sure  \n", "2           not sure  not sure  \n", "3           not sure  not sure  \n", "4           not sure    Hybrid  "]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["indeed_processed.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ba28f64b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}