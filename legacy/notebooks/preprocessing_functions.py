import pandas as pd
import json
import numpy as np
import os
import warnings
from datetime import datetime

# Suppress pandas warnings
warnings.filterwarnings('ignore', category=FutureWarning)

def extract_indeed_keyword(discovery_input_str):
    """Extracts 'keyword_search' from a JSON string in the discovery_input column for Indeed."""
    if pd.isna(discovery_input_str) or not isinstance(discovery_input_str, str):
        return None
    try:
        data = json.loads(discovery_input_str)
        return data.get('keyword_search')
    except (json.JSONDecodeError, TypeError):
        return None

def extract_linkedin_keyword(discovery_input_str):
    """Extracts 'keyword' from a JSON string in the discovery_input column for LinkedIn."""
    if pd.isna(discovery_input_str) or not isinstance(discovery_input_str, str):
        return None
    try:
        data = json.loads(discovery_input_str)
        return data.get('keyword')
    except (json.JSONDecodeError, TypeError):
        return None

def process_indeed_data(df):
    """Process Indeed DataFrame to match expected output format."""
    if df.empty:
        return df
    
    processed_df = df.copy()
    
    # 1. Extract keyword using Indeed-specific extractor
    if 'discovery_input' in processed_df.columns:
        processed_df['keyword'] = processed_df['discovery_input'].apply(extract_indeed_keyword)

    # 2. If 'apply_link' is empty, use the 'url'
    if 'apply_link' in processed_df.columns and 'url' in processed_df.columns:
        processed_df['apply_link'] = processed_df['apply_link'].replace('', np.nan)
        processed_df['apply_link'] = processed_df['apply_link'].fillna(processed_df['url'])

    # 3. Select and rename the columns - INDEED SPECIFIC MAPPING
    final_columns = {
        'keyword': 'keyword',
        'company_name': 'company_name',
        'date_posted_parsed': 'date_posted',
        'job_title': 'job_title',
        'description_text': 'description',  # Indeed uses 'description_text'
        'benefits': 'benefits',
        'job_qualifications': 'qualification',  # Indeed uses 'job_qualifications'
        'job_type': 'job_type',
        'location': 'location',
        'salary_formatted': 'salary',  # Indeed uses 'salary_formatted'
        'region': 'region',
        'apply_link': 'apply_link'
    }

    # Select the original columns that are available in the dataframe
    columns_to_select = [col for col in final_columns.keys() if col in processed_df.columns]
    
    # Create the processed dataframe from the available columns
    processed_df = processed_df[columns_to_select].rename(columns=final_columns)
    
    # Reorder columns to match expected output
    desired_order = ['keyword', 'company_name', 'date_posted', 'job_title', 'description', 'benefits', 'qualification', 'job_type', 'location', 'salary', 'region', 'apply_link']
    available_ordered_columns = [col for col in desired_order if col in processed_df.columns]
    processed_df = processed_df[available_ordered_columns]

    print(f"📊 Indeed columns found: {columns_to_select}")
    print(f"📋 Indeed processed: {len(processed_df)} rows with {len(processed_df.columns)} columns")
    print(f"📋 Column order: {list(processed_df.columns)}")

    return processed_df

def process_linkedin_data(df):
    """Process LinkedIn DataFrame to match expected output format."""
    if df.empty:
        return df
    
    processed_df = df.copy()
    
    # 1. Extract keyword using LinkedIn-specific extractor
    if 'discovery_input' in processed_df.columns:
        processed_df['keyword'] = processed_df['discovery_input'].apply(extract_linkedin_keyword)

    # 2. If 'apply_link' is empty, use the 'url'
    if 'apply_link' in processed_df.columns and 'url' in processed_df.columns:
        processed_df['apply_link'] = processed_df['apply_link'].replace('', np.nan)
        processed_df['apply_link'] = processed_df['apply_link'].fillna(processed_df['url'])

    # 3. Select and rename the columns - LINKEDIN SPECIFIC MAPPING
    final_columns = {
        'keyword': 'keyword',
        'company_name': 'company_name',
        'date_posted': 'date_posted',  # LinkedIn uses 'date_posted'
        'job_title': 'job_title',
        'job_summary': 'description',  # LinkedIn uses 'job_summary'
        'benefits': 'benefits',
        'qualifications': 'qualification',  # LinkedIn uses 'qualifications'
        'job_employment_type': 'job_type',  # LinkedIn uses 'job_employment_type'
        'job_location': 'location',  # LinkedIn uses 'job_location'
        'job_base_pay_range': 'salary',  # LinkedIn uses 'job_base_pay_range'
        'region': 'region',
        'apply_link': 'apply_link'
    }

    # Select the original columns that are available in the dataframe
    columns_to_select = [col for col in final_columns.keys() if col in processed_df.columns]
    
    # Create the processed dataframe from the available columns
    processed_df = processed_df[columns_to_select].rename(columns=final_columns)
    
    # Reorder columns to match expected output
    desired_order = ['keyword', 'company_name', 'date_posted', 'job_title', 'description', 'benefits', 'qualification', 'job_type', 'location', 'salary', 'region', 'apply_link']
    available_ordered_columns = [col for col in desired_order if col in processed_df.columns]
    processed_df = processed_df[available_ordered_columns]

    print(f"📊 LinkedIn columns found: {columns_to_select}")
    print(f"📋 LinkedIn processed: {len(processed_df)} rows with {len(processed_df.columns)} columns")
    print(f"📋 Column order: {list(processed_df.columns)}")

    return processed_df

def load_data_from_folder(folder_path):
    """Load and concatenate all CSV files from a folder."""
    all_dfs = []
    
    if not os.path.exists(folder_path):
        print(f"⚠️  Folder {folder_path} does not exist")
        return pd.DataFrame()
    
    csv_files = [f for f in os.listdir(folder_path) if f.endswith('.csv')]
    
    if not csv_files:
        print(f"⚠️  No CSV files found in {folder_path}")
        return pd.DataFrame()
    
    print(f"📂 Found {len(csv_files)} CSV files in {folder_path}")
    
    for filename in csv_files:
        file_path = os.path.join(folder_path, filename)
        try:
            df = pd.read_csv(file_path)
            all_dfs.append(df)
            print(f"   ✅ {filename}: {len(df)} rows loaded")
        except Exception as e:
            print(f"   ❌ Error loading {filename}: {str(e)}")
    
    if all_dfs:
        combined_df = pd.concat(all_dfs, ignore_index=True)
        print(f"📊 Total: {len(combined_df)} rows from {len(all_dfs)} files")
        return combined_df
    else:
        return pd.DataFrame()

def process_and_combine_all_data():
    """Complete workflow to load, process, and combine all data."""
    print("🚀 STARTING COMPLETE DATA PROCESSING WORKFLOW")
    print("=" * 60)
    
    # Load raw data
    print("\n🔍 Loading Indeed data...")
    indeed_raw = load_data_from_folder('indeed_data')
    
    print("\n🔗 Loading LinkedIn data...")
    linkedin_raw = load_data_from_folder('linkedin_data')
    
    print("\n" + "-" * 40)
    
    # Process data
    print("\n⚙️  PROCESSING DATA...")
    
    indeed_processed = pd.DataFrame()
    linkedin_processed = pd.DataFrame()
    
    if not indeed_raw.empty:
        indeed_processed = process_indeed_data(indeed_raw)
        print(f"✅ Indeed processed: {len(indeed_processed)} rows")
    else:
        print("⚠️  No Indeed data to process")
    
    if not linkedin_raw.empty:
        linkedin_processed = process_linkedin_data(linkedin_raw)
        print(f"✅ LinkedIn processed: {len(linkedin_processed)} rows")
    else:
        print("⚠️  No LinkedIn data to process")
    
    print("\n" + "-" * 40)
    
    # Combine data
    print("\n🔗 COMBINING DATA...")
    
    dfs_to_combine = []
    if not indeed_processed.empty:
        dfs_to_combine.append(indeed_processed)
    if not linkedin_processed.empty:
        dfs_to_combine.append(linkedin_processed)
    
    if dfs_to_combine:
        combined_df = pd.concat(dfs_to_combine, ignore_index=True)
        
        print(f"✅ Combined dataset: {len(combined_df)} rows")
        print(f"📊 Combined shape: {combined_df.shape}")
        print(f"📊 Combined columns: {list(combined_df.columns)}")
        
        # Show platform distribution
        indeed_count = len(indeed_processed) if not indeed_processed.empty else 0
        linkedin_count = len(linkedin_processed) if not linkedin_processed.empty else 0
        print(f"\n📊 Platform distribution:")
        if indeed_count > 0:
            print(f"   • Indeed: {indeed_count} jobs")
        if linkedin_count > 0:
            print(f"   • LinkedIn: {linkedin_count} jobs")
        
        # Show keyword distribution
        if 'keyword' in combined_df.columns:
            keyword_counts = combined_df['keyword'].value_counts().head(5)
            print(f"\n📋 Top keywords:")
            for keyword, count in keyword_counts.items():
                print(f"   • {keyword}: {count} jobs")
        
        # Save files
        today = datetime.now().strftime("%Y%m%d")
        
        if not indeed_processed.empty:
            indeed_filename = f"indeed_processed_{today}.csv"
            indeed_processed.to_csv(indeed_filename, index=False)
            print(f"\n💾 Indeed data saved to: {indeed_filename}")
        
        if not linkedin_processed.empty:
            linkedin_filename = f"linkedin_processed_{today}.csv"
            linkedin_processed.to_csv(linkedin_filename, index=False)
            print(f"💾 LinkedIn data saved to: {linkedin_filename}")
        
        combined_filename = f"combined_jobs_{today}.csv"
        combined_df.to_csv(combined_filename, index=False)
        print(f"💾 Combined data saved to: {combined_filename}")
        
        print(f"\n🎉 Processing complete! Final dataset: {len(combined_df)} jobs")
        
        return combined_df, indeed_processed, linkedin_processed
    else:
        print("⚠️  No data to combine")
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame()

def analyze_data(df):
    """Analyze the combined dataframe."""
    if df.empty:
        print("⚠️  No data to analyze")
        return
    
    print("🔍 DATA ANALYSIS")
    print("=" * 30)
    
    # Missing values analysis
    print("\n📊 Missing values analysis:")
    missing_values = df.isnull().sum()
    missing_percent = (missing_values / len(df)) * 100
    
    missing_df = pd.DataFrame({
        'Missing Count': missing_values,
        'Missing Percentage': missing_percent
    })
    
    print(missing_df[missing_df['Missing Count'] > 0])
    
    # Data types
    print("\n📊 Data types:")
    print(df.dtypes.to_frame('Data Type'))
    
    # Sample records
    print("\n🔍 Sample records:")
    for i in range(min(2, len(df))):
        print(f"\n--- Record {i+1} ---")
        record = df.iloc[i]
        for col in df.columns:
            value = record[col]
            if pd.isna(value):
                print(f"{col}: [MISSING]")
            elif isinstance(value, str) and len(value) > 100:
                print(f"{col}: {value[:100]}...")
            else:
                print(f"{col}: {value}")

# Example usage
if __name__ == "__main__":
    print("📚 Preprocessing functions loaded!")
    print("\nAvailable functions:")
    print("- extract_indeed_keyword()")
    print("- extract_linkedin_keyword()")
    print("- process_indeed_data()")
    print("- process_linkedin_data()")
    print("- load_data_from_folder()")
    print("- process_and_combine_all_data()")
    print("- analyze_data()")
    
    print("\n💡 Quick start:")
    print("combined_df, indeed_df, linkedin_df = process_and_combine_all_data()")
    print("analyze_data(combined_df)")