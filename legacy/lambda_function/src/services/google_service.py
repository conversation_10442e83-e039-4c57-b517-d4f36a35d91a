"""
Google API services for the Job-Sheet-Processor.
"""
import os
import json
from googleapiclient.discovery import build
from google.oauth2 import service_account
from src.utils.logger import get_logger
from src.utils.config import GOOGLE_API_SCOPES, SERVICE_ACCOUNT_FILE

logger = get_logger()

def initialize_google_services():
    """Initialize and return Google Sheets and Drive API services with credentials from the service account JSON file."""
    try:
        # Get credentials from the service account file
        credentials = service_account.Credentials.from_service_account_file(
            SERVICE_ACCOUNT_FILE, scopes=GOOGLE_API_SCOPES)
            
        # Build the services
        sheets_service = build('sheets', 'v4', credentials=credentials)
        drive_service = build('drive', 'v3', credentials=credentials)
        
        logger.info("Successfully initialized Google API services using service account file")
        return sheets_service, drive_service
    except Exception as e:
        logger.error(f"Error initializing Google API services: {str(e)}")
        raise
