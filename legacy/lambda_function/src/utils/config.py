"""
Configuration settings for the Job-Sheet-Processor.
"""
import os

# Constants
VIP_SPREADSHEET_ID = '1JDRjnID7KWLRE7jZohJTYQw8lALGCZ1Nv53yL938I0Q'
FOLDER_ID = '1dKVjmvDgelKZxi9BkYIbxU3p3Mg_dXFV'
VIP_SHEET_NAMES = ['VIP Jobs 1', 'VIP Jobs 2', 'VIP Jobs 3']

# Define scopes
GOOGLE_API_SCOPES = [
    'https://www.googleapis.com/auth/spreadsheets', 
    'https://www.googleapis.com/auth/drive'
]

# Service account settings
SERVICE_ACCOUNT_FILE = os.environ.get('GOOGLE_SERVICE_ACCOUNT_FILE', os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'service-account-key.json'))

# Job processing constants
EMPLOYMENT_TYPES_TO_KEEP = ['Full-time', 'Internship']
SOURCES_TO_REMOVE = ['Learn4Good', 'JobLead']

# Columns to remove during processing
COLUMNS_TO_REMOVE = [
    'Salary', 'Degree_requirement', 'Job_area1', 'Job_area2', 'Job_area3', 
    'Job_detail_URL', 'Job_highlights', 'Benifits', 'Responsibilities', 
    'Job_description', 'Website', 'Emails', 'Company_rating1', 
    'Company_rating2', 'Company_rating3'
]

# Keywords to exclude from job titles
KEYWORDS_TO_EXCLUDE = [
    'Senior', 'Lead', 'Chief', 'Director', 'Executive', 'Principal', 'Architect',
    'Supervisor', 'VP', 'Vice President', 'Contract', 'Contractor', 'Part-time',
    'mid-level', 'Clearance', 'Specialist', 'Consultant', 'Worker', 'Technician',
    'Advisor', 'Administrative', 'Clerk', 'teacher', 'tutor', 'educator',
    'instructor', 'professor', 'faculty', 'representative', 'Shift', 'Laborer',
    'Rep', 'operator', 'handler', 'inspector', 'Head', 'Associate', 'Apprentice',
    'lister', 'officer', 'writer', 'advisor', 'Banker', 'training', 'instruct',
    'staff', 'Facilitator', 'Appraiser', 'Auditor', 'Nurse', 'Representative',
    'Radiologist', 'Dealer', 'Sales', 'Counselor', 'Broker', 'Archaeologist',
    'agent', 'Agency', 'Floral', 'Interior', 'nails', 'part-time', 'therapist',
    'contract', 'certified', 'inn', 'restaurant', 'Sr.', 'Sr', 'coordinator', 'Buyer'
]

# Work mode detection keywords
REMOTE_KEYWORDS = [
    'remote', 'work from home', 'wfh', 'virtual', 'telecommute', 
    'anywhere', 'location independent', 'distributed team'
]

HYBRID_KEYWORDS = [
    'hybrid', 'flex', 'flexible location', 'part remote', 
    'mix of remote and onsite', 'partially remote'
]

ONSITE_KEYWORDS = [
    'on-site', 'onsite', 'in office', 'in-office', 'on site', 
    'physical location', 'workplace', 'office environment'
]

# PM role keywords
PM_KEYWORDS = ['product manager', 'project manager', 'program manager']

# Authorization keywords
AUTH_KEYWORDS = ['citizen', 'green card', 'h1b', 'visa', 'authorization']
