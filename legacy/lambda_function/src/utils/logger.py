"""
Logging utility for the Job-Sheet-Processor.
"""
import logging

def get_logger():
    """Set up and return a logger configured for the application."""
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    
    # Only add handler if it doesn't already exist
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    return logger
