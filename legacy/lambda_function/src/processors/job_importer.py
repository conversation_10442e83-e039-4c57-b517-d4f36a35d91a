"""
Module for importing jobs from VIP spreadsheets.
"""
from googleapiclient.errors import HttpError
from src.utils.logger import get_logger
from src.utils.config import VIP_SPREADSHEET_ID, VIP_SHEET_NAMES

logger = get_logger()

def import_and_process_vip_jobs(sheets_service, target_spreadsheet_id):
    """Import data from VIP jobs spreadsheet"""
    try:
        # Get sheets from VIP spreadsheet
        vip_jobs_data = []
        
        # Process each VIP sheet
        for sheet_name in VIP_SHEET_NAMES:
            try:
                # Get data from the VIP sheet
                range_name = f"'{sheet_name}'!A:Z"  # Adjust range as needed
                result = sheets_service.spreadsheets().values().get(
                    spreadsheetId=VIP_SPREADSHEET_ID,
                    range=range_name
                ).execute()
                
                sheet_data = result.get('values', [])
                
                if not sheet_data:
                    logger.info(f"No data found in sheet: {sheet_name}")
                    continue
                
                # Remove headers from subsequent sheets after the first
                if vip_jobs_data:
                    sheet_data.pop(0)
                
                # Append data to vip_jobs_data
                vip_jobs_data.extend(sheet_data)
                
            except HttpError as error:
                logger.error(f"Error accessing sheet {sheet_name}: {error}")
                continue
        
        # If no data found, exit
        if not vip_jobs_data:
            logger.warning("No VIP jobs data found")
            return False
        
        # Write headers first
        headers = vip_jobs_data[0]
        header_range = "'All Jobs'!A1"
        
        sheets_service.spreadsheets().values().update(
            spreadsheetId=target_spreadsheet_id,
            range=header_range,
            valueInputOption='USER_ENTERED',
            body={'values': [headers]}
        ).execute()
        
        # Write data in chunks to avoid API limits and timeouts
        data_rows = vip_jobs_data[1:] if len(vip_jobs_data) > 1 else []
        chunk_size = 500  # Adjust this value based on performance
        
        logger.info(f"Writing {len(data_rows)} rows in chunks of {chunk_size}")
        
        for i in range(0, len(data_rows), chunk_size):
            chunk = data_rows[i:i + chunk_size]
            
            # Calculate range for this chunk
            chunk_range = f"'All Jobs'!A{i + 2}:{chr(64 + len(headers))}{i + len(chunk) + 1}"
            
            logger.info(f"Writing chunk {i//chunk_size + 1} of {(len(data_rows) + chunk_size - 1)//chunk_size}, rows {i + 2}-{i + len(chunk) + 1}")
            
            sheets_service.spreadsheets().values().update(
                spreadsheetId=target_spreadsheet_id,
                range=chunk_range,
                valueInputOption='USER_ENTERED',
                body={'values': chunk}
            ).execute()
        
        logger.info(f"Successfully imported {len(vip_jobs_data)} rows from VIP jobs spreadsheet")
        return True
        
    except Exception as e:
        logger.error(f"Error importing VIP jobs: {str(e)}")
        raise
