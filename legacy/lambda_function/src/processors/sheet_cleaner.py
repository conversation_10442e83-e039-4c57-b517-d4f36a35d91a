"""
Module for clearing and resetting spreadsheets.
"""
from src.utils.logger import get_logger

logger = get_logger()

def clear_sheet(sheets_service, spreadsheet_id):
    """Clear all sheets except 'All Jobs'"""
    try:
        # Get all sheets in the spreadsheet
        sheet_metadata = sheets_service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
        sheets = sheet_metadata.get('sheets', [])
        
        # Track if "All Jobs" sheet exists
        all_jobs_sheet_id = None
        requests = []
        
        # Identify sheets to delete
        for sheet in sheets:
            props = sheet.get('properties', {})
            sheet_id = props.get('sheetId')
            sheet_title = props.get('title')
            
            if sheet_title == 'All Jobs':
                all_jobs_sheet_id = sheet_id
                # Clear the All Jobs sheet
                requests.append({
                    'updateCells': {
                        'range': {
                            'sheetId': sheet_id
                        },
                        'fields': 'userEnteredValue'
                    }
                })
            else:
                # Delete other sheets
                requests.append({
                    'deleteSheet': {
                        'sheetId': sheet_id
                    }
                })
        
        # If "All Jobs" sheet doesn't exist, create one
        if all_jobs_sheet_id is None:
            requests.append({
                'addSheet': {
                    'properties': {
                        'title': 'All Jobs'
                    }
                }
            })
        
        # Execute the requests if there are any
        if requests:
            sheets_service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_id,
                body={'requests': requests}
            ).execute()
            
        logger.info('Cleared sheets successfully')
        return True
    
    except Exception as e:
        logger.error(f"Error clearing sheets: {str(e)}")
        raise
