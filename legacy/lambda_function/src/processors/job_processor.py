"""
Module for processing job data.
"""
import re
from src.utils.logger import get_logger
from src.utils.config import (
    EMPLOYMENT_TYPES_TO_KEEP, COLUMNS_TO_REMOVE, KEYWORDS_TO_EXCLUDE,
    SOURCES_TO_REMOVE, PM_KEYWORDS, AUTH_KEYWORDS,
    REMOTE_KEYWORDS, HYBRID_KEYWORDS, ONSITE_KEYWORDS
)

logger = get_logger()

def extract_years_of_experience(qualification):
    """Extract years of experience from qualification text."""
    if not qualification:
        return None
        
    # Regex to match numbers associated with "years" in various phrasings
    regex = r"(?:at least|minimum|over|more than)?\s*(\d+)\s*(?:[\+\-]?\s*|\s*to\s*(\d+)\s*)years?(?!\s*old)"
    matches = re.findall(regex, qualification, re.IGNORECASE)
    
    if not matches:
        return None
        
    # Extract all numbers
    numbers = []
    for match in matches:
        if match[0]:  # First number
            numbers.append(int(match[0]))
        if match[1]:  # Second number (in range)
            numbers.append(int(match[1]))
    
    # Filter and get the highest
    filtered_numbers = [n for n in numbers if 0 <= n <= 20 and n != 18]
    
    if not filtered_numbers:
        return None
        
    return str(max(filtered_numbers))

def detect_work_mode(row, column_indices):
    """Detect work mode based on job details."""
    # Default to not mentioned
    if any(idx == -1 for idx in [column_indices['Keyword'], column_indices['Qualifications'], 
                                column_indices['Job_title'], column_indices['Location'],
                                column_indices['Apply_links']]):
        return 'Not Mentioned'
    
    keyword = str(row[column_indices['Keyword']]).lower() if column_indices['Keyword'] < len(row) else ''
    qualifications = str(row[column_indices['Qualifications']]).lower() if column_indices['Qualifications'] < len(row) else ''
    job_title = str(row[column_indices['Job_title']]).lower() if column_indices['Job_title'] < len(row) else ''
    location = str(row[column_indices['Location']]).lower() if column_indices['Location'] < len(row) else ''
    apply_links = str(row[column_indices['Apply_links']]).lower() if column_indices['Apply_links'] < len(row) else ''
    
    # Check for remote
    if any(kw in qualifications or kw in job_title for kw in REMOTE_KEYWORDS):
        return 'Remote'
    
    # Check for hybrid
    if any(kw in qualifications or kw in job_title for kw in HYBRID_KEYWORDS):
        return 'Hybrid'
    
    # Check for onsite
    if any(kw in qualifications or kw in job_title for kw in ONSITE_KEYWORDS):
        return 'On-site'
    
    # Default
    return 'Not Mentioned'

def process_job_data(sheets_service, spreadsheet_id):
    """Process job data in the spreadsheet"""
    try:
        # Get the All Jobs sheet data
        range_name = "'All Jobs'!A:Z"
        result = sheets_service.spreadsheets().values().get(
            spreadsheetId=spreadsheet_id,
            range=range_name
        ).execute()
        
        data = result.get('values', [])
        if not data:
            logger.warning("No data found in the All Jobs sheet")
            return False
        
        # Extract headers and data
        headers = data[0]
        data = data[1:]
        
        # Remove certain columns
        # Find indices of columns to remove
        indices_of_removal = sorted([
            headers.index(col) for col in COLUMNS_TO_REMOVE 
            if col in headers
        ], reverse=True)
        
        # Remove specified columns from headers
        for index in indices_of_removal:
            if 0 <= index < len(headers):
                headers.pop(index)
        
        # Remove specified columns from data rows
        for row in data:
            # Ensure row has enough elements
            while len(row) < len(headers) + len(indices_of_removal):
                row.append('')
                
            for index in indices_of_removal:
                if 0 <= index < len(row):
                    row.pop(index)
        
        # Identify column indices
        column_indices = {
            'Keyword': headers.index('Keyword') if 'Keyword' in headers else -1,
            'Job_title': headers.index('Job_title') if 'Job_title' in headers else -1,
            'Company': headers.index('Company') if 'Company' in headers else -1,
            'Location': headers.index('Location') if 'Location' in headers else -1,
            'Source': headers.index('Source') if 'Source' in headers else -1,
            'Job_post_time': headers.index('Job_post_time') if 'Job_post_time' in headers else -1,
            'Employment_type': headers.index('Employment_type') if 'Employment_type' in headers else -1,
            'Qualifications': headers.index('Qualifications') if 'Qualifications' in headers else -1,
            'Apply_links': headers.index('Apply_links') if 'Apply_links' in headers else -1
        }
        
        # Check if required columns exist
        for col_name, index in column_indices.items():
            if index == -1:
                logger.warning(f"Required column not found: {col_name}")
        
        logger.info(f"Initial number of job listings: {len(data)}")
        
        # Remove duplicates based on Job_title, Company, Location
        unique_jobs_map = {}
        filtered_data = []
        
        for row in data:
            # Ensure the row has enough elements
            while len(row) < len(headers):
                row.append('')
                
            # Create unique key
            if all(index != -1 for index in [column_indices['Job_title'], column_indices['Company'], column_indices['Location']]):
                key = f"{row[column_indices['Job_title']]}|{row[column_indices['Company']]}|{row[column_indices['Location']]}"
                if key not in unique_jobs_map:
                    unique_jobs_map[key] = True
                    filtered_data.append(row)
            else:
                # If missing columns, keep the row
                filtered_data.append(row)
        
        data = filtered_data
        logger.info(f"After removing duplicates: {len(data)} rows")
        
        # Process job post times - Only remove jobs more than 1 day old, keep jobs with empty dates
        filtered_data = []
        
        for row in data:
            if column_indices['Job_post_time'] == -1 or column_indices['Job_post_time'] >= len(row):
                filtered_data.append(row)
                continue
                
            post_time = row[column_indices['Job_post_time']]
            
            # If post time is empty, keep the job
            if not post_time:
                filtered_data.append(row)
                continue
                
            post_time = str(post_time).lower()
            if 'day' in post_time:
                try:
                    days = int(post_time.split(' ')[0])
                    if days <= 1:
                        filtered_data.append(row)
                except (ValueError, IndexError):
                    filtered_data.append(row)  # Keep if can't parse
            elif 'hour' in post_time:
                filtered_data.append(row)  # Keep jobs posted within hours
        
        data = filtered_data
        logger.info(f"After filtering old jobs: {len(data)} rows")
        
        # Filter employment types
        filtered_data = []
        
        for row in data:
            if column_indices['Employment_type'] == -1 or column_indices['Employment_type'] >= len(row):
                filtered_data.append(row)
                continue
                
            employment_type = row[column_indices['Employment_type']]
            
            if employment_type in EMPLOYMENT_TYPES_TO_KEEP:
                filtered_data.append(row)
        
        data = filtered_data
        logger.info(f"After filtering employment types: {len(data)} rows")
        
        # Filter job titles with excluded keywords
        filtered_data = []
        
        for row in data:
            if column_indices['Job_title'] == -1 or column_indices['Job_title'] >= len(row):
                filtered_data.append(row)
                continue
                
            job_title = str(row[column_indices['Job_title']]).lower()
            
            if not any(keyword.lower() in job_title for keyword in KEYWORDS_TO_EXCLUDE):
                filtered_data.append(row)
        
        data = filtered_data
        logger.info(f"After filtering excluded keywords: {len(data)} rows")
        
        # Process PM roles
        for row in data:
            # Initialize new properties
            is_pm_role = False
            is_manager_role = False
            
            if column_indices['Keyword'] != -1 and column_indices['Keyword'] < len(row):
                keyword = str(row[column_indices['Keyword']]).lower()
                is_pm_role = any(pm in keyword for pm in PM_KEYWORDS)
            
            if column_indices['Job_title'] != -1 and column_indices['Job_title'] < len(row):
                is_manager_role = 'manager' in str(row[column_indices['Job_title']]).lower()
            
            # Attach these as properties to the row (as a list extension)
            row.append(is_pm_role)  # This can be removed later
            row.append(is_manager_role)  # This can be removed later
        
        # Remove non-PM manager roles
        filtered_data = []
        
        for row in data:
            is_pm_role = row[-2]  # Second last element we just added
            is_manager_role = row[-1]  # Last element we just added
            
            if is_pm_role or not is_manager_role:
                # Remove the temporary properties
                row = row[:-2]
                filtered_data.append(row)
        
        data = filtered_data
        logger.info(f"After filtering non-PM manager roles: {len(data)} rows")
        
        # Process authorization requirements
        # Add new column header
        headers.append('Authorization Status')
        
        for row in data:
            requires_authorization = False
            
            if column_indices['Qualifications'] != -1 and column_indices['Qualifications'] < len(row):
                qualifications = str(row[column_indices['Qualifications']]).lower()
                requires_authorization = any(keyword in qualifications for keyword in AUTH_KEYWORDS)
            
            authorization_status = 'Requires authorization' if requires_authorization else 'Not mentioned'
            row.append(authorization_status)
        
        # Extract years of experience
        # Add years of experience to headers
        headers.append('Years of Experience')
        
        # Process each row for years of experience
        for row in data:
            years_of_experience = None
            
            if column_indices['Qualifications'] != -1 and column_indices['Qualifications'] < len(row):
                years_of_experience = extract_years_of_experience(row[column_indices['Qualifications']])
            
            row.append(years_of_experience or '')
        
        # Filter based on years of experience
        filtered_data = []
        
        for row in data:
            yoe = row[-1]  # Years of experience is the last column
            
            if not yoe:
                filtered_data.append(row)  # Keep if no YOE specified
                continue
                
            try:
                numbers = [int(n.strip()) for n in yoe.split(',') if n.strip()]
                if any(n <= 2 for n in numbers):
                    filtered_data.append(row)
            except ValueError:
                filtered_data.append(row)  # Keep if can't parse
        
        data = filtered_data
        logger.info(f"After filtering by years of experience: {len(data)} rows")
        
        # Remove specific sources
        filtered_data = []
        
        for row in data:
            if column_indices['Source'] == -1 or column_indices['Source'] >= len(row):
                filtered_data.append(row)
                continue
                
            source = str(row[column_indices['Source']]).lower()
            
            if not any(s.lower() in source for s in SOURCES_TO_REMOVE):
                filtered_data.append(row)
        
        data = filtered_data
        logger.info(f"After removing excluded sources: {len(data)} rows")
        
        # Add work mode - This is now the second-to-last step
        headers.append('Work Mode')
        
        # Add work mode to each row
        for row in data:
            work_mode = detect_work_mode(row, column_indices)
            row.append(work_mode)
            
        # Process Apply_links - This is now the very last step
        if column_indices['Apply_links'] != -1:
            logger.info("Processing Apply_links as the final step")
            # Process Apply_links here if any special handling is needed
            # This ensures Apply_links is processed as the final step before writing to sheet
        
        # Clear and write processed data back to the sheet
        clear_range = "'All Jobs'!A:Z"
        sheets_service.spreadsheets().values().clear(
            spreadsheetId=spreadsheet_id,
            range=clear_range
        ).execute()
        
        # Write headers
        header_range = "'All Jobs'!A1"
        sheets_service.spreadsheets().values().update(
            spreadsheetId=spreadsheet_id,
            range=header_range,
            valueInputOption="RAW",
            body={"values": [headers]}
        ).execute()
        
        # Write data if any
        if data:
            data_range = f"'All Jobs'!A2:{chr(64 + len(headers))}{len(data) + 1}"
            sheets_service.spreadsheets().values().update(
                spreadsheetId=spreadsheet_id,
                range=data_range,
                valueInputOption="RAW",
                body={"values": data}
            ).execute()
        
        logger.info(f"Processing complete. Final number of job listings: {len(data)}")
        return True
        
    except Exception as e:
        logger.error(f"Error processing job data: {str(e)}")
        raise
