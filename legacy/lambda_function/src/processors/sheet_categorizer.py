"""
Module for creating category-based job sheets.
"""
import re
import time
from src.utils.logger import get_logger

logger = get_logger()

def create_job_sheets(sheets_service, spreadsheet_id):
    """Create category-based job sheets"""
    try:
        # Get the All Jobs sheet data
        range_name = "'All Jobs'!A:Z"
        result = sheets_service.spreadsheets().values().get(
            spreadsheetId=spreadsheet_id,
            range=range_name
        ).execute()
        
        
        data = result.get('values', [])
        if not data:
            logger.warning("No data found in the All Jobs sheet")
            return False
        
        # Extract headers and data
        headers = data[0]
        data = data[1:] if len(data) > 1 else []
        
        # Find column indices
        keyword_index = headers.index('Keyword') if 'Keyword' in headers else -1
        job_title_index = headers.index('Job_title') if 'Job_title' in headers else -1
        location_index = headers.index('Location') if 'Location' in headers else -1
        
        if any(idx == -1 for idx in [keyword_index, job_title_index, location_index]):
            logger.error("Required columns not found")
            return False
        
        # Create a map of categories and their jobs
        category_map = {}
        
        for row in data:
            # Ensure row has required index
            if keyword_index < len(row):
                keyword = str(row[keyword_index]).strip()
                categories = keyword.split(' in ')
                
                if categories:
                    primary_category = categories[0].strip()
                    
                    if primary_category not in category_map:
                        category_map[primary_category] = []
                    
                    category_map[primary_category].append(row)
        
        # Get existing sheets and delete all except All Jobs
        sheet_metadata = sheets_service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
        # Add delay after API call
        
        sheets = sheet_metadata.get('sheets', [])
        
        delete_requests = []
        for sheet in sheets:
            props = sheet.get('properties', {})
            sheet_id = props.get('sheetId')
            sheet_title = props.get('title')
            
            if sheet_title != 'All Jobs':
                delete_requests.append({
                    'deleteSheet': {
                        'sheetId': sheet_id
                    }
                })
        
        # Execute delete requests if any
        if delete_requests:
            sheets_service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_id,
                body={'requests': delete_requests}
            ).execute()
            # Add delay after API call
        
        # Process each category
        for category, category_jobs in category_map.items():
            if not category_jobs:
                continue
                
            # Create sheet name (sanitize)
            sheet_name = re.sub(r'[^\w\s]', '', category)[:31]
            
            # Add new sheet
            add_sheet_request = {
                'addSheet': {
                    'properties': {
                        'title': sheet_name
                    }
                }
            }
            
            add_response = sheets_service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_id,
                body={'requests': [add_sheet_request]}
            ).execute()
            # Add delay after API call
            
            # Get new sheet ID
            new_sheet_id = add_response['replies'][0]['addSheet']['properties']['sheetId']
            
            # Write headers
            header_range = f"'{sheet_name}'!A1"
            sheets_service.spreadsheets().values().update(
                spreadsheetId=spreadsheet_id,
                range=header_range,
                valueInputOption="RAW",
                body={"values": [headers]}
            ).execute()
            # Add delay after API call
            
            # Write data in chunks to avoid API limits
            chunk_size = 100
            for i in range(0, len(category_jobs), chunk_size):
                chunk = category_jobs[i:i + chunk_size]
                
                data_range = f"'{sheet_name}'!A{i + 2}:{chr(64 + len(headers))}{i + len(chunk) + 1}"
                sheets_service.spreadsheets().values().update(
                    spreadsheetId=spreadsheet_id,
                    range=data_range,
                    valueInputOption="RAW",
                    body={"values": chunk}
                ).execute()
                # Add delay after API call
            
            # Copy formatting from All Jobs sheet to maintain consistent style
            # Get All Jobs sheet ID
            all_jobs_sheet_id = None
            for sheet in sheets:
                if sheet.get('properties', {}).get('title') == 'All Jobs':
                    all_jobs_sheet_id = sheet.get('properties', {}).get('sheetId')
                    break
            
            if all_jobs_sheet_id is not None:
                # Copy formatting from All Jobs to the new sheet
                copy_formatting_request = {
                    'copyPaste': {
                        'source': {
                            'sheetId': all_jobs_sheet_id,
                            'startRowIndex': 0,
                            'endRowIndex': 1,  # Just the header row for formatting
                            'startColumnIndex': 0,
                            'endColumnIndex': len(headers)
                        },
                        'destination': {
                            'sheetId': new_sheet_id,
                            'startRowIndex': 0,
                            'endRowIndex': 1,
                            'startColumnIndex': 0,
                            'endColumnIndex': len(headers)
                        },
                        'pasteType': 'PASTE_FORMAT'
                    }
                }
                
                sheets_service.spreadsheets().batchUpdate(
                    spreadsheetId=spreadsheet_id,
                    body={'requests': [copy_formatting_request]}
                ).execute()
                # Add delay after API call
                
                # Apply auto-resize to columns for better readability
                auto_resize_request = {
                    'autoResizeDimensions': {
                        'dimensions': {
                            'sheetId': new_sheet_id,
                            'dimension': 'COLUMNS',
                            'startIndex': 0,
                            'endIndex': len(headers)
                        }
                    }
                }
                
                sheets_service.spreadsheets().batchUpdate(
                    spreadsheetId=spreadsheet_id,
                    body={'requests': [auto_resize_request]}
                ).execute()
                # Add delay after API call
                time.sleep(2)
            
            logger.info(f"Created sheet for {category} with {len(category_jobs)} jobs")
        
        return True
        
    except Exception as e:
        logger.error(f"Error creating job sheets: {str(e)}")
        raise