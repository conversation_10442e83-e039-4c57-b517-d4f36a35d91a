"""
Module for exporting spreadsheet data.
"""
from datetime import datetime
from src.utils.logger import get_logger
from src.utils.config import FOLDER_ID

logger = get_logger()

def export_sheet_to_new_spreadsheet(sheets_service, drive_service, source_spreadsheet_id):
    """Export sheet to a new spreadsheet"""
    try:
        # Generate the new sheet name with today's date
        today = datetime.now()
        formatted_date = today.strftime("%m%d")
        new_sheet_name = f"[Daily Jobs] {formatted_date} Daily Jobs for NG.xlsx"
        
        # Create a copy in Drive
        copy_file = {
            'name': new_sheet_name,
            'parents': [FOLDER_ID]
        }
        
        # Use the Drive API to copy the file
        new_file = drive_service.files().copy(
            fileId=source_spreadsheet_id,
            body=copy_file
        ).execute()
        
        # Set the file to be viewable by anyone with the link
        drive_service.permissions().create(
            fileId=new_file['id'],
            body={
                'type': 'anyone',
                'role': 'reader'
            }
        ).execute()
        
        logger.info(f"New spreadsheet created with ID: {new_file['id']}")
        return new_file['id']
        
    except Exception as e:
        logger.error(f"Error exporting sheet: {str(e)}")
        raise
