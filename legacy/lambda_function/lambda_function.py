"""
AWS Lambda Handler for Job Sheet Processor.

This is the main entry point for the Job Sheet Processor Lambda function.
It processes job data from VIP spreadsheets, filters and categorizes the jobs,
and creates category-based job sheets.
"""
import json
import logging
from src.utils.logger import get_logger
from src.services.google_service import initialize_google_services
from src.processors.sheet_cleaner import clear_sheet
from src.processors.job_importer import import_and_process_vip_jobs
from src.processors.job_processor import process_job_data
from src.processors.sheet_categorizer import create_job_sheets
from src.processors.sheet_exporter import export_sheet_to_new_spreadsheet

# Set up logger
logger = get_logger()

def lambda_handler(event, context):
    """Main Lambda handler function"""
    try:
        # Log the incoming event (for debugging)
        logger.info(f"Received event: {json.dumps(event)}")
        
        # Extract action and parameters
        action = event.get('action', 'processAll')
        target_spreadsheet_id = event.get('targetSpreadsheetId')
        
        if not target_spreadsheet_id:
            return {
                'statusCode': 400,
                'body': "Missing targetSpreadsheetId parameter"
            }
        
        # Initialize the services
        sheets_service, drive_service = initialize_google_services()
        
        # Execute the requested action
        if action == 'importAndProcessVIPJobs':
            result = import_and_process_vip_jobs(sheets_service, target_spreadsheet_id)
            return_message = "Successfully imported VIP jobs" if result else "No VIP jobs imported"
        elif action == 'clearSheet':
            result = clear_sheet(sheets_service, target_spreadsheet_id)
            return_message = "Successfully cleared sheet" if result else "Failed to clear sheet"
        elif action == 'exportSheetToNewSpreadsheet':
            result = export_sheet_to_new_spreadsheet(sheets_service, drive_service, target_spreadsheet_id)
            return_message = f"Successfully exported to new spreadsheet: {result}" if result else "Failed to export sheet"
        elif action == 'processJobData':
            result = process_job_data(sheets_service, target_spreadsheet_id)
            return_message = "Successfully processed job data" if result else "Failed to process job data"
        elif action == 'createJobSheets':
            result = create_job_sheets(sheets_service, target_spreadsheet_id)
            return_message = "Successfully created job sheets" if result else "Failed to create job sheets"
        elif action == 'processAll':
            # Run the full workflow
            clear_sheet(sheets_service, target_spreadsheet_id)
            import_and_process_vip_jobs(sheets_service, target_spreadsheet_id)
            process_job_data(sheets_service, target_spreadsheet_id)
            create_job_sheets(sheets_service, target_spreadsheet_id)
            # Export as the final step
            result = export_sheet_to_new_spreadsheet(sheets_service, drive_service, target_spreadsheet_id)
            return_message = "Successfully processed all steps" if result else "Completed with some failures"
        else:
            return {
                'statusCode': 400,
                'body': f"Invalid action: {action}"
            }
            
        return {
            'statusCode': 200,
            'body': return_message
        }
    
    except Exception as e:
        logger.error(f"Error in lambda_handler: {str(e)}")
        return {
            'statusCode': 500,
            'body': f"Error: {str(e)}"
        }
