{"metadata": {"scraping_timestamp": "2025-09-18T16:51:37.598032", "config_file_used": "config/scraping_inputs.json", "total_api_calls": 20, "platforms_scraped": ["indeed", "linkedin"]}, "results": {"indeed": {"software_engineer": {"error": "Invalid input provided", "code": "validation_error", "type": "validation", "line": "{\"keyword_search\":\"software engineer\",\"location\":\"United States\",\"country\":\"US\",\"domain\":\"indeed.com\",\"date_posted\":\"7\",\"posted_by\":\"employer\",\"location_radius\":\"25\"}", "index": 1, "errors": [["date_posted", "This value is not allowed"], ["posted_by", "This value is not allowed"], ["location_radius", "This value is not allowed"]], "category": "software_engineer", "platform": "indeed", "input_count": 5, "expected_results": 5, "s3_directory": "2025-09-18/indeed/software_engineer/"}, "data_scientist": {"error": "Invalid input provided", "code": "validation_error", "type": "validation", "line": "{\"keyword_search\":\"data scientist\",\"location\":\"United States\",\"country\":\"US\",\"domain\":\"indeed.com\",\"date_posted\":\"7\",\"posted_by\":\"employer\",\"location_radius\":\"25\"}", "index": 1, "errors": [["date_posted", "This value is not allowed"], ["posted_by", "This value is not allowed"], ["location_radius", "This value is not allowed"]], "category": "data_scientist", "platform": "indeed", "input_count": 3, "expected_results": 3, "s3_directory": "2025-09-18/indeed/data_scientist/"}, "data_analyst": {"error": "Invalid input provided", "code": "validation_error", "type": "validation", "line": "{\"keyword_search\":\"data analyst\",\"location\":\"United States\",\"country\":\"US\",\"domain\":\"indeed.com\",\"date_posted\":\"7\",\"posted_by\":\"employer\",\"location_radius\":\"25\"}", "index": 1, "errors": [["date_posted", "This value is not allowed"], ["posted_by", "This value is not allowed"], ["location_radius", "This value is not allowed"]], "category": "data_analyst", "platform": "indeed", "input_count": 2, "expected_results": 2, "s3_directory": "2025-09-18/indeed/data_analyst/"}, "product_manager": {"error": "Invalid input provided", "code": "validation_error", "type": "validation", "line": "{\"keyword_search\":\"product manager\",\"location\":\"United States\",\"country\":\"US\",\"domain\":\"indeed.com\",\"date_posted\":\"7\",\"posted_by\":\"employer\",\"location_radius\":\"25\"}", "index": 1, "errors": [["date_posted", "This value is not allowed"], ["posted_by", "This value is not allowed"], ["location_radius", "This value is not allowed"]], "category": "product_manager", "platform": "indeed", "input_count": 3, "expected_results": 3, "s3_directory": "2025-09-18/indeed/product_manager/"}, "project_manager": {"error": "Invalid input provided", "code": "validation_error", "type": "validation", "line": "{\"keyword_search\":\"project manager\",\"location\":\"United States\",\"country\":\"US\",\"domain\":\"indeed.com\",\"date_posted\":\"7\",\"posted_by\":\"employer\",\"location_radius\":\"25\"}", "index": 1, "errors": [["date_posted", "This value is not allowed"], ["posted_by", "This value is not allowed"], ["location_radius", "This value is not allowed"]], "category": "project_manager", "platform": "indeed", "input_count": 2, "expected_results": 2, "s3_directory": "2025-09-18/indeed/project_manager/"}, "business_analyst": {"error": "Invalid input provided", "code": "validation_error", "type": "validation", "line": "{\"keyword_search\":\"business analyst\",\"location\":\"United States\",\"country\":\"US\",\"domain\":\"indeed.com\",\"date_posted\":\"7\",\"posted_by\":\"employer\",\"location_radius\":\"25\"}", "index": 1, "errors": [["date_posted", "This value is not allowed"], ["posted_by", "This value is not allowed"], ["location_radius", "This value is not allowed"]], "category": "business_analyst", "platform": "indeed", "input_count": 2, "expected_results": 2, "s3_directory": "2025-09-18/indeed/business_analyst/"}, "marketing": {"error": "Invalid input provided", "code": "validation_error", "type": "validation", "line": "{\"keyword_search\":\"marketing coordinator\",\"location\":\"United States\",\"country\":\"US\",\"domain\":\"indeed.com\",\"date_posted\":\"7\",\"posted_by\":\"employer\",\"location_radius\":\"25\"}", "index": 1, "errors": [["date_posted", "This value is not allowed"], ["posted_by", "This value is not allowed"], ["location_radius", "This value is not allowed"]], "category": "marketing", "platform": "indeed", "input_count": 3, "expected_results": 3, "s3_directory": "2025-09-18/indeed/marketing/"}, "sales": {"error": "Invalid input provided", "code": "validation_error", "type": "validation", "line": "{\"keyword_search\":\"sales representative\",\"location\":\"United States\",\"country\":\"US\",\"domain\":\"indeed.com\",\"date_posted\":\"7\",\"posted_by\":\"employer\",\"location_radius\":\"25\"}", "index": 1, "errors": [["date_posted", "This value is not allowed"], ["posted_by", "This value is not allowed"], ["location_radius", "This value is not allowed"]], "category": "sales", "platform": "indeed", "input_count": 2, "expected_results": 2, "s3_directory": "2025-09-18/indeed/sales/"}, "customer_success": {"error": "Invalid input provided", "code": "validation_error", "type": "validation", "line": "{\"keyword_search\":\"customer success\",\"location\":\"United States\",\"country\":\"US\",\"domain\":\"indeed.com\",\"date_posted\":\"7\",\"posted_by\":\"employer\",\"location_radius\":\"25\"}", "index": 1, "errors": [["date_posted", "This value is not allowed"], ["posted_by", "This value is not allowed"], ["location_radius", "This value is not allowed"]], "category": "customer_success", "platform": "indeed", "input_count": 2, "expected_results": 2, "s3_directory": "2025-09-18/indeed/customer_success/"}, "operations": {"error": "Invalid input provided", "code": "validation_error", "type": "validation", "line": "{\"keyword_search\":\"operations coordinator\",\"location\":\"United States\",\"country\":\"US\",\"domain\":\"indeed.com\",\"date_posted\":\"7\",\"posted_by\":\"employer\",\"location_radius\":\"25\"}", "index": 1, "errors": [["date_posted", "This value is not allowed"], ["posted_by", "This value is not allowed"], ["location_radius", "This value is not allowed"]], "category": "operations", "platform": "indeed", "input_count": 2, "expected_results": 2, "s3_directory": "2025-09-18/indeed/operations/"}}, "linkedin": {"software_engineer": {"error": "Invalid input provided", "code": "validation_error", "type": "validation", "line": "{\"keyword\":\"software engineer\",\"location\":\"United States\",\"country\":\"US\",\"time_range\":\"r604800\",\"job_type\":\"F\",\"experience_level\":\"2\",\"remote\":\"false\",\"location_radius\":\"25\",\"company\":\"\"}", "index": 1, "errors": [["time_range", "This value is not allowed"], ["job_type", "This value is not allowed"], ["experience_level", "This value is not allowed"], ["remote", "This value is not allowed"], ["location_radius", "This value is not allowed"]], "category": "software_engineer", "platform": "linkedin", "input_count": 5, "expected_results": 5, "s3_directory": "2025-09-18/linkedin/software_engineer/"}, "data_scientist": {"error": "Invalid input provided", "code": "validation_error", "type": "validation", "line": "{\"keyword\":\"data scientist\",\"location\":\"United States\",\"country\":\"US\",\"time_range\":\"r604800\",\"job_type\":\"F\",\"experience_level\":\"2\",\"remote\":\"false\",\"location_radius\":\"25\",\"company\":\"\"}", "index": 1, "errors": [["time_range", "This value is not allowed"], ["job_type", "This value is not allowed"], ["experience_level", "This value is not allowed"], ["remote", "This value is not allowed"], ["location_radius", "This value is not allowed"]], "category": "data_scientist", "platform": "linkedin", "input_count": 3, "expected_results": 3, "s3_directory": "2025-09-18/linkedin/data_scientist/"}, "data_analyst": {"error": "Invalid input provided", "code": "validation_error", "type": "validation", "line": "{\"keyword\":\"data analyst\",\"location\":\"United States\",\"country\":\"US\",\"time_range\":\"r604800\",\"job_type\":\"F\",\"experience_level\":\"2\",\"remote\":\"false\",\"location_radius\":\"25\",\"company\":\"\"}", "index": 1, "errors": [["time_range", "This value is not allowed"], ["job_type", "This value is not allowed"], ["experience_level", "This value is not allowed"], ["remote", "This value is not allowed"], ["location_radius", "This value is not allowed"]], "category": "data_analyst", "platform": "linkedin", "input_count": 2, "expected_results": 2, "s3_directory": "2025-09-18/linkedin/data_analyst/"}, "product_manager": {"error": "Invalid input provided", "code": "validation_error", "type": "validation", "line": "{\"keyword\":\"product manager\",\"location\":\"United States\",\"country\":\"US\",\"time_range\":\"r604800\",\"job_type\":\"F\",\"experience_level\":\"2\",\"remote\":\"false\",\"location_radius\":\"25\",\"company\":\"\"}", "index": 1, "errors": [["time_range", "This value is not allowed"], ["job_type", "This value is not allowed"], ["experience_level", "This value is not allowed"], ["remote", "This value is not allowed"], ["location_radius", "This value is not allowed"]], "category": "product_manager", "platform": "linkedin", "input_count": 3, "expected_results": 3, "s3_directory": "2025-09-18/linkedin/product_manager/"}, "project_manager": {"error": "Invalid input provided", "code": "validation_error", "type": "validation", "line": "{\"keyword\":\"project manager\",\"location\":\"United States\",\"country\":\"US\",\"time_range\":\"r604800\",\"job_type\":\"F\",\"experience_level\":\"2\",\"remote\":\"false\",\"location_radius\":\"25\",\"company\":\"\"}", "index": 1, "errors": [["time_range", "This value is not allowed"], ["job_type", "This value is not allowed"], ["experience_level", "This value is not allowed"], ["remote", "This value is not allowed"], ["location_radius", "This value is not allowed"]], "category": "project_manager", "platform": "linkedin", "input_count": 2, "expected_results": 2, "s3_directory": "2025-09-18/linkedin/project_manager/"}, "business_analyst": {"error": "Invalid input provided", "code": "validation_error", "type": "validation", "line": "{\"keyword\":\"business analyst\",\"location\":\"United States\",\"country\":\"US\",\"time_range\":\"r604800\",\"job_type\":\"F\",\"experience_level\":\"2\",\"remote\":\"false\",\"location_radius\":\"25\",\"company\":\"\"}", "index": 1, "errors": [["time_range", "This value is not allowed"], ["job_type", "This value is not allowed"], ["experience_level", "This value is not allowed"], ["remote", "This value is not allowed"], ["location_radius", "This value is not allowed"]], "category": "business_analyst", "platform": "linkedin", "input_count": 2, "expected_results": 2, "s3_directory": "2025-09-18/linkedin/business_analyst/"}, "marketing": {"error": "Invalid input provided", "code": "validation_error", "type": "validation", "line": "{\"keyword\":\"marketing coordinator\",\"location\":\"United States\",\"country\":\"US\",\"time_range\":\"r604800\",\"job_type\":\"F\",\"experience_level\":\"2\",\"remote\":\"false\",\"location_radius\":\"25\",\"company\":\"\"}", "index": 1, "errors": [["time_range", "This value is not allowed"], ["job_type", "This value is not allowed"], ["experience_level", "This value is not allowed"], ["remote", "This value is not allowed"], ["location_radius", "This value is not allowed"]], "category": "marketing", "platform": "linkedin", "input_count": 3, "expected_results": 3, "s3_directory": "2025-09-18/linkedin/marketing/"}, "sales": {"error": "Invalid input provided", "code": "validation_error", "type": "validation", "line": "{\"keyword\":\"sales representative\",\"location\":\"United States\",\"country\":\"US\",\"time_range\":\"r604800\",\"job_type\":\"F\",\"experience_level\":\"2\",\"remote\":\"false\",\"location_radius\":\"25\",\"company\":\"\"}", "index": 1, "errors": [["time_range", "This value is not allowed"], ["job_type", "This value is not allowed"], ["experience_level", "This value is not allowed"], ["remote", "This value is not allowed"], ["location_radius", "This value is not allowed"]], "category": "sales", "platform": "linkedin", "input_count": 2, "expected_results": 2, "s3_directory": "2025-09-18/linkedin/sales/"}, "customer_success": {"error": "Invalid input provided", "code": "validation_error", "type": "validation", "line": "{\"keyword\":\"customer success\",\"location\":\"United States\",\"country\":\"US\",\"time_range\":\"r604800\",\"job_type\":\"F\",\"experience_level\":\"2\",\"remote\":\"false\",\"location_radius\":\"25\",\"company\":\"\"}", "index": 1, "errors": [["time_range", "This value is not allowed"], ["job_type", "This value is not allowed"], ["experience_level", "This value is not allowed"], ["remote", "This value is not allowed"], ["location_radius", "This value is not allowed"]], "category": "customer_success", "platform": "linkedin", "input_count": 2, "expected_results": 2, "s3_directory": "2025-09-18/linkedin/customer_success/"}, "operations": {"error": "Invalid input provided", "code": "validation_error", "type": "validation", "line": "{\"keyword\":\"operations coordinator\",\"location\":\"United States\",\"country\":\"US\",\"time_range\":\"r604800\",\"job_type\":\"F\",\"experience_level\":\"2\",\"remote\":\"false\",\"location_radius\":\"25\",\"company\":\"\"}", "index": 1, "errors": [["time_range", "This value is not allowed"], ["job_type", "This value is not allowed"], ["experience_level", "This value is not allowed"], ["remote", "This value is not allowed"], ["location_radius", "This value is not allowed"]], "category": "operations", "platform": "linkedin", "input_count": 2, "expected_results": 2, "s3_directory": "2025-09-18/linkedin/operations/"}}}}