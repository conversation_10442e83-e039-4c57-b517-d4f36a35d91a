#!/usr/bin/env python3
"""
Small test script for LLM processing with minimal cost.

This script tests both LinkedIn and Indeed LLM processing with only 30 jobs each
to minimize LLM API costs while verifying the complete pipeline works.
"""

import sys
import os
import pandas as pd
from pathlib import Path

# Add the src directory to the Python path
sys.path.append(str(Path(__file__).parent))

from core.data_processor import DataProcessor

def test_small_llm_pipeline():
    """Test LLM processing with small sample (30 jobs each)."""
    print("🧪 Testing Small LLM Pipeline (30 jobs each)")
    print("=" * 60)
    
    try:
        # Initialize data processor
        print("🔧 Initializing data processor...")
        processor = DataProcessor(
            linkedin_folder="data/raw/linkedin_data",
            indeed_folder="data/raw/indeed_data",
            output_folder="data/processed/combined_data",
            extract_redirects=False,
            redirect_delay=2,
            linkedin_only=False,
            indeed_only=False,  # Process both
            use_llm_filter=True
        )
        
        print("✅ Data processor initialized successfully")
        
        # Load small samples of data
        print("\n📂 Loading small samples of data...")
        
        # Load LinkedIn data (first 30 jobs)
        linkedin_raw = processor.load_linkedin_data()
        if not linkedin_raw.empty:
            linkedin_sample = linkedin_raw.head(30).copy()
            print(f"📊 LinkedIn sample: {len(linkedin_sample)} jobs")
        else:
            print("⚠️ No LinkedIn data found")
            linkedin_sample = pd.DataFrame()
        
        # Load Indeed data (first 30 jobs)
        indeed_raw = processor.load_indeed_data()
        if not indeed_raw.empty:
            indeed_sample = indeed_raw.head(30).copy()
            print(f"📊 Indeed sample: {len(indeed_sample)} jobs")
        else:
            print("⚠️ No Indeed data found")
            indeed_sample = pd.DataFrame()
        
        if linkedin_sample.empty and indeed_sample.empty:
            print("❌ No data found for testing!")
            return
        
        # Process LinkedIn sample
        linkedin_processed = pd.DataFrame()
        if not linkedin_sample.empty:
            print("\n🔗 Processing LinkedIn sample with LLM...")
            linkedin_processed = processor.process_linkedin_jobs(linkedin_sample)
            print(f"✅ LinkedIn processed: {len(linkedin_processed)} jobs")
        
        # Process Indeed sample
        indeed_processed = pd.DataFrame()
        if not indeed_sample.empty:
            print("\n💼 Processing Indeed sample with LLM...")
            indeed_processed = processor.process_indeed_jobs(indeed_sample)
            print(f"✅ Indeed processed: {len(indeed_processed)} jobs")
        
        # Combine results
        if not linkedin_processed.empty and not indeed_processed.empty:
            combined = pd.concat([linkedin_processed, indeed_processed], ignore_index=True)
            print(f"\n🔗 Combined dataset: {len(combined)} total jobs")
        elif not linkedin_processed.empty:
            combined = linkedin_processed
            print(f"\n🔗 Using LinkedIn only: {len(combined)} jobs")
        else:
            combined = indeed_processed
            print(f"\n🔗 Using Indeed only: {len(combined)} jobs")
        
        # Show results summary
        print("\n📊 Processing Results Summary:")
        print(f"  Total jobs processed: {len(combined)}")
        
        if 'job_decision' in combined.columns:
            decision_dist = combined['job_decision'].value_counts()
            print(f"  Job decisions:")
            for decision, count in decision_dist.items():
                print(f"    {decision}: {count}")
        
        if 'job_quality_score' in combined.columns:
            print(f"  Quality score stats:")
            print(f"    Average: {combined['job_quality_score'].mean():.1f}")
            print(f"    Min: {combined['job_quality_score'].min()}")
            print(f"    Max: {combined['job_quality_score'].max()}")
        
        # Show platform breakdown
        if 'source' in combined.columns:
            platform_dist = combined['source'].value_counts()
            print(f"  Platform breakdown:")
            for platform, count in platform_dist.items():
                print(f"    {platform}: {count}")
        
        # Show sample results
        print("\n📋 Sample Results:")
        for i, (idx, row) in enumerate(combined.head(3).iterrows(), 1):
            print(f"\n  Job {i}:")
            print(f"    Title: {row.get('job_title', 'N/A')}")
            print(f"    Company: {row.get('company_name', 'N/A')}")
            print(f"    Decision: {row.get('job_decision', 'N/A')}")
            print(f"    Score: {row.get('job_quality_score', 'N/A')}")
            print(f"    Work Mode: {row.get('llm_work_mode', 'N/A')}")
            print(f"    Authorization: {row.get('llm_authorization', 'N/A')}")
            if 'llm_job_seniority' in row:
                print(f"    Job Seniority: {row.get('llm_job_seniority', 'N/A')}")
            print(f"    Reason: {row.get('llm_reason', 'N/A')[:100]}...")
        
        # Check for required columns
        required_llm_columns = [
            'job_decision', 'job_quality_score', 'llm_work_mode', 
            'llm_authorization', 'llm_qualifications', 'llm_reason',
            'llm_is_professional', 'llm_is_entry_level', 'llm_is_relevant'
        ]
        
        missing_columns = [col for col in required_llm_columns if col not in combined.columns]
        if missing_columns:
            print(f"\n⚠️ Missing LLM columns: {missing_columns}")
        else:
            print(f"\n✅ All required LLM columns present!")
        
        # Check qualifications format
        if 'llm_qualifications' in combined.columns:
            sample_quals = combined['llm_qualifications'].iloc[0] if len(combined) > 0 else []
            if isinstance(sample_quals, list):
                print(f"✅ Qualifications in array format: {sample_quals}")
            else:
                print(f"⚠️ Qualifications not in array format: {type(sample_quals)}")
        
        # Save results
        output_file = "test_small_llm_results.csv"
        combined.to_csv(output_file, index=False)
        print(f"\n💾 Results saved to: {output_file}")
        
        print("\n🎉 Small LLM pipeline test completed successfully!")
        print("✅ Ready for full pipeline processing!")
        
    except Exception as e:
        print(f"❌ Error in small LLM pipeline test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_small_llm_pipeline()
