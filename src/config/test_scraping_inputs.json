{"metadata": {"created_at": "2025-01-27T00:00:00.000000", "total_indeed_categories": 1, "total_linkedin_categories": 1, "total_indeed_inputs": 1, "total_linkedin_inputs": 1, "description": "Minimal test configuration for API testing"}, "indeed": {"software_engineer": [{"keyword_search": "software engineer", "location": "United States", "country": "US", "domain": "indeed.com", "date_posted": "7", "posted_by": "employer", "location_radius": "25"}]}, "linkedin": {"software_engineer": [{"keyword": "software engineer", "location": "United States", "country": "US", "time_range": "r604800", "job_type": "F", "experience_level": "2", "remote": "false", "location_radius": "25"}]}}