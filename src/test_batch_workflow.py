#!/usr/bin/env python3
"""
Test script for LinkedIn batch processing workflow.

This script tests the new batch processing method that:
1. Processes all LinkedIn jobs in batches
2. Keeps both KEPT and REJECTED jobs
3. Adds assessment columns for all jobs
4. Does NOT filter out rejected jobs
"""

import sys
import os
import pandas as pd
import logging
from pathlib import Path

# Add src to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from llm.groq_client import GroqStructuredClient, GroqConfig
from llm.linkedin_processor import LinkedInJobProcessor, LinkedInProcessorConfig

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_batch_workflow():
    """Test the batch processing workflow with a sample of LinkedIn data."""
    
    print("🧪 Testing LinkedIn Batch Processing Workflow")
    print("=" * 60)
    
    # Load sample data
    data_file = Path("data/raw/linkedin_jobs.csv")
    if not data_file.exists():
        print(f"❌ Data file not found: {data_file}")
        return False
    
    df = pd.read_csv(data_file)
    print(f"📊 Loaded {len(df)} LinkedIn jobs from {data_file}")
    
    # Take a small sample for testing (first 50 jobs)
    df_sample = df.head(50).copy()
    print(f"🧪 Testing with {len(df_sample)} jobs")
    
    # Initialize Groq client
    try:
        groq_config = GroqConfig(
            api_key="gsk_your_api_key_here",  # Replace with actual API key
            model="openai/gpt-oss-120b",
            temperature=0.1,
            max_tokens=10000,
            timeout=30,
            max_retries=3,
            rate_limit_delay=1.0
        )
        
        processor_config = LinkedInProcessorConfig(
            batch_size=10,
            max_workers=2,
            delay_between_batches=2.0,
            retry_failed_jobs=True,
            max_retries=2,
            enable_parallel_processing=False,  # Disable for testing
            use_processed_description=True
        )
        
        processor = LinkedInJobProcessor(groq_config, processor_config)
        
    except Exception as e:
        print(f"❌ Error initializing processor: {e}")
        return False
    
    # Test batch processing workflow
    try:
        print("\n🚀 Starting batch processing workflow...")
        result_df = processor.process_linkedin_jobs_batch_workflow(
            df_sample, 
            batch_size=10
        )
        
        print(f"\n📊 Batch Processing Results:")
        print(f"Total jobs processed: {len(result_df)}")
        
        # Count decisions
        kept_jobs = len(result_df[result_df['job_decision'] == 'KEEP'])
        rejected_jobs = len(result_df[result_df['job_decision'] == 'REJECT'])
        error_jobs = len(result_df[result_df['job_decision'] == 'ERROR'])
        
        print(f"Jobs KEPT: {kept_jobs}")
        print(f"Jobs REJECTED: {rejected_jobs}")
        print(f"Jobs with ERRORS: {error_jobs}")
        
        # Show sample results
        print(f"\n📋 Sample Results (First 5 jobs):")
        for i, (_, row) in enumerate(result_df.head(5).iterrows()):
            print(f"\n--- Job {i+1} ---")
            print(f"Title: {row.get('job_title', 'N/A')}")
            print(f"Company: {row.get('company_name', 'N/A')}")
            print(f"Decision: {row.get('job_decision', 'N/A')}")
            print(f"Quality Score: {row.get('job_quality_score', 'N/A')}")
            print(f"Work Mode: {row.get('llm_work_mode', 'N/A')}")
            print(f"Authorization: {row.get('llm_authorization', 'N/A')}")
            print(f"Qualifications: {row.get('llm_qualifications', 'N/A')}")
            print(f"Reason: {row.get('llm_reason', 'N/A')}")
        
        # Save results
        output_file = "test_batch_workflow_results.csv"
        result_df.to_csv(output_file, index=False)
        print(f"\n💾 Results saved to: {output_file}")
        
        print("\n🎉 Batch processing workflow test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error in batch processing: {e}")
        return False

if __name__ == "__main__":
    success = test_batch_workflow()
    sys.exit(0 if success else 1)
