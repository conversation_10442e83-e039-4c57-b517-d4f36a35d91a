#!/usr/bin/env python3
"""
Minimal test script to test a single BrightData API call.
This avoids costs by testing with just one input.
"""

import sys
import os
import json
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent))

from core.scraper import BrightData<PERSON><PERSON>raper

def test_single_api_call():
    """Test a single API call to verify the configuration works."""
    print("🧪 Testing single BrightData API call...")
    
    try:
        # Initialize scraper with correct config paths
        scraper = BrightDataScraper(
            config_file="config/test_scraping_inputs.json",
            brightdata_config_file="config/brightdata_config.json"
        )
        
        # Test with just one Indeed input to minimize costs - using legacy format
        test_inputs = [
            {
                "keyword_search": "software engineer",
                "location": "United States",
                "country": "US",
                "domain": "indeed.com",
                "date_posted": "Last 24 hours",
                "posted_by": "",
                "location_radius": ""
            }
        ]
        
        print("🔍 Testing Indeed API call...")
        result = scraper.trigger_single_category_scraping(
            platform="indeed",
            category="test_software_engineer",
            inputs=test_inputs
        )
        
        if "error" in result:
            print(f"❌ API call failed: {result['error']}")
            return False
        else:
            print(f"✅ API call successful!")
            print(f"   📊 Snapshot ID: {result.get('snapshot_id', 'N/A')}")
            print(f"   📁 S3 Path: {result.get('s3_directory', 'N/A')}")
            return True
            
    except Exception as e:
        print(f"❌ Test failed with exception: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_single_api_call()
    if success:
        print("\n🎉 Single API call test PASSED!")
    else:
        print("\n💥 Single API call test FAILED!")
        sys.exit(1)
