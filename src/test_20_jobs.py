#!/usr/bin/env python3
"""
Test script for processing first 20 LinkedIn jobs with LLM
"""

import sys
import os
import pandas as pd
from pathlib import Path

# Add src to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_20_linkedin_jobs():
    """Test LLM processing with first 20 LinkedIn jobs."""
    try:
        from llm.linkedin_processor import LinkedInJobProcessor, LinkedInProcessorConfig
        from llm.groq_client import GroqConfig
        import json
        
        # Load config
        config_path = "config/llm_config.json"
        with open(config_path, 'r') as f:
            config_data = json.load(f)
        
        llm_config = config_data.get('llm', {})
        groq_config = GroqConfig(
            api_key=llm_config.get('api_key', ''),
            model=llm_config.get('model', 'openai/gpt-oss-20b'),
            temperature=llm_config.get('temperature', 0.1),
            max_tokens=800,
            timeout=llm_config.get('timeout', 30),
            max_retries=llm_config.get('max_retries', 3),
            rate_limit_delay=llm_config.get('rate_limit_delay', 1.0)
        )
        
        processor_config = LinkedInProcessorConfig(
            batch_size=5,  # Process 5 jobs at a time
            max_workers=1,  # Single worker for stability
            delay_between_batches=3.0,  # 3 second delay between batches
            enable_parallel_processing=False,
            use_processed_description=True
        )
        
        processor = LinkedInJobProcessor(groq_config, processor_config)
        
        # Load LinkedIn data
        print("📂 Loading LinkedIn data...")
        linkedin_file = "data/raw/linkedin_data/s_mffbl84c2flosm1xg.csv"
        df = pd.read_csv(linkedin_file)
        
        # Take only first 20 jobs
        df_sample = df.head(20).copy()
        print(f"📊 Loaded {len(df_sample)} jobs for testing")
        
        # Show sample job titles
        print("\n📋 Sample job titles:")
        for i, title in enumerate(df_sample['job_title'].head(5)):
            print(f"  {i+1}. {title}")
        
        # Process jobs with LLM
        print(f"\n🤖 Processing {len(df_sample)} jobs with LLM...")
        results = processor.process_linkedin_jobs(df_sample, test_mode=True)
        
        # Show results summary
        print("\n📊 Results Summary:")
        print(f"Total jobs processed: {len(results)}")
        
        kept_count = len(results[results['job_decision'] == 'KEEP'])
        rejected_count = len(results[results['job_decision'] == 'REJECT'])
        error_count = len(results[results['job_decision'] == 'ERROR'])
        
        print(f"Jobs kept: {kept_count}")
        print(f"Jobs rejected: {rejected_count}")
        print(f"Jobs with errors: {error_count}")
        
        # Show detailed results for first 5 jobs
        print("\n📋 Detailed Results (First 5 jobs):")
        for idx, row in results.head(5).iterrows():
            print(f"\n--- Job {idx + 1} ---")
            print(f"Title: {row['job_title']}")
            print(f"Company: {row['company_name']}")
            print(f"Decision: {row['job_decision']}")
            print(f"Quality Score: {row['job_quality_score']}")
            print(f"Work Mode: {row['llm_work_mode']}")
            print(f"Authorization: {row['llm_authorization']}")
            print(f"Professional: {row['llm_is_professional']}")
            print(f"Entry Level: {row['llm_is_entry_level']}")
            print(f"Relevant: {row['llm_is_relevant']}")
            print(f"Qualifications: {row['llm_qualifications']}")
            print(f"Reason: {row['llm_reason'][:100]}...")
        
        # Show work mode distribution
        print("\n🏠 Work Mode Distribution:")
        work_mode_counts = results['llm_work_mode'].value_counts()
        for mode, count in work_mode_counts.items():
            print(f"  {mode}: {count}")
        
        # Show authorization distribution
        print("\n🛂 Authorization Distribution:")
        auth_counts = results['llm_authorization'].value_counts()
        for auth, count in auth_counts.items():
            print(f"  {auth}: {count}")
        
        # Show quality score statistics
        print("\n📈 Quality Score Statistics:")
        print(f"  Average: {results['job_quality_score'].mean():.1f}")
        print(f"  Min: {results['job_quality_score'].min()}")
        print(f"  Max: {results['job_quality_score'].max()}")
        
        # Save results to CSV for inspection
        output_file = "test_20_jobs_results.csv"
        results.to_csv(output_file, index=False)
        print(f"\n💾 Results saved to: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Testing 20 LinkedIn Jobs with LLM")
    print("=" * 60)
    
    success = test_20_linkedin_jobs()
    
    if success:
        print("\n🎉 20-job test completed successfully!")
    else:
        print("\n❌ 20-job test failed!")
    
    sys.exit(0 if success else 1)
