#!/usr/bin/env python3
"""
Test script for small batch LinkedIn LLM processing
"""

import sys
import os
import pandas as pd
from pathlib import Path

# Add src to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_small_batch():
    """Test LLM processing with a small batch of jobs."""
    try:
        from llm.linkedin_processor import LinkedInJobProcessor, LinkedInProcessorConfig
        from llm.groq_client import GroqConfig
        import json
        
        # Load config
        config_path = "config/llm_config.json"
        with open(config_path, 'r') as f:
            config_data = json.load(f)
        
        llm_config = config_data.get('llm', {})
        groq_config = GroqConfig(
            api_key=llm_config.get('api_key', ''),
            model=llm_config.get('model', 'openai/gpt-oss-20b'),
            temperature=llm_config.get('temperature', 0.1),
            max_tokens=500,
            timeout=llm_config.get('timeout', 30),
            max_retries=llm_config.get('max_retries', 3),
            rate_limit_delay=llm_config.get('rate_limit_delay', 1.0)
        )
        
        processor_config = LinkedInProcessorConfig(
            batch_size=2,  # Very small batch
            max_workers=1,  # Single worker
            delay_between_batches=3.0,  # Longer delay
            enable_parallel_processing=False,
            use_processed_description=True
        )
        
        processor = LinkedInJobProcessor(groq_config, processor_config)
        
        # Create test data
        test_data = {
            'job_title': ['Software Engineer Intern', 'Marketing Intern'],
            'company_name': ['Tech Corp', 'Marketing Inc'],
            'processed_description': [
                'Software development internship. Learn Python, JavaScript. 0-2 years experience.',
                'Marketing internship. Social media, content creation. Entry level position.'
            ],
            'keyword': ['software', 'marketing'],
            'job_location': ['New York, NY', 'San Francisco, CA'],
            'company_industry': ['Technology', 'Marketing']
        }
        
        df = pd.DataFrame(test_data)
        print(f"🧪 Testing with {len(df)} jobs...")
        
        # Process jobs
        results = processor.process_linkedin_jobs(df, test_mode=True)
        
        print("\n📊 Results:")
        print(f"Total jobs: {len(results)}")
        print(f"Jobs kept: {len(results[results['job_decision'] == 'KEEP'])}")
        print(f"Jobs rejected: {len(results[results['job_decision'] == 'REJECT'])}")
        print(f"Jobs with errors: {len(results[results['job_decision'] == 'ERROR'])}")
        
        # Show sample results
        print("\n📋 Sample Results:")
        for idx, row in results.head(2).iterrows():
            print(f"\nJob {idx + 1}: {row['job_title']}")
            print(f"  Decision: {row['job_decision']}")
            print(f"  Score: {row['job_quality_score']}")
            print(f"  Work Mode: {row['llm_work_mode']}")
            print(f"  Authorization: {row['llm_authorization']}")
            print(f"  Reason: {row['llm_reason']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Testing Small Batch LinkedIn LLM Processing")
    print("=" * 60)
    
    success = test_small_batch()
    
    if success:
        print("\n🎉 Small batch test successful!")
    else:
        print("\n❌ Small batch test failed!")
    
    sys.exit(0 if success else 1)
