# 🚀 Job Scraper & Data Processing Pipeline

## 📋 **Quick Summary**

This project **scrapes job data** from LinkedIn and Indeed, **processes it intelligently**, and **outputs clean, structured data** for analysis.

## 🎯 **What It Does**

1. **Scrapes jobs** from LinkedIn and Indeed using BrightData API
2. **Filters out non-professional jobs** (cook, driver, nurse, etc.)
3. **Extracts key information** (work mode, authorization, qualifications)
4. **Outputs structured CSV** with 23 columns of processed data

## 🏃‍♂️ **Quick Start**

```bash
# Process LinkedIn data only
python main.py --action process --linkedin-only

# Process with redirect extraction
python main.py --action process --linkedin-only --extract-redirects

# Full pipeline (scrape + process + excel)
python main.py --action all --linkedin-only
```

## 📊 **Current Results**

- **Input:** 1,812 LinkedIn jobs
- **Output:** 729 professional jobs (60% reduction)
- **Processing Time:** ~2 seconds
- **Accuracy:** 40-70% (needs improvement)

## 🔧 **Current Architecture**

```
Raw Job Data → Job Filtering → NLTK Processing → Work Mode/Auth Extraction → Final Output
```

## 🚀 **Future Plan: LLM Integration**

We're planning to integrate **Groq + GPT OSS 20B** to improve accuracy:
- **Work Mode Detection:** 60% → 85-90%
- **Authorization Detection:** 40% → 85-90%
- **Job Filtering:** 70% → 90-95%
- **Cost:** $0.02 per 729 jobs

## 📁 **File Structure**

```
src/
├── main.py                    # 🚀 Entry point
├── PROJECT_OVERVIEW.md        # 📋 Detailed documentation
├── ARCHITECTURE_FLOW.md       # 🏗️ Architecture diagrams
├── core/                      # 🔧 Core processing logic
├── llm/                       # 🤖 LLM integration (future)
├── utils/                     # 🛠️ Utility functions
├── config/                    # ⚙️ Configuration files
└── data/                      # 📁 Data storage
```

## 📖 **Documentation**

- **`PROJECT_OVERVIEW.md`** - Complete project documentation
- **`ARCHITECTURE_FLOW.md`** - Architecture diagrams and flowcharts

## 🎯 **Next Steps**

1. **Implement LLM integration** for better accuracy
2. **Test and optimize** the hybrid approach
3. **Monitor accuracy and costs**

---

**The codebase is now clean, organized, and ready for LLM integration!**