{"metadata": {"scraping_timestamp": "2025-09-19T22:07:54.259615", "config_file_used": "config/scraping_inputs.json", "total_api_calls": 20, "platforms_scraped": ["indeed", "linkedin"]}, "results": {"indeed": {"software_engineer": {"snapshot_id": "s_mfr2b4t51l5b5v4dk3", "category": "software_engineer", "platform": "indeed", "input_count": 5, "expected_results": 5, "s3_directory": "2025-09-19/indeed/software_engineer/"}, "data_scientist": {"snapshot_id": "s_mfr2b781zl7nye5tv", "category": "data_scientist", "platform": "indeed", "input_count": 3, "expected_results": 3, "s3_directory": "2025-09-19/indeed/data_scientist/"}, "data_analyst": {"snapshot_id": "s_mfr2b9ol1raifcuvqd", "category": "data_analyst", "platform": "indeed", "input_count": 2, "expected_results": 2, "s3_directory": "2025-09-19/indeed/data_analyst/"}, "product_manager": {"snapshot_id": "s_mfr2bc7frmhr04xo8", "category": "product_manager", "platform": "indeed", "input_count": 3, "expected_results": 3, "s3_directory": "2025-09-19/indeed/product_manager/"}, "project_manager": {"snapshot_id": "s_mfr2bepz27na7mppb7", "category": "project_manager", "platform": "indeed", "input_count": 2, "expected_results": 2, "s3_directory": "2025-09-19/indeed/project_manager/"}, "business_analyst": {"snapshot_id": "s_mfr2bh481iu82hc8w", "category": "business_analyst", "platform": "indeed", "input_count": 2, "expected_results": 2, "s3_directory": "2025-09-19/indeed/business_analyst/"}, "marketing": {"snapshot_id": "s_mfr2bjvri1pgcz33v", "category": "marketing", "platform": "indeed", "input_count": 3, "expected_results": 3, "s3_directory": "2025-09-19/indeed/marketing/"}, "sales": {"snapshot_id": "s_mfr2bm9t2p9xz0efp1", "category": "sales", "platform": "indeed", "input_count": 2, "expected_results": 2, "s3_directory": "2025-09-19/indeed/sales/"}, "customer_success": {"snapshot_id": "s_mfr2bopf24c377ne5g", "category": "customer_success", "platform": "indeed", "input_count": 2, "expected_results": 2, "s3_directory": "2025-09-19/indeed/customer_success/"}, "operations": {"snapshot_id": "s_mfr2br4w25luwpekjd", "category": "operations", "platform": "indeed", "input_count": 2, "expected_results": 2, "s3_directory": "2025-09-19/indeed/operations/"}}, "linkedin": {"software_engineer": {"error": "HTTP 400: {\"error\":\"Invalid input provided\",\"code\":\"validation_error\",\"type\":\"validation\",\"line\":\"{\\\"keyword\\\":\\\"software engineer\\\",\\\"location\\\":\\\"United States\\\",\\\"country\\\":\\\"US\\\",\\\"time_range\\\":\\\"r604800\\\",\\\"job_type\\\":\\\"F\\\",\\\"experience_level\\\":\\\"2\\\",\\\"remote\\\":\\\"false\\\",\\\"location_radius\\\":\\\"25\\\",\\\"company\\\":\\\"\\\"}\",\"index\":1,\"errors\":[[\"time_range\",\"This value is not allowed\"],[\"job_type\",\"This value is not allowed\"],[\"experience_level\",\"This value is not allowed\"],[\"remote\",\"This value is not allowed\"],[\"location_radius\",\"This value is not allowed\"]]}", "category": "software_engineer", "platform": "linkedin", "input_count": 5}, "data_scientist": {"error": "HTTP 400: {\"error\":\"Invalid input provided\",\"code\":\"validation_error\",\"type\":\"validation\",\"line\":\"{\\\"keyword\\\":\\\"data scientist\\\",\\\"location\\\":\\\"United States\\\",\\\"country\\\":\\\"US\\\",\\\"time_range\\\":\\\"r604800\\\",\\\"job_type\\\":\\\"F\\\",\\\"experience_level\\\":\\\"2\\\",\\\"remote\\\":\\\"false\\\",\\\"location_radius\\\":\\\"25\\\",\\\"company\\\":\\\"\\\"}\",\"index\":1,\"errors\":[[\"time_range\",\"This value is not allowed\"],[\"job_type\",\"This value is not allowed\"],[\"experience_level\",\"This value is not allowed\"],[\"remote\",\"This value is not allowed\"],[\"location_radius\",\"This value is not allowed\"]]}", "category": "data_scientist", "platform": "linkedin", "input_count": 3}, "data_analyst": {"error": "HTTP 400: {\"error\":\"Invalid input provided\",\"code\":\"validation_error\",\"type\":\"validation\",\"line\":\"{\\\"keyword\\\":\\\"data analyst\\\",\\\"location\\\":\\\"United States\\\",\\\"country\\\":\\\"US\\\",\\\"time_range\\\":\\\"r604800\\\",\\\"job_type\\\":\\\"F\\\",\\\"experience_level\\\":\\\"2\\\",\\\"remote\\\":\\\"false\\\",\\\"location_radius\\\":\\\"25\\\",\\\"company\\\":\\\"\\\"}\",\"index\":1,\"errors\":[[\"time_range\",\"This value is not allowed\"],[\"job_type\",\"This value is not allowed\"],[\"experience_level\",\"This value is not allowed\"],[\"remote\",\"This value is not allowed\"],[\"location_radius\",\"This value is not allowed\"]]}", "category": "data_analyst", "platform": "linkedin", "input_count": 2}, "product_manager": {"error": "HTTP 400: {\"error\":\"Invalid input provided\",\"code\":\"validation_error\",\"type\":\"validation\",\"line\":\"{\\\"keyword\\\":\\\"product manager\\\",\\\"location\\\":\\\"United States\\\",\\\"country\\\":\\\"US\\\",\\\"time_range\\\":\\\"r604800\\\",\\\"job_type\\\":\\\"F\\\",\\\"experience_level\\\":\\\"2\\\",\\\"remote\\\":\\\"false\\\",\\\"location_radius\\\":\\\"25\\\",\\\"company\\\":\\\"\\\"}\",\"index\":1,\"errors\":[[\"time_range\",\"This value is not allowed\"],[\"job_type\",\"This value is not allowed\"],[\"experience_level\",\"This value is not allowed\"],[\"remote\",\"This value is not allowed\"],[\"location_radius\",\"This value is not allowed\"]]}", "category": "product_manager", "platform": "linkedin", "input_count": 3}, "project_manager": {"error": "HTTP 400: {\"error\":\"Invalid input provided\",\"code\":\"validation_error\",\"type\":\"validation\",\"line\":\"{\\\"keyword\\\":\\\"project manager\\\",\\\"location\\\":\\\"United States\\\",\\\"country\\\":\\\"US\\\",\\\"time_range\\\":\\\"r604800\\\",\\\"job_type\\\":\\\"F\\\",\\\"experience_level\\\":\\\"2\\\",\\\"remote\\\":\\\"false\\\",\\\"location_radius\\\":\\\"25\\\",\\\"company\\\":\\\"\\\"}\",\"index\":1,\"errors\":[[\"time_range\",\"This value is not allowed\"],[\"job_type\",\"This value is not allowed\"],[\"experience_level\",\"This value is not allowed\"],[\"remote\",\"This value is not allowed\"],[\"location_radius\",\"This value is not allowed\"]]}", "category": "project_manager", "platform": "linkedin", "input_count": 2}, "business_analyst": {"error": "HTTP 400: {\"error\":\"Invalid input provided\",\"code\":\"validation_error\",\"type\":\"validation\",\"line\":\"{\\\"keyword\\\":\\\"business analyst\\\",\\\"location\\\":\\\"United States\\\",\\\"country\\\":\\\"US\\\",\\\"time_range\\\":\\\"r604800\\\",\\\"job_type\\\":\\\"F\\\",\\\"experience_level\\\":\\\"2\\\",\\\"remote\\\":\\\"false\\\",\\\"location_radius\\\":\\\"25\\\",\\\"company\\\":\\\"\\\"}\",\"index\":1,\"errors\":[[\"time_range\",\"This value is not allowed\"],[\"job_type\",\"This value is not allowed\"],[\"experience_level\",\"This value is not allowed\"],[\"remote\",\"This value is not allowed\"],[\"location_radius\",\"This value is not allowed\"]]}", "category": "business_analyst", "platform": "linkedin", "input_count": 2}, "marketing": {"error": "HTTP 400: {\"error\":\"Invalid input provided\",\"code\":\"validation_error\",\"type\":\"validation\",\"line\":\"{\\\"keyword\\\":\\\"marketing coordinator\\\",\\\"location\\\":\\\"United States\\\",\\\"country\\\":\\\"US\\\",\\\"time_range\\\":\\\"r604800\\\",\\\"job_type\\\":\\\"F\\\",\\\"experience_level\\\":\\\"2\\\",\\\"remote\\\":\\\"false\\\",\\\"location_radius\\\":\\\"25\\\",\\\"company\\\":\\\"\\\"}\",\"index\":1,\"errors\":[[\"time_range\",\"This value is not allowed\"],[\"job_type\",\"This value is not allowed\"],[\"experience_level\",\"This value is not allowed\"],[\"remote\",\"This value is not allowed\"],[\"location_radius\",\"This value is not allowed\"]]}", "category": "marketing", "platform": "linkedin", "input_count": 3}, "sales": {"error": "HTTP 400: {\"error\":\"Invalid input provided\",\"code\":\"validation_error\",\"type\":\"validation\",\"line\":\"{\\\"keyword\\\":\\\"sales representative\\\",\\\"location\\\":\\\"United States\\\",\\\"country\\\":\\\"US\\\",\\\"time_range\\\":\\\"r604800\\\",\\\"job_type\\\":\\\"F\\\",\\\"experience_level\\\":\\\"2\\\",\\\"remote\\\":\\\"false\\\",\\\"location_radius\\\":\\\"25\\\",\\\"company\\\":\\\"\\\"}\",\"index\":1,\"errors\":[[\"time_range\",\"This value is not allowed\"],[\"job_type\",\"This value is not allowed\"],[\"experience_level\",\"This value is not allowed\"],[\"remote\",\"This value is not allowed\"],[\"location_radius\",\"This value is not allowed\"]]}", "category": "sales", "platform": "linkedin", "input_count": 2}, "customer_success": {"error": "HTTP 400: {\"error\":\"Invalid input provided\",\"code\":\"validation_error\",\"type\":\"validation\",\"line\":\"{\\\"keyword\\\":\\\"customer success\\\",\\\"location\\\":\\\"United States\\\",\\\"country\\\":\\\"US\\\",\\\"time_range\\\":\\\"r604800\\\",\\\"job_type\\\":\\\"F\\\",\\\"experience_level\\\":\\\"2\\\",\\\"remote\\\":\\\"false\\\",\\\"location_radius\\\":\\\"25\\\",\\\"company\\\":\\\"\\\"}\",\"index\":1,\"errors\":[[\"time_range\",\"This value is not allowed\"],[\"job_type\",\"This value is not allowed\"],[\"experience_level\",\"This value is not allowed\"],[\"remote\",\"This value is not allowed\"],[\"location_radius\",\"This value is not allowed\"]]}", "category": "customer_success", "platform": "linkedin", "input_count": 2}, "operations": {"error": "HTTP 400: {\"error\":\"Invalid input provided\",\"code\":\"validation_error\",\"type\":\"validation\",\"line\":\"{\\\"keyword\\\":\\\"operations coordinator\\\",\\\"location\\\":\\\"United States\\\",\\\"country\\\":\\\"US\\\",\\\"time_range\\\":\\\"r604800\\\",\\\"job_type\\\":\\\"F\\\",\\\"experience_level\\\":\\\"2\\\",\\\"remote\\\":\\\"false\\\",\\\"location_radius\\\":\\\"25\\\",\\\"company\\\":\\\"\\\"}\",\"index\":1,\"errors\":[[\"time_range\",\"This value is not allowed\"],[\"job_type\",\"This value is not allowed\"],[\"experience_level\",\"This value is not allowed\"],[\"remote\",\"This value is not allowed\"],[\"location_radius\",\"This value is not allowed\"]]}", "category": "operations", "platform": "linkedin", "input_count": 2}}}}