# 💼 Indeed-Only Processing Usage Guide

## 🚀 **New Feature: --indeed-only Flag**

The job scraper now supports processing Indeed jobs only, without LinkedIn data. This is useful for testing Indeed-specific functionality and processing.

## 📋 **Available Commands**

### **Basic Indeed-Only Processing**
```bash
# Process only Indeed jobs (no LLM)
python3 main.py --action process --indeed-only

# Process Indeed jobs with redirect extraction
python3 main.py --action process --indeed-only --extract-redirects

# Full pipeline: scrape + process + excel (Indeed only)
python3 main.py --action all --indeed-only
```

### **Comparison with LinkedIn-Only**
```bash
# LinkedIn only (with LLM processing)
python3 main.py --action process --linkedin-only

# Indeed only (without LLM processing)
python3 main.py --action process --indeed-only

# Both platforms (default)
python3 main.py --action process
```

## 🔧 **What Happens in Indeed-Only Mode**

1. **Data Loading**: Only loads Indeed CSV files from `data/raw/indeed_data/`
2. **Enhanced Processing**: Uses same preprocessing pipeline as LinkedIn
3. **Job Filtering**: Enhanced filtering removes non-professional jobs
4. **Job Descriptions**: NLTK processing for 3-line summaries (like LinkedIn)
5. **Company Name Standardization**: Amazon.com services.llc → Amazon
6. **Duplicate Removal**: Removes duplicate jobs using jobid
7. **Senior Role Filtering**: Removes senior positions
8. **Work Mode**: "Not Specified" (handled by LLM only)
9. **Authorization**: "Not Specified" (handled by LLM only)
10. **Output**: Saves processed Indeed jobs to `data/processed/combined_data/`

## 📊 **Expected Results**

- **Input**: ~14 Indeed CSV files with various job categories
- **Processing**: Enhanced preprocessing (same as LinkedIn)
- **Output**: Filtered Indeed jobs with standardized columns
- **Job Descriptions**: NLTK-processed 3-line summaries (like LinkedIn)
- **Company Names**: Standardized (Amazon.com services.llc → Amazon)
- **Work Mode**: "Not Specified" (LLM will handle)
- **Authorization**: "Not Specified" (LLM will handle)
- **Filtering**: Enhanced job filtering removes non-professional roles

## 🧪 **Testing the Feature**

### **Run the Test Scripts**
```bash
# Test fixed Indeed-only processing (recommended)
python3 test_fixed_indeed.py

# Test enhanced Indeed-only processing
python3 test_enhanced_indeed.py

# Test basic Indeed-only processing
python3 test_indeed_only.py

# Verify integration
python3 verify_indeed_only.py
```

### **Check Results**
- Look for `test_fixed_indeed_results.csv` in the src directory (recommended)
- Look for `test_enhanced_indeed_results.csv` in the src directory
- Look for `test_indeed_only_results.csv` in the src directory
- Check `data/processed/combined_data/` for the main output
- Review logs for processing details

## 📁 **Output Files**

- **Main Output**: `data/processed/combined_data/combined_jobs.csv`
- **Summary**: `data/processed/combined_data/data_summary_YYYYMMDD_HHMMSS.txt`
- **Test Output**: `test_fixed_indeed_results.csv` (fixed processing - recommended)
- **Test Output**: `test_enhanced_indeed_results.csv` (enhanced processing)
- **Test Output**: `test_indeed_only_results.csv` (basic processing)

## 🔍 **Key Differences from LinkedIn Processing**

| Feature | LinkedIn-Only | Indeed-Only |
|---------|---------------|-------------|
| **LLM Processing** | ✅ Yes (Groq + GPT OSS 120B) | ❌ No (regex only) |
| **Work Mode Detection** | ✅ 85-90% accuracy (LLM) | ✅ 60% accuracy (regex) |
| **Authorization Detection** | ✅ 85-90% accuracy (LLM) | ✅ 40% accuracy (regex) |
| **Job Description Processing** | ✅ NLTK + LLM | ✅ NLTK only |
| **Quality Scoring** | ✅ LLM-based scoring | ❌ Basic filtering |
| **Duplicate Removal** | ✅ Yes | ✅ Yes |
| **Senior Role Filtering** | ✅ Yes | ✅ Yes |
| **Processing Time** | ~5 seconds | ~3 seconds |
| **Accuracy** | High (LLM) | Medium (regex) |

## 🚀 **Next Steps**

1. **Test the basic functionality** with `--indeed-only` flag
2. **Review the results** to understand current Indeed processing
3. **Implement LLM processing** for Indeed (similar to LinkedIn)
4. **Add Indeed-specific test scripts** for comprehensive testing

## 💡 **Notes**

- The Indeed-only mode currently uses basic regex filtering
- Work mode and authorization are not detected (set to "Not Specified")
- This is perfect for testing the basic Indeed processing pipeline
- Future enhancement will add LLM processing for Indeed jobs
