{"metadata": {"scraping_timestamp": "2025-09-21T00:16:17.010890", "config_file_used": "config/scraping_inputs.json", "total_api_calls": 20, "platforms_scraped": ["indeed", "linkedin"]}, "results": {"indeed": {"software_engineer": {"snapshot_id": "s_mfsmc5tgdfv5fgy5v", "category": "software_engineer", "platform": "indeed", "input_count": 5, "expected_results": 5, "s3_directory": "2025-09-21/indeed/software_engineer/"}, "data_scientist": {"snapshot_id": "s_mfsmc85h1wib7oybpv", "category": "data_scientist", "platform": "indeed", "input_count": 3, "expected_results": 3, "s3_directory": "2025-09-21/indeed/data_scientist/"}, "data_analyst": {"snapshot_id": "s_mfsmcaia1j3j2zvrhl", "category": "data_analyst", "platform": "indeed", "input_count": 2, "expected_results": 2, "s3_directory": "2025-09-21/indeed/data_analyst/"}, "product_manager": {"snapshot_id": "s_mfsmccw91h94qwdr29", "category": "product_manager", "platform": "indeed", "input_count": 3, "expected_results": 3, "s3_directory": "2025-09-21/indeed/product_manager/"}, "project_manager": {"snapshot_id": "s_mfsmcf991qbe8tkyum", "category": "project_manager", "platform": "indeed", "input_count": 2, "expected_results": 2, "s3_directory": "2025-09-21/indeed/project_manager/"}, "business_analyst": {"snapshot_id": "s_mfsmchjot9mbcae8f", "category": "business_analyst", "platform": "indeed", "input_count": 2, "expected_results": 2, "s3_directory": "2025-09-21/indeed/business_analyst/"}, "marketing": {"snapshot_id": "s_mfsmcjwzzzywbi5lr", "category": "marketing", "platform": "indeed", "input_count": 3, "expected_results": 3, "s3_directory": "2025-09-21/indeed/marketing/"}, "sales": {"snapshot_id": "s_mfsmcm9s11lc0qo3iz", "category": "sales", "platform": "indeed", "input_count": 2, "expected_results": 2, "s3_directory": "2025-09-21/indeed/sales/"}, "customer_success": {"snapshot_id": "s_mfsmconw2hqnnudo8e", "category": "customer_success", "platform": "indeed", "input_count": 2, "expected_results": 2, "s3_directory": "2025-09-21/indeed/customer_success/"}, "operations": {"snapshot_id": "s_mfsmcr0u2o3gamguyh", "category": "operations", "platform": "indeed", "input_count": 2, "expected_results": 2, "s3_directory": "2025-09-21/indeed/operations/"}}, "linkedin": {"software_engineer": {"error": "HTTP 400: {\"error\":\"Invalid input provided\",\"code\":\"validation_error\",\"type\":\"validation\",\"line\":\"{\\\"keyword\\\":\\\"software engineer\\\",\\\"location\\\":\\\"United States\\\",\\\"country\\\":\\\"US\\\",\\\"time_range\\\":\\\"r604800\\\",\\\"job_type\\\":\\\"F\\\",\\\"experience_level\\\":\\\"2\\\",\\\"remote\\\":\\\"false\\\",\\\"location_radius\\\":\\\"25\\\",\\\"company\\\":\\\"\\\"}\",\"index\":1,\"errors\":[[\"time_range\",\"This value is not allowed\"],[\"job_type\",\"This value is not allowed\"],[\"experience_level\",\"This value is not allowed\"],[\"remote\",\"This value is not allowed\"],[\"location_radius\",\"This value is not allowed\"]]}", "category": "software_engineer", "platform": "linkedin", "input_count": 5}, "data_scientist": {"error": "HTTP 400: {\"error\":\"Invalid input provided\",\"code\":\"validation_error\",\"type\":\"validation\",\"line\":\"{\\\"keyword\\\":\\\"data scientist\\\",\\\"location\\\":\\\"United States\\\",\\\"country\\\":\\\"US\\\",\\\"time_range\\\":\\\"r604800\\\",\\\"job_type\\\":\\\"F\\\",\\\"experience_level\\\":\\\"2\\\",\\\"remote\\\":\\\"false\\\",\\\"location_radius\\\":\\\"25\\\",\\\"company\\\":\\\"\\\"}\",\"index\":1,\"errors\":[[\"time_range\",\"This value is not allowed\"],[\"job_type\",\"This value is not allowed\"],[\"experience_level\",\"This value is not allowed\"],[\"remote\",\"This value is not allowed\"],[\"location_radius\",\"This value is not allowed\"]]}", "category": "data_scientist", "platform": "linkedin", "input_count": 3}, "data_analyst": {"error": "HTTP 400: {\"error\":\"Invalid input provided\",\"code\":\"validation_error\",\"type\":\"validation\",\"line\":\"{\\\"keyword\\\":\\\"data analyst\\\",\\\"location\\\":\\\"United States\\\",\\\"country\\\":\\\"US\\\",\\\"time_range\\\":\\\"r604800\\\",\\\"job_type\\\":\\\"F\\\",\\\"experience_level\\\":\\\"2\\\",\\\"remote\\\":\\\"false\\\",\\\"location_radius\\\":\\\"25\\\",\\\"company\\\":\\\"\\\"}\",\"index\":1,\"errors\":[[\"time_range\",\"This value is not allowed\"],[\"job_type\",\"This value is not allowed\"],[\"experience_level\",\"This value is not allowed\"],[\"remote\",\"This value is not allowed\"],[\"location_radius\",\"This value is not allowed\"]]}", "category": "data_analyst", "platform": "linkedin", "input_count": 2}, "product_manager": {"error": "HTTP 400: {\"error\":\"Invalid input provided\",\"code\":\"validation_error\",\"type\":\"validation\",\"line\":\"{\\\"keyword\\\":\\\"product manager\\\",\\\"location\\\":\\\"United States\\\",\\\"country\\\":\\\"US\\\",\\\"time_range\\\":\\\"r604800\\\",\\\"job_type\\\":\\\"F\\\",\\\"experience_level\\\":\\\"2\\\",\\\"remote\\\":\\\"false\\\",\\\"location_radius\\\":\\\"25\\\",\\\"company\\\":\\\"\\\"}\",\"index\":1,\"errors\":[[\"time_range\",\"This value is not allowed\"],[\"job_type\",\"This value is not allowed\"],[\"experience_level\",\"This value is not allowed\"],[\"remote\",\"This value is not allowed\"],[\"location_radius\",\"This value is not allowed\"]]}", "category": "product_manager", "platform": "linkedin", "input_count": 3}, "project_manager": {"error": "HTTP 400: {\"error\":\"Invalid input provided\",\"code\":\"validation_error\",\"type\":\"validation\",\"line\":\"{\\\"keyword\\\":\\\"project manager\\\",\\\"location\\\":\\\"United States\\\",\\\"country\\\":\\\"US\\\",\\\"time_range\\\":\\\"r604800\\\",\\\"job_type\\\":\\\"F\\\",\\\"experience_level\\\":\\\"2\\\",\\\"remote\\\":\\\"false\\\",\\\"location_radius\\\":\\\"25\\\",\\\"company\\\":\\\"\\\"}\",\"index\":1,\"errors\":[[\"time_range\",\"This value is not allowed\"],[\"job_type\",\"This value is not allowed\"],[\"experience_level\",\"This value is not allowed\"],[\"remote\",\"This value is not allowed\"],[\"location_radius\",\"This value is not allowed\"]]}", "category": "project_manager", "platform": "linkedin", "input_count": 2}, "business_analyst": {"error": "HTTP 400: {\"error\":\"Invalid input provided\",\"code\":\"validation_error\",\"type\":\"validation\",\"line\":\"{\\\"keyword\\\":\\\"business analyst\\\",\\\"location\\\":\\\"United States\\\",\\\"country\\\":\\\"US\\\",\\\"time_range\\\":\\\"r604800\\\",\\\"job_type\\\":\\\"F\\\",\\\"experience_level\\\":\\\"2\\\",\\\"remote\\\":\\\"false\\\",\\\"location_radius\\\":\\\"25\\\",\\\"company\\\":\\\"\\\"}\",\"index\":1,\"errors\":[[\"time_range\",\"This value is not allowed\"],[\"job_type\",\"This value is not allowed\"],[\"experience_level\",\"This value is not allowed\"],[\"remote\",\"This value is not allowed\"],[\"location_radius\",\"This value is not allowed\"]]}", "category": "business_analyst", "platform": "linkedin", "input_count": 2}, "marketing": {"error": "HTTP 400: {\"error\":\"Invalid input provided\",\"code\":\"validation_error\",\"type\":\"validation\",\"line\":\"{\\\"keyword\\\":\\\"marketing coordinator\\\",\\\"location\\\":\\\"United States\\\",\\\"country\\\":\\\"US\\\",\\\"time_range\\\":\\\"r604800\\\",\\\"job_type\\\":\\\"F\\\",\\\"experience_level\\\":\\\"2\\\",\\\"remote\\\":\\\"false\\\",\\\"location_radius\\\":\\\"25\\\",\\\"company\\\":\\\"\\\"}\",\"index\":1,\"errors\":[[\"time_range\",\"This value is not allowed\"],[\"job_type\",\"This value is not allowed\"],[\"experience_level\",\"This value is not allowed\"],[\"remote\",\"This value is not allowed\"],[\"location_radius\",\"This value is not allowed\"]]}", "category": "marketing", "platform": "linkedin", "input_count": 3}, "sales": {"error": "HTTP 400: {\"error\":\"Invalid input provided\",\"code\":\"validation_error\",\"type\":\"validation\",\"line\":\"{\\\"keyword\\\":\\\"sales representative\\\",\\\"location\\\":\\\"United States\\\",\\\"country\\\":\\\"US\\\",\\\"time_range\\\":\\\"r604800\\\",\\\"job_type\\\":\\\"F\\\",\\\"experience_level\\\":\\\"2\\\",\\\"remote\\\":\\\"false\\\",\\\"location_radius\\\":\\\"25\\\",\\\"company\\\":\\\"\\\"}\",\"index\":1,\"errors\":[[\"time_range\",\"This value is not allowed\"],[\"job_type\",\"This value is not allowed\"],[\"experience_level\",\"This value is not allowed\"],[\"remote\",\"This value is not allowed\"],[\"location_radius\",\"This value is not allowed\"]]}", "category": "sales", "platform": "linkedin", "input_count": 2}, "customer_success": {"error": "HTTP 400: {\"error\":\"Invalid input provided\",\"code\":\"validation_error\",\"type\":\"validation\",\"line\":\"{\\\"keyword\\\":\\\"customer success\\\",\\\"location\\\":\\\"United States\\\",\\\"country\\\":\\\"US\\\",\\\"time_range\\\":\\\"r604800\\\",\\\"job_type\\\":\\\"F\\\",\\\"experience_level\\\":\\\"2\\\",\\\"remote\\\":\\\"false\\\",\\\"location_radius\\\":\\\"25\\\",\\\"company\\\":\\\"\\\"}\",\"index\":1,\"errors\":[[\"time_range\",\"This value is not allowed\"],[\"job_type\",\"This value is not allowed\"],[\"experience_level\",\"This value is not allowed\"],[\"remote\",\"This value is not allowed\"],[\"location_radius\",\"This value is not allowed\"]]}", "category": "customer_success", "platform": "linkedin", "input_count": 2}, "operations": {"error": "HTTP 400: {\"error\":\"Invalid input provided\",\"code\":\"validation_error\",\"type\":\"validation\",\"line\":\"{\\\"keyword\\\":\\\"operations coordinator\\\",\\\"location\\\":\\\"United States\\\",\\\"country\\\":\\\"US\\\",\\\"time_range\\\":\\\"r604800\\\",\\\"job_type\\\":\\\"F\\\",\\\"experience_level\\\":\\\"2\\\",\\\"remote\\\":\\\"false\\\",\\\"location_radius\\\":\\\"25\\\",\\\"company\\\":\\\"\\\"}\",\"index\":1,\"errors\":[[\"time_range\",\"This value is not allowed\"],[\"job_type\",\"This value is not allowed\"],[\"experience_level\",\"This value is not allowed\"],[\"remote\",\"This value is not allowed\"],[\"location_radius\",\"This value is not allowed\"]]}", "category": "operations", "platform": "linkedin", "input_count": 2}}}}