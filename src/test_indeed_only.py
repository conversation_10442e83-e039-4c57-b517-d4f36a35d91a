#!/usr/bin/env python3
"""
Test script for Indeed-only processing without LLM.

This script tests the new --indeed-only flag functionality.
"""

import sys
import os
import pandas as pd
from pathlib import Path

# Add src to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.data_processor import DataProcessor

def test_indeed_only():
    """Test Indeed-only processing without LLM."""
    
    print("🧪 Testing Indeed-Only Processing (No LLM)")
    print("=" * 60)
    
    # Initialize data processor in indeed-only mode
    try:
        processor = DataProcessor(
            indeed_only=True,
            use_llm_filter=False  # Disable LLM for basic processing
        )
        print("✅ Data processor initialized successfully in Indeed-only mode")
        print("🔧 Enhanced preprocessing enabled (same as LinkedIn)")
    except Exception as e:
        print(f"❌ Error initializing data processor: {e}")
        return False
    
    # Check if Indeed data exists
    indeed_folder = "data/raw/indeed_data"
    if not os.path.exists(indeed_folder):
        print(f"❌ Indeed data folder not found: {indeed_folder}")
        return False
    
    csv_files = [f for f in os.listdir(indeed_folder) if f.endswith('.csv')]
    if not csv_files:
        print(f"❌ No CSV files found in {indeed_folder}")
        return False
    
    print(f"📂 Found {len(csv_files)} Indeed CSV files")
    
    # Test loading Indeed data
    try:
        indeed_raw = processor.load_indeed_data()
        print(f"📊 Loaded {len(indeed_raw)} Indeed jobs")
        
        if indeed_raw.empty:
            print("❌ No Indeed data loaded")
            return False
        
        # Show sample data
        print(f"\n📋 Sample Indeed job titles:")
        for i, title in enumerate(indeed_raw['job_title'].head(5)):
            print(f"  {i+1}. {title}")
        
        # Show available columns
        print(f"\n📋 Available columns: {list(indeed_raw.columns)}")
        
    except Exception as e:
        print(f"❌ Error loading Indeed data: {e}")
        return False
    
    # Test processing Indeed data
    try:
        print(f"\n⚙️ Processing {len(indeed_raw)} Indeed jobs...")
        indeed_processed = processor.process_indeed_jobs(indeed_raw)
        
        print(f"📊 Processed {len(indeed_processed)} Indeed jobs")
        
        if indeed_processed.empty:
            print("❌ No Indeed jobs processed")
            return False
        
        # Show processing results
        print(f"\n📊 Processing Results:")
        print(f"  Input jobs: {len(indeed_raw)}")
        print(f"  Output jobs: {len(indeed_processed)}")
        print(f"  Reduction: {len(indeed_raw) - len(indeed_processed)} jobs filtered out")
        
        # Show sample processed data
        print(f"\n📋 Sample processed job titles:")
        for i, title in enumerate(indeed_processed['job_title'].head(5)):
            print(f"  {i+1}. {title}")
        
        # Show work mode distribution (now with regex-based extraction)
        if 'work_mode' in indeed_processed.columns:
            work_mode_dist = indeed_processed['work_mode'].value_counts()
            print(f"\n🏠 Work Mode Distribution (Regex-based):")
            for mode, count in work_mode_dist.items():
                print(f"  {mode}: {count}")
        
        # Show authorization distribution (now with regex-based extraction)
        if 'authorization' in indeed_processed.columns:
            auth_dist = indeed_processed['authorization'].value_counts()
            print(f"\n🛂 Authorization Distribution (Regex-based):")
            for auth, count in auth_dist.items():
                print(f"  {auth}: {count}")
        
        # Show qualifications processing results
        if 'qualifications' in indeed_processed.columns:
            qual_count = indeed_processed['qualifications'].notna().sum()
            print(f"\n📝 Qualifications Processing:")
            print(f"  Jobs with processed qualifications: {qual_count}/{len(indeed_processed)}")
            
            # Show sample qualifications
            sample_quals = indeed_processed['qualifications'].dropna().head(3)
            if not sample_quals.empty:
                print(f"  Sample qualifications:")
                for i, qual in enumerate(sample_quals):
                    print(f"    {i+1}. {qual[:100]}...")
        
        # Save results for inspection
        output_file = "test_indeed_only_results.csv"
        indeed_processed.to_csv(output_file, index=False)
        print(f"\n💾 Results saved to: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error processing Indeed data: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Testing Indeed-Only Processing")
    print("=" * 60)
    
    success = test_indeed_only()
    
    if success:
        print("\n🎉 Indeed-only test completed successfully!")
    else:
        print("\n❌ Indeed-only test failed!")
    
    sys.exit(0 if success else 1)
