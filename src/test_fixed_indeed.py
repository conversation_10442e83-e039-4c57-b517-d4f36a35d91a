#!/usr/bin/env python3
"""
Test script for fixed Indeed processing.

This script tests the fixed Indeed processing with:
- Job description processing (like LinkedIn)
- Company name standardization (Amazon.com services.llc -> Amazon)
- No work mode/authorization extraction (LLM only)
"""

import sys
import os
import pandas as pd
from pathlib import Path

# Add src to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.data_processor import DataProcessor

def test_fixed_indeed_processing():
    """Test fixed Indeed processing with all requested changes."""
    
    print("🧪 Testing Fixed Indeed Processing")
    print("=" * 60)
    print("🔧 Changes made:")
    print("  ✅ Job description processing (like LinkedIn)")
    print("  ✅ Company name standardization (Amazon.com services.llc -> Amazon)")
    print("  ✅ Removed work mode/authorization extraction (LLM only)")
    print("  ✅ Fixed NaN handling in job filtering")
    print()
    
    # Initialize data processor in indeed-only mode
    try:
        processor = DataProcessor(
            indeed_only=True,
            use_llm_filter=False  # Disable LLM for basic processing
        )
        print("✅ Data processor initialized successfully")
    except Exception as e:
        print(f"❌ Error initializing data processor: {e}")
        return False
    
    # Check if Indeed data exists
    indeed_folder = "data/raw/indeed_data"
    if not os.path.exists(indeed_folder):
        print(f"❌ Indeed data folder not found: {indeed_folder}")
        return False
    
    csv_files = [f for f in os.listdir(indeed_folder) if f.endswith('.csv')]
    if not csv_files:
        print(f"❌ No CSV files found in {indeed_folder}")
        return False
    
    print(f"📂 Found {len(csv_files)} Indeed CSV files")
    
    # Test loading Indeed data
    try:
        indeed_raw = processor.load_indeed_data()
        print(f"📊 Loaded {len(indeed_raw)} Indeed jobs")
        
        if indeed_raw.empty:
            print("❌ No Indeed data loaded")
            return False
        
        # Show sample raw data
        print(f"\n📋 Sample raw Indeed job titles:")
        for i, title in enumerate(indeed_raw['job_title'].head(5)):
            print(f"  {i+1}. {title}")
        
        # Show sample raw company names
        print(f"\n🏢 Sample raw company names:")
        for i, company in enumerate(indeed_raw['company_name'].head(5)):
            print(f"  {i+1}. {company}")
        
    except Exception as e:
        print(f"❌ Error loading Indeed data: {e}")
        return False
    
    # Test fixed processing
    try:
        print(f"\n⚙️ Processing {len(indeed_raw)} Indeed jobs with fixes...")
        indeed_processed = processor.process_indeed_jobs(indeed_raw)
        
        print(f"📊 Fixed processing completed: {len(indeed_processed)} Indeed jobs")
        
        if indeed_processed.empty:
            print("❌ No Indeed jobs processed")
            return False
        
        # Show processing results
        print(f"\n📊 Processing Results:")
        print(f"  Input jobs: {len(indeed_raw)}")
        print(f"  Output jobs: {len(indeed_processed)}")
        print(f"  Reduction: {len(indeed_raw) - len(indeed_processed)} jobs filtered out")
        print(f"  Filtering rate: {((len(indeed_raw) - len(indeed_processed)) / len(indeed_raw) * 100):.1f}%")
        
        # Show sample processed data
        print(f"\n📋 Sample processed job titles:")
        for i, title in enumerate(indeed_processed['job_title'].head(5)):
            print(f"  {i+1}. {title}")
        
        # Show company name standardization results
        print(f"\n🏢 Company Name Standardization Results:")
        amazon_companies = indeed_processed[indeed_processed['company_name'].str.contains('Amazon', case=False, na=False)]
        if not amazon_companies.empty:
            print(f"  Amazon companies found: {len(amazon_companies)}")
            print(f"  Sample Amazon companies:")
            for i, company in enumerate(amazon_companies['company_name'].head(3)):
                print(f"    {i+1}. {company}")
        else:
            print("  No Amazon companies found in sample")
        
        # Show job description processing results
        if 'qualifications' in indeed_processed.columns:
            qual_count = indeed_processed['qualifications'].notna().sum()
            print(f"\n📝 Job Description Processing (NLTK):")
            print(f"  Jobs with processed qualifications: {qual_count}/{len(indeed_processed)}")
            print(f"  Processing rate: {(qual_count / len(indeed_processed) * 100):.1f}%")
            
            # Show sample qualifications
            sample_quals = indeed_processed['qualifications'].dropna().head(3)
            if not sample_quals.empty:
                print(f"  Sample qualifications:")
                for i, qual in enumerate(sample_quals):
                    print(f"    {i+1}. {qual[:100]}...")
        
        # Show work mode and authorization (should be "Not Specified")
        if 'work_mode' in indeed_processed.columns:
            work_mode_dist = indeed_processed['work_mode'].value_counts()
            print(f"\n🏠 Work Mode (LLM Only):")
            for mode, count in work_mode_dist.items():
                print(f"  {mode}: {count}")
        
        if 'authorization' in indeed_processed.columns:
            auth_dist = indeed_processed['authorization'].value_counts()
            print(f"\n🛂 Authorization (LLM Only):")
            for auth, count in auth_dist.items():
                print(f"  {auth}: {count}")
        
        # Show job filtering results
        print(f"\n🔍 Job Filtering Results:")
        print(f"  Professional jobs kept: {len(indeed_processed)}")
        print(f"  Non-professional jobs removed: {len(indeed_raw) - len(indeed_processed)}")
        
        # Show duplicate removal results
        if 'jobid' in indeed_processed.columns:
            unique_jobs = indeed_processed['jobid'].nunique()
            print(f"  Unique jobs: {unique_jobs}")
            print(f"  Duplicates removed: {len(indeed_processed) - unique_jobs}")
        
        # Show sample processed job with all details
        print(f"\n📋 Sample Processed Job (Full Details):")
        if not indeed_processed.empty:
            sample_job = indeed_processed.iloc[0]
            print(f"  Title: {sample_job['job_title']}")
            print(f"  Company: {sample_job['company_name']}")
            print(f"  Location: {sample_job['job_location']}")
            print(f"  Work Mode: {sample_job['work_mode']}")
            print(f"  Authorization: {sample_job['authorization']}")
            print(f"  Qualifications: {sample_job['qualifications'][:100]}...")
        
        # Save results for inspection
        output_file = "test_fixed_indeed_results.csv"
        indeed_processed.to_csv(output_file, index=False)
        print(f"\n💾 Fixed results saved to: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in fixed processing: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_company_standardization():
    """Show company name standardization examples."""
    
    print("\n🏢 Company Name Standardization Examples")
    print("=" * 50)
    
    examples = [
        ("amazon.com services.llc", "Amazon"),
        ("Amazon Web Services", "Amazon"),
        ("amazon.com", "Amazon"),
        ("Google LLC", "Google"),
        ("Microsoft Corporation", "Microsoft"),
        ("Apple Inc.", "Apple"),
        ("Meta Platforms Inc.", "Meta"),
        ("Facebook", "Meta"),
        ("Netflix Inc.", "Netflix"),
        ("Tesla Inc.", "Tesla")
    ]
    
    for old_name, new_name in examples:
        print(f"  '{old_name}' → '{new_name}'")

if __name__ == "__main__":
    print("🧪 Testing Fixed Indeed Processing")
    print("=" * 60)
    
    # Show company standardization examples
    show_company_standardization()
    
    # Run the test
    success = test_fixed_indeed_processing()
    
    if success:
        print("\n🎉 Fixed Indeed processing test completed successfully!")
        print("✅ All requested changes have been implemented!")
    else:
        print("\n❌ Fixed Indeed processing test failed!")
    
    sys.exit(0 if success else 1)
