#!/usr/bin/env python3
"""
Test script for full LinkedIn batch processing workflow.

This script processes the entire LinkedIn dataset using the new batch workflow.
"""

import sys
import os
import pandas as pd
import logging
from pathlib import Path

# Add src to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.data_processor import DataProcessor

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_full_linkedin_batch():
    """Test the full LinkedIn batch processing workflow."""
    
    print("🚀 Testing Full LinkedIn Batch Processing Workflow")
    print("=" * 60)
    
    # Initialize data processor
    try:
        processor = DataProcessor()
        print("✅ Data processor initialized successfully")
    except Exception as e:
        print(f"❌ Error initializing data processor: {e}")
        return False
    
    # Load LinkedIn data
    data_file = Path("data/raw/linkedin_jobs.csv")
    if not data_file.exists():
        print(f"❌ LinkedIn data file not found: {data_file}")
        return False
    
    try:
        df = pd.read_csv(data_file)
        print(f"📊 Loaded {len(df)} LinkedIn jobs from {data_file}")
        
        # Process LinkedIn jobs using the new batch workflow
        print("\n🤖 Starting LinkedIn batch processing...")
        result_df = processor.process_linkedin_jobs(df)
        
        print(f"\n📊 Batch Processing Results:")
        print(f"Total jobs processed: {len(result_df)}")
        
        # Count decisions
        kept_jobs = len(result_df[result_df['job_decision'] == 'KEEP'])
        rejected_jobs = len(result_df[result_df['job_decision'] == 'REJECT'])
        error_jobs = len(result_df[result_df['job_decision'] == 'ERROR'])
        
        print(f"Jobs KEPT: {kept_jobs}")
        print(f"Jobs REJECTED: {rejected_jobs}")
        print(f"Jobs with ERRORS: {error_jobs}")
        
        # Show work mode distribution
        if 'llm_work_mode' in result_df.columns:
            work_mode_dist = result_df['llm_work_mode'].value_counts()
            print(f"\n🏠 Work Mode Distribution:")
            for mode, count in work_mode_dist.items():
                print(f"  {mode}: {count}")
        
        # Show authorization distribution
        if 'llm_authorization' in result_df.columns:
            auth_dist = result_df['llm_authorization'].value_counts()
            print(f"\n🛂 Authorization Distribution:")
            for auth, count in auth_dist.items():
                print(f"  {auth}: {count}")
        
        # Show quality score statistics
        if 'job_quality_score' in result_df.columns:
            quality_scores = result_df['job_quality_score'].dropna()
            if len(quality_scores) > 0:
                print(f"\n📈 Quality Score Statistics:")
                print(f"  Average: {quality_scores.mean():.1f}")
                print(f"  Min: {quality_scores.min()}")
                print(f"  Max: {quality_scores.max()}")
        
        # Save results
        output_file = "linkedin_batch_processing_results.csv"
        result_df.to_csv(output_file, index=False)
        print(f"\n💾 Results saved to: {output_file}")
        
        print("\n🎉 Full LinkedIn batch processing completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error in batch processing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_full_linkedin_batch()
    sys.exit(0 if success else 1)
