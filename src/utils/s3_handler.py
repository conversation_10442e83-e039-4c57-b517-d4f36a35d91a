"""
S3 Handler Module

Handles all S3 operations including downloading and uploading data.
"""

import boto3
import os
import datetime
from typing import List, Optional

class S3Handler:
    """
    Handles S3 operations for job data.
    """
    
    def __init__(self, 
                 aws_access_key: str = "********************",
                 aws_secret_key: str = "n+6MxKRIu03f4kN4/EvZc6fy5ZAB98CKKd/wLWzW",
                 bucket_name: str = "bright-data-api"):
        """
        Initialize S3 handler with credentials.
        
        Args:
            aws_access_key: AWS access key
            aws_secret_key: AWS secret key
            bucket_name: S3 bucket name
        """
        self.s3_client = boto3.client(
            's3',
            aws_access_key_id=aws_access_key,
            aws_secret_access_key=aws_secret_key
        )
        self.bucket_name = bucket_name
        
        # Create local directories if they don't exist
        self.local_dirs = {
            'indeed': 'data/raw/indeed_data',
            'linkedin': 'data/raw/linkedin_data'
        }
        for directory in self.local_dirs.values():
            os.makedirs(directory, exist_ok=True)
    
    def get_today_s3_prefix(self, platform: str) -> str:
        """Get the S3 prefix for today's data."""
        # today = datetime.datetime.now().strftime("%Y-%m-%d")
        today   = "2025-09-19"
        return f"{today}/{platform.lower()}/"
    
    def list_s3_files(self, prefix: str) -> List[str]:
        """List all CSV files in the S3 bucket with given prefix."""
        try:
            paginator = self.s3_client.get_paginator('list_objects_v2')
            files = []
            
            for page in paginator.paginate(Bucket=self.bucket_name, Prefix=prefix):
                if 'Contents' in page:
                    files.extend([
                        obj['Key'] for obj in page['Contents']
                        if obj['Key'].endswith('.csv')
                    ])
            
            return files
        except Exception as e:
            print(f"❌ Error listing S3 files: {str(e)}")
            return []
    
    def download_file(self, s3_key: str, local_dir: str) -> bool:
        """Download a single file from S3."""
        try:
            # Generate local filename from S3 key
            filename = os.path.basename(s3_key)
            local_path = os.path.join(local_dir, filename)
            
            # Download file
            print(f"📥 Downloading {filename}...")
            self.s3_client.download_file(self.bucket_name, s3_key, local_path)
            print(f"✅ Successfully downloaded {filename}")
            return True
            
        except Exception as e:
            print(f"❌ Error downloading {s3_key}: {str(e)}")
            return False
    
    def fetch_platform_data(self, platform: str) -> int:
        """Download all files for a specific platform."""
        print(f"\n🔄 Fetching {platform.upper()} data...")
        
        # Get S3 prefix for today
        prefix = self.get_today_s3_prefix(platform)
        print(f"📂 Looking in S3 path: {prefix}")
        
        # List all CSV files
        s3_files = self.list_s3_files(prefix)
        
        if not s3_files:
            print(f"⚠️  No CSV files found for {platform} today")
            return 0
        
        print(f"📊 Found {len(s3_files)} CSV files")
        
        # Download each file
        local_dir = self.local_dirs[platform]
        downloaded_count = 0
        
        for s3_key in s3_files:
            if self.download_file(s3_key, local_dir):
                downloaded_count += 1
        
        print(f"✅ Downloaded {downloaded_count}/{len(s3_files)} files for {platform}")
        return downloaded_count
    
    def fetch_today_data(self) -> dict:
        """Fetch today's data for both platforms."""
        print("🚀 FETCHING TODAY'S JOB DATA")
        print("=" * 50)
        
        results = {}
        
        # Download Indeed data
        results['indeed'] = self.fetch_platform_data('indeed')
        
        # Download LinkedIn data
        results['linkedin'] = self.fetch_platform_data('linkedin')
        
        total_downloaded = results['indeed'] + results['linkedin']
        print(f"\n🎉 DATA FETCHING COMPLETE!")
        print(f"📊 Total files downloaded: {total_downloaded}")
        
        return results
