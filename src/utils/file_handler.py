"""
File Handler Module

Handles file operations including CSV processing, Excel generation, and Google Drive uploads.
"""

import os
import pandas as pd
from datetime import datetime
from pathlib import Path
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload
from google.oauth2 import service_account

class FileHandler:
    """
    Handles file operations for job data.
    """
    
    def __init__(self, output_folder: str = "data/exports/excel"):
        """
        Initialize file handler.
        
        Args:
            output_folder: Path to output folder for Excel files
        """
        self.output_folder = output_folder
        Path(self.output_folder).mkdir(parents=True, exist_ok=True)
    
    def beautify_worksheet(self, worksheet, df):
        """Apply beautiful formatting to the worksheet."""
        # Define styles
        header_font = Font(bold=True, color="FFFFFF", size=12, name="Arial")
        header_fill = PatternFill(start_color="20705B", end_color="20705B", fill_type="solid")
        data_font = Font(size=10, name="Arial")
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # Get the maximum row and column
        max_row = worksheet.max_row
        max_col = worksheet.max_column
        
        # Get column headers to identify specific columns
        headers = [worksheet.cell(row=1, column=col).value for col in range(1, max_col + 1)]
        
        # Format headers (first row) - apply color to entire header row
        for col in range(1, max_col + 1):
            cell = worksheet.cell(row=1, column=col)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal='left', vertical='center')
            cell.border = border
        
        # Apply header color to the entire first row
        worksheet.row_dimensions[1].fill = header_fill
        
        # Set fixed row height for all data rows to prevent excessive height
        for row in range(2, max_row + 1):
            worksheet.row_dimensions[row].height = 30  # Fixed height in points
        
        # Format data rows with specific handling for different column types
        for row in range(2, max_row + 1):
            for col in range(1, max_col + 1):
                cell = worksheet.cell(row=row, column=col)
                cell.font = data_font
                cell.border = border
                
                # Get column header to determine formatting
                col_header = headers[col - 1] if col <= len(headers) else ""
                
                # Special handling for apply_link column - allow horizontal overflow
                if col_header and 'apply_link' in col_header.lower():
                    cell.alignment = Alignment(horizontal='left', vertical='top', wrap_text=False)
                # Special handling for job description - limit wrapping
                elif col_header and any(keyword in col_header.lower() for keyword in ['description', 'jd', 'job_description']):
                    cell.alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
                else:
                    # Default formatting for other columns
                    cell.alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
        
        # Set column widths based on header text length + 20
        for col in range(1, max_col + 1):
            column_letter = worksheet.cell(row=1, column=col).column_letter
            col_header = headers[col - 1] if col <= len(headers) else ""
            
            # Calculate width as header text length + 10 (reduced padding)
            header_length = len(str(col_header)) if col_header else 10
            column_width = header_length + 10
            
            # Special handling for apply_link - double the width
            if col_header and 'apply_link' in col_header.lower():
                # Double the width for apply_link columns
                column_width = column_width * 2
            
            worksheet.column_dimensions[column_letter].width = column_width
        
        # Freeze the first row
        worksheet.freeze_panes = "A2"
    
    def create_excel_with_sheets(self, df, output_path, sheet_name_prefix="Jobs"):
        """Create Excel file with multiple sheets."""
        if df.empty:
            print("❌ No data to process")
            return False
        
        try:
            # Create output directory if it doesn't exist
            output_dir = os.path.dirname(output_path)
            if output_dir:
                Path(output_dir).mkdir(parents=True, exist_ok=True)
            
            # Create Excel writer
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                
                # 1. All Jobs sheet
                print(f"📊 Creating 'All Jobs' sheet with {len(df)} rows...")
                df.to_excel(writer, sheet_name='All Jobs', index=False)
                
                # Beautify the All Jobs sheet
                workbook = writer.book
                worksheet = writer.sheets['All Jobs']
                self.beautify_worksheet(worksheet, df)
                
                # 2. Keyword-specific sheets
                if 'keyword' in df.columns:
                    # Get unique keywords (excluding None/NaN) and normalize them
                    keywords = df['keyword'].dropna().unique()
                    print(f"🔍 Found {len(keywords)} unique keywords")
                    
                    # Create a mapping of normalized keywords to handle duplicates
                    keyword_mapping = {}
                    for keyword in keywords:
                        if pd.isna(keyword) or keyword == "":
                            continue
                        
                        # Normalize keyword (lowercase and strip whitespace)
                        normalized_keyword = str(keyword).strip().lower()
                        
                        if normalized_keyword not in keyword_mapping:
                            keyword_mapping[normalized_keyword] = []
                        keyword_mapping[normalized_keyword].append(keyword)
                    
                    print(f"🔍 After normalization: {len(keyword_mapping)} unique keyword groups")
                    
                    # Create sheets for each normalized keyword group
                    for normalized_keyword, original_keywords in keyword_mapping.items():
                        # Filter data for all variations of this keyword
                        keyword_mask = df['keyword'].isin(original_keywords)
                        keyword_df = df[keyword_mask].copy()
                        
                        if not keyword_df.empty:
                            # Use the first original keyword as the sheet name
                            sheet_name = str(original_keywords[0])[:31]  # Excel sheet name limit
                            sheet_name = sheet_name.replace('/', '_').replace('\\', '_').replace('*', '_').replace('?', '_').replace('[', '_').replace(']', '_')
                            
                            print(f"📋 Creating '{sheet_name}' sheet with {len(keyword_df)} jobs (combining: {', '.join(original_keywords)})")
                            keyword_df.to_excel(writer, sheet_name=sheet_name, index=False)
                            
                            # Beautify the keyword sheet
                            keyword_worksheet = writer.sheets[sheet_name]
                            self.beautify_worksheet(keyword_worksheet, keyword_df)
                        else:
                            print(f"⚠️ No jobs found for keyword group: {original_keywords}")
                else:
                    print("⚠️ No 'keyword' column found in data")
            
            print(f"✅ Excel file created successfully: {output_path}")
            return True
            
        except Exception as e:
            print(f"❌ Error creating Excel file: {str(e)}")
            return False
    
    def process_csv_to_excel(self, csv_path, output_dir=None):
        """Process a CSV file and create Excel sheets."""
        if output_dir is None:
            output_dir = self.output_folder
        
        print("🚀 Starting Excel sheet creation...")
        print("=" * 50)
        
        # Load the data
        try:
            df = pd.read_csv(csv_path)
            print(f"✅ Loaded {len(df)} jobs from {csv_path}")
        except Exception as e:
            print(f"❌ Error loading CSV: {str(e)}")
            return None
        
        # Show data overview
        print(f"\n📊 Data Overview:")
        print(f"  Total jobs: {len(df)}")
        if 'keyword' in df.columns:
            unique_keywords = df['keyword'].dropna().nunique()
            print(f"  Unique keywords: {unique_keywords}")
        if 'source' in df.columns:
            sources = df['source'].unique()
            print(f"  Sources: {', '.join(sources)}")
        
        # Generate output filename
        output_filename = f"job_sheets_final.xlsx"
        output_path = os.path.join(output_dir, output_filename)
        
        # Create Excel file with sheets
        success = self.create_excel_with_sheets(df, output_path)
        
        if success:
            print(f"\n✅ Excel file created: {output_path}")
            print(f"📁 File size: {os.path.getsize(output_path) / 1024:.1f} KB")
            
            # Show what sheets were created
            print(f"\n📋 Sheets created:")
            print(f"  - All Jobs ({len(df)} rows)")
            if 'keyword' in df.columns:
                keywords = df['keyword'].dropna().unique()
                for keyword in keywords:
                    if pd.notna(keyword) and keyword != "":
                        keyword_count = len(df[df['keyword'] == keyword])
                        print(f"  - {keyword} ({keyword_count} rows)")
            
            return output_path
        
        return None
    
    def upload_to_google_drive(self, excel_file_path, service_account_file="../lambda_function/service-account-key.json", folder_id="1dKVjmvDgelKZxi9BkYIbxU3p3Mg_dXFV"):
        """Upload Excel file to Google Drive."""
        try:
            # Google API scopes
            scopes = [
                'https://www.googleapis.com/auth/drive',
                'https://www.googleapis.com/auth/spreadsheets'
            ]
            
            # Check if Excel file exists
            if not os.path.exists(excel_file_path):
                print(f"❌ Excel file not found: {excel_file_path}")
                return None
            
            # Check if service account file exists
            if not os.path.exists(service_account_file):
                print(f"❌ Service account file not found: {service_account_file}")
                return None
            
            # Initialize Google Drive service
            credentials = service_account.Credentials.from_service_account_file(
                service_account_file, scopes=scopes
            )
            drive_service = build('drive', 'v3', credentials=credentials)
            
            # Generate filename with today's date
            today = datetime.now()
            formatted_date = today.strftime("%m%d")
            filename = f"[Daily Jobs] {formatted_date} Daily Jobs for NG.xlsx"
            
            # File metadata
            file_metadata = {
                'name': filename,
                'parents': [folder_id]
            }
            
            # Create media upload
            media = MediaFileUpload(
                excel_file_path,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            
            # Upload file
            print(f"📤 Uploading file: {filename}")
            file = drive_service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id,name,webViewLink'
            ).execute()
            
            # Set file permissions (anyone with link can view)
            drive_service.permissions().create(
                fileId=file['id'],
                body={
                    'type': 'anyone',
                    'role': 'reader'
                }
            ).execute()
            
            print(f"✅ Successfully uploaded: {filename}")
            print(f"📁 File ID: {file['id']}")
            print(f"🔗 View link: {file.get('webViewLink', 'N/A')}")
            
            return file['id']
            
        except Exception as e:
            print(f"❌ Error uploading to Google Drive: {str(e)}")
            return None
