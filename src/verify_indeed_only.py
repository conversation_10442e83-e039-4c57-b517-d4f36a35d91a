#!/usr/bin/env python3
"""
Verification script to check that --indeed-only flag is properly integrated.
"""

import sys
import os
import argparse

# Add src to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_argument_parser():
    """Test that the argument parser includes the indeed-only flag."""
    
    print("🧪 Testing Argument Parser Integration")
    print("=" * 50)
    
    # Import the main function to test argument parsing
    try:
        from main import main
        print("✅ Successfully imported main function")
    except Exception as e:
        print(f"❌ Error importing main function: {e}")
        return False
    
    # Test argument parser directly
    try:
        parser = argparse.ArgumentParser(description="Job Scraper Application")
        parser.add_argument(
            "--action", 
            choices=["scrape", "fetch", "process", "excel", "upload", "all"],
            default="all",
            help="Action to perform (default: all)"
        )
        parser.add_argument(
            "--linkedin-only",
            action="store_true",
            default=False,
            help="Process only LinkedIn jobs (skip Indeed)"
        )
        parser.add_argument(
            "--indeed-only",
            action="store_true",
            default=False,
            help="Process only Indeed jobs (skip LinkedIn)"
        )
        
        # Test parsing with indeed-only flag
        test_args = parser.parse_args(["--action", "process", "--indeed-only"])
        
        print(f"✅ Argument parsing successful")
        print(f"  Action: {test_args.action}")
        print(f"  LinkedIn only: {test_args.linkedin_only}")
        print(f"  Indeed only: {test_args.indeed_only}")
        
        if test_args.indeed_only:
            print("✅ --indeed-only flag is working correctly")
        else:
            print("❌ --indeed-only flag not working")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing argument parser: {e}")
        return False

def test_data_processor_initialization():
    """Test that DataProcessor can be initialized with indeed_only parameter."""
    
    print("\n🧪 Testing DataProcessor Initialization")
    print("=" * 50)
    
    try:
        from core.data_processor import DataProcessor
        
        # Test initialization with indeed_only=True
        processor = DataProcessor(
            indeed_only=True,
            use_llm_filter=False
        )
        
        print("✅ DataProcessor initialized successfully with indeed_only=True")
        print(f"  LinkedIn only: {processor.linkedin_only}")
        print(f"  Indeed only: {processor.indeed_only}")
        print(f"  Use LLM filter: {processor.use_llm_filter}")
        
        if processor.indeed_only:
            print("✅ indeed_only parameter is working correctly")
        else:
            print("❌ indeed_only parameter not working")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing DataProcessor initialization: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all verification tests."""
    
    print("🔍 Verifying Indeed-Only Integration")
    print("=" * 60)
    
    tests = [
        test_argument_parser,
        test_data_processor_initialization
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Indeed-only integration is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
