#!/usr/bin/env python3
"""
Test script for Indeed LLM processing pipeline.

This script tests the complete Indeed LLM processing pipeline including:
- Job filtering and scoring
- Work mode detection (using job_location)
- Authorization detection
- Job seniority extraction
- Qualifications extraction
"""

import sys
import os
import pandas as pd
from pathlib import Path

# Add the src directory to the Python path
sys.path.append(str(Path(__file__).parent))

from core.data_processor import DataProcessor

def test_indeed_llm_pipeline():
    """Test the complete Indeed LLM processing pipeline."""
    print("🧪 Testing Indeed LLM Processing Pipeline")
    print("=" * 60)
    
    try:
        # Initialize data processor
        print("🔧 Initializing data processor...")
        processor = DataProcessor(
            linkedin_folder="data/raw/linkedin_data",
            indeed_folder="data/raw/indeed_data",
            output_folder="data/processed/combined_data",
            extract_redirects=False,
            redirect_delay=2,
            linkedin_only=False,
            indeed_only=True,  # Test Indeed only
            use_llm_filter=True
        )
        
        print("✅ Data processor initialized successfully")
        
        # Load Indeed data
        print("\n📂 Loading Indeed data...")
        indeed_raw = processor.load_indeed_data()
        
        if indeed_raw.empty:
            print("❌ No Indeed data found!")
            return
        
        print(f"📊 Loaded {len(indeed_raw)} Indeed jobs")
        
        # Show sample data
        print("\n📋 Sample raw Indeed job titles:")
        for i, title in enumerate(indeed_raw['job_title'].head(5), 1):
            print(f"  {i}. {title}")
        
        print("\n🏢 Sample raw company names:")
        for i, company in enumerate(indeed_raw['company_name'].head(5), 1):
            print(f"  {i}. {company}")
        
        # Process Indeed jobs with LLM
        print("\n⚙️ Processing Indeed jobs with LLM...")
        indeed_processed = processor.process_indeed_jobs(indeed_raw)
        
        if indeed_processed.empty:
            print("❌ No jobs processed!")
            return
        
        print(f"📊 Processed {len(indeed_processed)} Indeed jobs")
        
        # Show processing results
        print("\n📊 Processing Results:")
        print(f"  Input jobs: {len(indeed_raw)}")
        print(f"  Output jobs: {len(indeed_processed)}")
        print(f"  Reduction: {len(indeed_raw) - len(indeed_processed)} jobs filtered out")
        print(f"  Filtering rate: {((len(indeed_raw) - len(indeed_processed)) / len(indeed_raw) * 100):.1f}%")
        
        # Show sample processed jobs
        print("\n📋 Sample processed job titles:")
        for i, title in enumerate(indeed_processed['job_title'].head(5), 1):
            print(f"  {i}. {title}")
        
        # Show LLM processing results
        if 'llm_work_mode' in indeed_processed.columns:
            work_mode_dist = indeed_processed['llm_work_mode'].value_counts()
            print(f"\n🏠 Work Mode Distribution (LLM):")
            for mode, count in work_mode_dist.items():
                print(f"  {mode}: {count}")
        
        if 'llm_authorization' in indeed_processed.columns:
            auth_dist = indeed_processed['llm_authorization'].value_counts()
            print(f"\n🛂 Authorization Distribution (LLM):")
            for auth, count in auth_dist.items():
                print(f"  {auth}: {count}")
        
        if 'llm_job_seniority' in indeed_processed.columns:
            seniority_dist = indeed_processed['llm_job_seniority'].value_counts()
            print(f"\n👔 Job Seniority Distribution (LLM):")
            for seniority, count in seniority_dist.items():
                print(f"  {seniority}: {count}")
        
        if 'job_quality_score' in indeed_processed.columns:
            print(f"\n⭐ Quality Score Statistics:")
            print(f"  Average: {indeed_processed['job_quality_score'].mean():.1f}")
            print(f"  Min: {indeed_processed['job_quality_score'].min()}")
            print(f"  Max: {indeed_processed['job_quality_score'].max()}")
        
        # Show sample processed job with full details
        if len(indeed_processed) > 0:
            sample_job = indeed_processed.iloc[0]
            print(f"\n📋 Sample Processed Job (Full Details):")
            print(f"  Title: {sample_job['job_title']}")
            print(f"  Company: {sample_job['company_name']}")
            print(f"  Location: {sample_job['job_location']}")
            if 'llm_work_mode' in sample_job:
                print(f"  Work Mode: {sample_job['llm_work_mode']}")
            if 'llm_authorization' in sample_job:
                print(f"  Authorization: {sample_job['llm_authorization']}")
            if 'llm_job_seniority' in sample_job:
                print(f"  Job Seniority: {sample_job['llm_job_seniority']}")
            if 'llm_qualifications' in sample_job:
                print(f"  Qualifications: {sample_job['llm_qualifications']}")
            if 'job_quality_score' in sample_job:
                print(f"  Quality Score: {sample_job['job_quality_score']}")
            if 'llm_reason' in sample_job:
                print(f"  LLM Reason: {sample_job['llm_reason']}")
        
        # Save results
        output_file = "test_indeed_llm_results.csv"
        indeed_processed.to_csv(output_file, index=False)
        print(f"\n💾 Results saved to: {output_file}")
        
        print("\n🎉 Indeed LLM processing test completed successfully!")
        print("✅ All LLM processing features are working!")
        
    except Exception as e:
        print(f"❌ Error in Indeed LLM processing test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_indeed_llm_pipeline()
