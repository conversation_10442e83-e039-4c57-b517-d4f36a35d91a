"""
Job Description Summarizer <PERSON><PERSON><PERSON>

Handles extraction of 3-line summaries from job descriptions using advanced NLP techniques.
Includes text preprocessing, stop word removal, and key phrase extraction.
"""

import re
import pandas as pd
import numpy as np
from typing import List, Dict, Any
import nltk
from nltk.corpus import stopwords
from nltk.tokenize import sent_tokenize, word_tokenize
from nltk.stem import WordNetLemmatizer
from collections import Counter
import string

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('tokenizers/punkt_tab')
except LookupError:
    nltk.download('punkt_tab')

try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

try:
    nltk.data.find('corpora/wordnet')
except LookupError:
    nltk.download('wordnet')

class JobDescriptionSummarizer:
    """
    Advanced job description summarizer using NLP techniques.
    """
    
    def __init__(self):
        """Initialize the summarizer with NLP components."""
        self.stop_words = set(stopwords.words('english'))
        self.lemmatizer = WordNetLemmatizer()
        
        # Job-specific stop words to remove
        self.job_stop_words = {
            'job', 'position', 'role', 'work', 'company', 'team', 'department',
            'responsibilities', 'requirements', 'qualifications', 'skills',
            'experience', 'years', 'degree', 'bachelor', 'master', 'phd',
            'please', 'apply', 'submit', 'resume', 'cv', 'cover', 'letter',
            'equal', 'opportunity', 'employer', 'diverse', 'inclusive',
            'benefits', 'salary', 'compensation', 'package', 'perks'
        }
        
        # Technical skills patterns
        self.tech_patterns = [
            r'\b(python|java|javascript|react|angular|vue|node\.?js|typescript)\b',
            r'\b(sql|mysql|postgresql|mongodb|redis|elasticsearch)\b',
            r'\b(aws|azure|gcp|docker|kubernetes|terraform)\b',
            r'\b(api|rest|graphql|microservices|agile|scrum)\b',
            r'\b(machine learning|ai|artificial intelligence|data science)\b',
            r'\b(devops|ci/cd|jenkins|git|github|gitlab)\b'
        ]
        
        # Responsibility patterns
        self.responsibility_patterns = [
            r'(develop|build|create|design|implement|maintain|manage)',
            r'(lead|collaborate|work with|partner with)',
            r'(analyze|evaluate|assess|review|optimize)',
            r'(deliver|execute|complete|achieve|drive)'
        ]
        
        # Requirement patterns
        self.requirement_patterns = [
            r'(bachelor|master|phd|degree|diploma|certification)',
            r'(\d+\+?\s*years?\s*of\s*experience)',
            r'(proven|demonstrated|strong|excellent|solid)',
            r'(required|must have|should have|preferred)'
        ]
    
    def preprocess_text(self, text: str) -> str:
        """
        Preprocess text by cleaning, normalizing, and removing noise.
        
        Args:
            text: Raw job description text
            
        Returns:
            str: Preprocessed text
        """
        if pd.isna(text) or not isinstance(text, str):
            return ""
        
        # Convert to lowercase
        text = text.lower()
        
        # Remove HTML tags
        text = re.sub(r'<[^>]+>', ' ', text)
        
        # Remove URLs
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', ' ', text)
        
        # Remove email addresses
        text = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', ' ', text)
        
        # Remove phone numbers
        text = re.sub(r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b', ' ', text)
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove leading/trailing whitespace
        text = text.strip()
        
        return text
    
    def extract_key_sentences(self, text: str) -> List[str]:
        """
        Extract key sentences from job description.
        
        Args:
            text: Preprocessed job description
            
        Returns:
            List[str]: Key sentences ranked by importance
        """
        if not text:
            return []
        
        # Split into sentences
        sentences = sent_tokenize(text)
        
        # Score sentences based on various criteria
        scored_sentences = []
        
        for sentence in sentences:
            score = 0
            
            # Check for technical skills
            for pattern in self.tech_patterns:
                if re.search(pattern, sentence, re.IGNORECASE):
                    score += 3
            
            # Check for responsibilities
            for pattern in self.responsibility_patterns:
                if re.search(pattern, sentence, re.IGNORECASE):
                    score += 2
            
            # Check for requirements
            for pattern in self.requirement_patterns:
                if re.search(pattern, sentence, re.IGNORECASE):
                    score += 2
            
            # Prefer shorter sentences (more concise)
            if len(sentence.split()) <= 20:
                score += 1
            
            # Avoid sentences with too many stop words
            words = word_tokenize(sentence.lower())
            stop_word_ratio = sum(1 for word in words if word in self.stop_words) / len(words) if words else 1
            if stop_word_ratio < 0.5:
                score += 1
            
            scored_sentences.append((sentence, score))
        
        # Sort by score and return top sentences
        scored_sentences.sort(key=lambda x: x[1], reverse=True)
        return [sentence for sentence, score in scored_sentences if score > 0]
    
    def extract_key_phrases(self, text: str) -> List[str]:
        """
        Extract key phrases using TF-IDF-like approach.
        
        Args:
            text: Preprocessed job description
            
        Returns:
            List[str]: Key phrases
        """
        if not text:
            return []
        
        # Tokenize and clean
        words = word_tokenize(text.lower())
        words = [word for word in words if word.isalpha() and word not in self.stop_words]
        words = [word for word in words if word not in self.job_stop_words]
        
        # Lemmatize words
        words = [self.lemmatizer.lemmatize(word) for word in words]
        
        # Count word frequencies
        word_freq = Counter(words)
        
        # Get most common words (excluding very common ones)
        common_words = word_freq.most_common(20)
        key_phrases = [word for word, freq in common_words if freq >= 2 and len(word) > 3]
        
        return key_phrases[:10]  # Return top 10 key phrases
    
    def create_summary(self, job_description: str) -> Dict[str, Any]:
        """
        Create a comprehensive summary of the job description.
        
        Args:
            job_description: Raw job description text
            
        Returns:
            Dict containing processed text and summary
        """
        # Preprocess the text
        processed_text = self.preprocess_text(job_description)
        
        # Extract key sentences
        key_sentences = self.extract_key_sentences(processed_text)
        
        # Extract key phrases
        key_phrases = self.extract_key_phrases(processed_text)
        
        # Create 3-line summary
        summary_lines = []
        
        # Line 1: Key responsibilities (from key sentences)
        responsibility_sentences = [s for s in key_sentences[:3] if any(pattern in s.lower() for pattern in self.responsibility_patterns)]
        if responsibility_sentences:
            summary_lines.append(responsibility_sentences[0][:150] + "..." if len(responsibility_sentences[0]) > 150 else responsibility_sentences[0])
        else:
            summary_lines.append(key_sentences[0][:150] + "..." if key_sentences and len(key_sentences[0]) > 150 else key_sentences[0] if key_sentences else "No description available")
        
        # Line 2: Requirements/Qualifications
        requirement_sentences = [s for s in key_sentences[:5] if any(pattern in s.lower() for pattern in self.requirement_patterns)]
        if requirement_sentences:
            summary_lines.append(requirement_sentences[0][:150] + "..." if len(requirement_sentences[0]) > 150 else requirement_sentences[0])
        else:
            summary_lines.append(key_sentences[1][:150] + "..." if len(key_sentences) > 1 and len(key_sentences[1]) > 150 else key_sentences[1] if len(key_sentences) > 1 else "Requirements not specified")
        
        # Line 3: Technical skills or additional info
        tech_sentences = [s for s in key_sentences[:5] if any(pattern in s.lower() for pattern in self.tech_patterns)]
        if tech_sentences:
            summary_lines.append(tech_sentences[0][:150] + "..." if len(tech_sentences[0]) > 150 else tech_sentences[0])
        elif key_phrases:
            summary_lines.append(f"Key skills: {', '.join(key_phrases[:5])}")
        else:
            summary_lines.append(key_sentences[2][:150] + "..." if len(key_sentences) > 2 and len(key_sentences[2]) > 150 else key_sentences[2] if len(key_sentences) > 2 else "Additional details not available")
        
        # Ensure we have exactly 3 lines
        while len(summary_lines) < 3:
            summary_lines.append("No additional information available")
        
        summary_lines = summary_lines[:3]  # Truncate to exactly 3 lines
        
        return {
            'processed_text': processed_text,
            'summary_lines': summary_lines,
            'key_phrases': key_phrases,
            'key_sentences': key_sentences[:5],  # Top 5 key sentences
            'qualifications': ' | '.join(summary_lines)  # Final 3-line summary
        }
    
    def process_job_descriptions(self, df: pd.DataFrame, description_column: str = 'job_description') -> pd.DataFrame:
        """
        Process job descriptions in a DataFrame and add summary columns.
        
        Args:
            df: DataFrame containing job descriptions
            description_column: Name of the column containing job descriptions
            
        Returns:
            DataFrame with added summary columns
        """
        if df.empty or description_column not in df.columns:
            return df
        
        print(f"🔧 Processing job descriptions for {len(df)} jobs...")
        
        # Initialize new columns
        df['processed_description'] = ''
        df['qualifications'] = ''
        df['key_phrases'] = ''
        df['summary_line_1'] = ''
        df['summary_line_2'] = ''
        df['summary_line_3'] = ''
        
        # Process each job description
        for idx, row in df.iterrows():
            job_description = row.get(description_column, '')
            
            try:
                summary_data = self.create_summary(job_description)
                
                # Update DataFrame
                df.at[idx, 'processed_description'] = summary_data['processed_text']
                df.at[idx, 'qualifications'] = summary_data['qualifications']
                df.at[idx, 'key_phrases'] = ', '.join(summary_data['key_phrases'][:5])
                df.at[idx, 'summary_line_1'] = summary_data['summary_lines'][0]
                df.at[idx, 'summary_line_2'] = summary_data['summary_lines'][1]
                df.at[idx, 'summary_line_3'] = summary_data['summary_lines'][2]
                
            except Exception as e:
                print(f"⚠️ Error processing job {idx}: {str(e)}")
                # Set default values
                df.at[idx, 'processed_description'] = str(job_description)[:500] if job_description else ""
                df.at[idx, 'qualifications'] = "Description processing failed"
                df.at[idx, 'key_phrases'] = ""
                df.at[idx, 'summary_line_1'] = "Processing error"
                df.at[idx, 'summary_line_2'] = "Processing error"
                df.at[idx, 'summary_line_3'] = "Processing error"
        
        print(f"✅ Job description processing completed")
        return df
