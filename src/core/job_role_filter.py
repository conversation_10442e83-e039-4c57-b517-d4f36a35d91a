"""
Job Role Filter Module

Enhanced filtering system to remove non-tech roles like cook, driver, etc.
Uses both job title and job description for comprehensive filtering.
"""

import re
import pandas as pd
from typing import List, Dict, Any, Tuple
from difflib import SequenceMatcher

class JobRoleFilter:
    """
    Advanced job role filter to remove non-tech positions.
    """
    
    def __init__(self):
        """Initialize the job role filter with comprehensive exclusion patterns."""
        
        # List of NON-PROFESSIONAL roles to exclude (not non-tech)
        self.EXCLUDED_ROLES = [
            # Food Service
            'cook', 'chef', 'kitchen', 'restaurant', 'food service', 'catering',
            'waiter', 'waitress', 'server', 'bartender', 'barista', 'cashier',
            'host', 'hostess', 'busser', 'dishwasher', 'prep cook', 'line cook',
            
            # Transportation
            'driver', 'delivery', 'truck driver', 'uber', 'lyft', 'taxi',
            'chauffeur', 'courier', 'logistics', 'shipping', 'warehouse',
            'forklift', 'loader', 'unloader', 'packer', 'picker',
            
            # Retail/Sales (non-professional)
            'retail', 'sales associate', 'cashier', 'store clerk', 'merchandiser',
            'stock clerk', 'inventory', 'customer service', 'sales rep',
            'telemarketer', 'call center', 'phone sales',
            
            # Healthcare (non-professional)
            'nurse', 'nursing', 'caregiver', 'aide', 'assistant', 'technician',
            'medical assistant', 'pharmacy', 'dental', 'veterinary',
            
            # Manual Labor
            'laborer', 'construction', 'maintenance', 'janitor', 'cleaner',
            'custodian', 'groundskeeper', 'landscaper', 'painter', 'electrician',
            'plumber', 'mechanic', 'repair', 'installer',
            
            # Administrative (non-professional)
            'receptionist', 'secretary', 'administrative assistant', 'clerk',
            'data entry', 'filing', 'office assistant', 'scheduler',
            
            # Security
            'security guard', 'security officer', 'guard', 'patrol',
            
            # Education (non-professional)
            'teacher', 'tutor', 'instructor', 'professor', 'faculty',
            'educator', 'substitute teacher', 'teaching assistant',
            
            # Hospitality
            'hotel', 'housekeeping', 'concierge', 'front desk', 'reception',
            'event coordinator', 'event planner',
            
            # Other Non-Professional
            'beauty', 'cosmetology', 'hair stylist', 'nail technician',
            'massage therapist', 'fitness trainer', 'personal trainer',
            'lifeguard', 'pool attendant', 'gym instructor',
            'social worker', 'counselor', 'therapist', 'psychologist',
            'real estate', 'insurance agent', 'financial advisor',
            'bank teller', 'loan officer', 'accountant', 'bookkeeper',
            'legal assistant', 'paralegal', 'law clerk',
            'journalist', 'writer', 'editor', 'content writer',
            'photographer', 'videographer', 'graphic designer',
            'artist', 'musician', 'performer', 'entertainer'
        ]
        
        # Management roles to exclude (except PM roles and tech management)
        self.MANAGEMENT_EXCLUDED = [
            'ceo', 'cfo', 'coo', 'vp', 'vice president',
            'executive', 'principal', 'head', 'supervisor'
        ]
        
        # PM roles that should be kept (exceptions to management exclusion)
        self.PM_EXCEPTIONS = [
            'product manager', 'project manager', 'program manager',
            'technical product manager', 'senior product manager',
            'associate product manager', 'junior product manager'
        ]
        
        # Tech roles that should be kept (all tech roles are professional)
        self.TECH_ROLES = [
            'software', 'developer', 'engineer', 'programmer', 'coder',
            'data scientist', 'data analyst', 'data engineer', 'analyst',
            'architect', 'consultant', 'specialist', 'technician',
            'designer', 'ui', 'ux', 'frontend', 'backend', 'fullstack',
            'devops', 'sre', 'qa', 'tester', 'automation',
            'machine learning', 'ai', 'artificial intelligence',
            'cloud', 'aws', 'azure', 'gcp', 'docker', 'kubernetes',
            'database', 'dba', 'admin', 'administrator',
            'security', 'cyber', 'network', 'system', 'infrastructure',
            'mobile', 'ios', 'android', 'react', 'angular', 'vue',
            'python', 'java', 'javascript', 'typescript', 'c++', 'c#',
            'ruby', 'php', 'go', 'rust', 'scala', 'kotlin', 'swift'
        ]
        
        # Professional non-tech roles that should be kept
        self.PROFESSIONAL_ROLES = [
            'marketing', 'finance', 'hr', 'human resources', 'accounting',
            'business', 'strategy', 'operations', 'consulting', 'sales',
            'business development', 'product management', 'project management',
            'program management', 'research', 'analyst', 'coordinator',
            'specialist', 'manager', 'director', 'lead', 'senior',
            'engineering', 'cto', 'technical', 'technology'
        ]
        
        # Patterns for job descriptions that indicate non-professional roles
        self.DESCRIPTION_EXCLUSION_PATTERNS = [
            r'(cooking|food preparation|kitchen|restaurant)',
            r'(driving|delivery|transportation|logistics)',
            r'(retail|sales associate|customer service|cashier)',
            r'(nursing|healthcare|medical|patient care)',
            r'(teaching|education|classroom|student)',
            r'(cleaning|maintenance|janitorial|custodial)',
            r'(security|patrol|guard|surveillance)',
            r'(beauty|cosmetology|hair|nail|spa)',
            r'(fitness|gym|personal trainer|exercise)',
            r'(art|music|entertainment|performance)'
        ]
    
    def calculate_similarity(self, text1: str, text2: str) -> float:
        """
        Calculate similarity between two strings.
        
        Args:
            text1: First string
            text2: Second string
            
        Returns:
            float: Similarity score between 0 and 1
        """
        return SequenceMatcher(None, text1.lower(), text2.lower()).ratio()
    
    def is_tech_role(self, job_title: str, job_description: str = "") -> bool:
        """
        Check if a role is tech-related based on title and description.
        
        Args:
            job_title: Job title
            job_description: Job description (optional)
            
        Returns:
            bool: True if tech role, False otherwise
        """
        # Handle NaN values
        if pd.isna(job_title):
            job_title = ''
        if pd.isna(job_description):
            job_description = ''
        
        title_lower = job_title.lower()
        desc_lower = job_description.lower() if job_description else ""
        
        # Check for tech keywords in title
        for tech_keyword in self.TECH_ROLES:
            if tech_keyword in title_lower:
                return True
        
        # Check for tech keywords in description (first 500 chars)
        desc_sample = desc_lower[:500]
        for tech_keyword in self.TECH_ROLES:
            if tech_keyword in desc_sample:
                return True
        
        return False
    
    def is_pm_role(self, job_title: str) -> bool:
        """
        Check if a role is a PM role (should be kept even if management).
        
        Args:
            job_title: Job title
            
        Returns:
            bool: True if PM role, False otherwise
        """
        # Handle NaN values
        if pd.isna(job_title):
            job_title = ''
        
        title_lower = job_title.lower()
        
        for pm_keyword in self.PM_EXCEPTIONS:
            if pm_keyword in title_lower:
                return True
        
        return False
    
    def is_professional_role(self, job_title: str) -> bool:
        """
        Check if a role is professional (tech or business professional).
        
        Args:
            job_title: Job title
            
        Returns:
            bool: True if professional role, False otherwise
        """
        title_lower = job_title.lower()
        
        # Check for tech keywords
        for tech_keyword in self.TECH_ROLES:
            if tech_keyword in title_lower:
                return True
        
        # Check for professional business keywords
        for prof_keyword in self.PROFESSIONAL_ROLES:
            if prof_keyword in title_lower:
                return True
        
        return False

    def should_exclude_by_title(self, job_title: str) -> Tuple[bool, str]:
        """
        Check if job should be excluded based on title.
        
        Args:
            job_title: Job title
            
        Returns:
            Tuple[bool, str]: (should_exclude, reason)
        """
        if not job_title or pd.isna(job_title):
            return False, ""
        
        title_lower = job_title.lower()
        
        # First check: Is it a PM role? If yes, keep it
        if self.is_pm_role(job_title):
            return False, "PM role - keeping"
        
        # Second check: Non-professional roles (cook, driver, etc.) - exclude
        for excluded_role in self.EXCLUDED_ROLES:
            if excluded_role in title_lower:
                return True, f"Non-professional role: {excluded_role}"
        
        # Third check: Management roles (CEO, Director, etc.) - exclude unless PM
        for mgmt_term in self.MANAGEMENT_EXCLUDED:
            if mgmt_term in title_lower:
                return True, f"Management role: {mgmt_term}"
        
        # Fourth check: Is it a professional role? If yes, keep it
        if self.is_professional_role(job_title):
            return False, "Professional role - keeping"
        
        # Check for fuzzy matches (similarity > 0.8) for non-professional roles
        for excluded_role in self.EXCLUDED_ROLES:
            if len(excluded_role) > 4:  # Only check longer terms
                similarity = self.calculate_similarity(title_lower, excluded_role)
                if similarity > 0.8:
                    return True, f"Similar to non-professional role: {excluded_role} (similarity: {similarity:.2f})"
        
        return False, "Unknown role - keeping by default"
    
    def should_exclude_by_description(self, job_description: str) -> Tuple[bool, str]:
        """
        Check if job should be excluded based on description.
        
        Args:
            job_description: Job description
            
        Returns:
            Tuple[bool, str]: (should_exclude, reason)
        """
        if not job_description or pd.isna(job_description):
            return False, ""
        
        desc_lower = job_description.lower()
        
        # Check for exclusion patterns
        for pattern in self.DESCRIPTION_EXCLUSION_PATTERNS:
            if re.search(pattern, desc_lower):
                return True, f"Description matches exclusion pattern: {pattern}"
        
        # Check for high concentration of non-tech keywords
        non_tech_count = 0
        total_words = len(desc_lower.split())
        
        for excluded_role in self.EXCLUDED_ROLES:
            if excluded_role in desc_lower:
                non_tech_count += 1
        
        # If more than 3 non-tech keywords in description, likely non-tech role
        if non_tech_count >= 3:
            return True, f"High concentration of non-tech keywords: {non_tech_count}"
        
        return False, ""
    
    def filter_job(self, job_title: str, job_description: str = "") -> Dict[str, Any]:
        """
        Filter a single job based on title and description.
        
        Args:
            job_title: Job title
            job_description: Job description
            
        Returns:
            Dict containing filter result and details
        """
        # Check title-based exclusion
        title_exclude, title_reason = self.should_exclude_by_title(job_title)
        if title_exclude:
            return {
                'should_keep': False,
                'reason': title_reason,
                'confidence': 'high',
                'filter_type': 'title'
            }
        
        # Check description-based exclusion
        desc_exclude, desc_reason = self.should_exclude_by_description(job_description)
        if desc_exclude:
            return {
                'should_keep': False,
                'reason': desc_reason,
                'confidence': 'medium',
                'filter_type': 'description'
            }
        
        # Check if it's a tech role (positive indicator)
        is_tech = self.is_tech_role(job_title, job_description)
        
        return {
            'should_keep': True,
            'reason': 'Tech role detected' if is_tech else 'No exclusion criteria met',
            'confidence': 'high' if is_tech else 'medium',
            'filter_type': 'tech_role' if is_tech else 'no_exclusion'
        }
    
    def filter_dataframe(self, df: pd.DataFrame, 
                        title_column: str = 'job_title',
                        description_column: str = 'job_description') -> pd.DataFrame:
        """
        Filter a DataFrame of jobs based on role criteria.
        
        Args:
            df: DataFrame containing job data
            title_column: Name of the job title column
            description_column: Name of the job description column
            
        Returns:
            DataFrame with filtered jobs and filter metadata
        """
        if df.empty:
            return df
        
        print(f"🔍 Filtering {len(df)} jobs for non-tech roles...")
        
        # Initialize filter result columns
        df['filter_keep'] = True
        df['filter_reason'] = ''
        df['filter_confidence'] = ''
        df['filter_type'] = ''
        
        filtered_count = 0
        
        for idx, row in df.iterrows():
            # Handle NaN values properly
            job_title = row.get(title_column, '')
            job_description = row.get(description_column, '')
            
            # Convert to string and handle NaN values
            if pd.isna(job_title):
                job_title = ''
            else:
                job_title = str(job_title)
            
            if pd.isna(job_description):
                job_description = ''
            else:
                job_description = str(job_description)
            
            filter_result = self.filter_job(job_title, job_description)
            
            # Update DataFrame
            df.at[idx, 'filter_keep'] = filter_result['should_keep']
            df.at[idx, 'filter_reason'] = filter_result['reason']
            df.at[idx, 'filter_confidence'] = filter_result['confidence']
            df.at[idx, 'filter_type'] = filter_result['filter_type']
            
            if not filter_result['should_keep']:
                filtered_count += 1
        
        # Filter the DataFrame
        filtered_df = df[df['filter_keep'] == True].copy()
        
        # Remove filter metadata columns from final output
        filter_columns = ['filter_keep', 'filter_reason', 'filter_confidence', 'filter_type']
        filtered_df = filtered_df.drop(columns=filter_columns)
        
        print(f"✅ Job filtering completed: {len(df)} → {len(filtered_df)} jobs (removed {filtered_count} non-tech roles)")
        
        # Print summary of filtered jobs
        if filtered_count > 0:
            print(f"📊 Filtered job reasons:")
            filter_reasons = df[df['filter_keep'] == False]['filter_reason'].value_counts()
            for reason, count in filter_reasons.head(10).items():
                print(f"   • {reason}: {count}")
        
        return filtered_df
