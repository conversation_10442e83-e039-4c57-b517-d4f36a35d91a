"""
Authorization Extractor Module

Advanced work authorization requirements detection using enhanced pattern matching.
"""

import re
import pandas as pd
from typing import Dict, Any, Tuple

class AuthorizationExtractor:
    """
    Advanced authorization extractor with context analysis and confidence scoring.
    """
    
    def __init__(self):
        """Initialize the authorization extractor with comprehensive patterns."""
        
        # US Citizen requirements with confidence scores
        self.CITIZEN_PATTERNS = {
            'high_confidence': [
                r'\b(us citizen|u\.s\. citizen|united states citizen)\b',
                r'\b(must be a citizen|citizen required|citizenship required)\b',
                r'\b(us citizenship|u\.s\. citizenship|american citizen)\b',
                r'\b(citizen of the united states|citizen of the us)\b',
                r'\b(citizen|citizenship)\b'
            ],
            'medium_confidence': [
                r'\b(us national|u\.s\. national)\b',
                r'\b(american|americans only)\b'
            ],
            'low_confidence': [
                r'\b(citizen status|citizenship status)\b',
                r'\b(eligible to work|work authorization)\b'
            ]
        }
        
        # Green Card requirements
        self.GREEN_CARD_PATTERNS = {
            'high_confidence': [
                r'\b(green card|permanent resident|pr status)\b',
                r'\b(permanent resident alien|lawful permanent resident)\b',
                r'\b(lpr|permanent residence|resident alien)\b'
            ],
            'medium_confidence': [
                r'\b(permanent resident|resident status)\b',
                r'\b(immigration status|resident alien)\b'
            ],
            'low_confidence': [
                r'\b(resident|residency)\b'
            ]
        }
        
        # Visa sponsorship patterns
        self.VISA_SPONSORSHIP_PATTERNS = {
            'high_confidence': [
                r'\b(visa sponsorship|sponsor visa|h1b sponsorship)\b',
                r'\b(work visa|employment visa|immigration sponsorship)\b',
                r'\b(sponsor h1b|h1b visa|h-1b)\b',
                r'\b(immigration support|visa support)\b'
            ],
            'medium_confidence': [
                r'\b(visa|h1b|h-1b|work permit)\b',
                r'\b(sponsor|sponsorship)\b',
                r'\b(immigration|immigrant)\b'
            ],
            'low_confidence': [
                r'\b(international|foreign|overseas)\b'
            ]
        }
        
        # No sponsorship patterns
        self.NO_SPONSORSHIP_PATTERNS = {
            'high_confidence': [
                r'\b(no sponsorship|no visa|no h1b|no h-1b)\b',
                r'\b(no immigration|no visa sponsorship)\b',
                r'\b(citizens only|us citizens only)\b',
                r'\b(no work visa|no employment visa)\b'
            ],
            'medium_confidence': [
                r'\b(no sponsor|no sponsoring)\b',
                r'\b(no immigration support|no visa support)\b',
                r'\b(citizenship required|citizen required)\b'
            ],
            'low_confidence': [
                r'\b(no international|no foreign)\b'
            ]
        }
        
        # Context modifiers that affect confidence
        self.CONTEXT_MODIFIERS = {
            'positive': [
                r'\b(required|must|need|essential|critical|mandatory)\b',
                r'\b(only|exclusively|solely|purely)\b',
                r'\b(permanent|long term|ongoing)\b',
                r'\b(immediately|urgent|asap)\b'
            ],
            'negative': [
                r'\b(not|no|never|avoid|prevent|prohibit)\b',
                r'\b(sometimes|occasionally|rarely|seldom)\b',
                r'\b(temporary|short term|limited)\b',
                r'\b(preferred|nice to have|optional)\b'
            ]
        }
        
        # Negation patterns that reverse meaning
        self.NEGATION_PATTERNS = [
            r'\b(no\s+sponsorship|no\s+visa|no\s+h1b)\b',
            r'\b(not\s+required|not\s+needed|not\s+necessary)\b',
            r'\b(no\s+citizenship|no\s+green\s+card)\b'
        ]
    
    def extract_authorization_from_text(self, text: str) -> Dict[str, Any]:
        """
        Extract authorization requirements from text with confidence scoring.
        
        Args:
            text: Text to analyze (job description)
            
        Returns:
            Dict containing authorization requirements and confidence details
        """
        if not text or pd.isna(text):
            return {
                'authorization': 'Not Specified',
                'confidence': 'low',
                'reason': 'No text provided',
                'patterns_found': []
            }
        
        text_lower = text.lower()
        patterns_found = []
        confidence_scores = {
            'us_citizen': 0, 
            'green_card': 0, 
            'visa_sponsorship': 0, 
            'no_sponsorship': 0
        }
        
        # Check for negation patterns first
        has_negation = False
        for pattern in self.NEGATION_PATTERNS:
            if re.search(pattern, text_lower):
                has_negation = True
                patterns_found.append(f"Negation: {pattern}")
                break
        
        # Check US Citizen patterns
        for confidence_level, patterns in self.CITIZEN_PATTERNS.items():
            for pattern in patterns:
                matches = re.findall(pattern, text_lower, re.IGNORECASE)
                if matches:
                    patterns_found.append(f"US Citizen ({confidence_level}): {pattern}")
                    score = 3 if confidence_level == 'high_confidence' else (2 if confidence_level == 'medium_confidence' else 1)
                    confidence_scores['us_citizen'] += score
        
        # Check Green Card patterns
        for confidence_level, patterns in self.GREEN_CARD_PATTERNS.items():
            for pattern in patterns:
                matches = re.findall(pattern, text_lower, re.IGNORECASE)
                if matches:
                    patterns_found.append(f"Green Card ({confidence_level}): {pattern}")
                    score = 3 if confidence_level == 'high_confidence' else (2 if confidence_level == 'medium_confidence' else 1)
                    confidence_scores['green_card'] += score
        
        # Check Visa Sponsorship patterns
        for confidence_level, patterns in self.VISA_SPONSORSHIP_PATTERNS.items():
            for pattern in patterns:
                matches = re.findall(pattern, text_lower, re.IGNORECASE)
                if matches:
                    patterns_found.append(f"Visa Sponsorship ({confidence_level}): {pattern}")
                    score = 3 if confidence_level == 'high_confidence' else (2 if confidence_level == 'medium_confidence' else 1)
                    confidence_scores['visa_sponsorship'] += score
        
        # Check No Sponsorship patterns
        for confidence_level, patterns in self.NO_SPONSORSHIP_PATTERNS.items():
            for pattern in patterns:
                matches = re.findall(pattern, text_lower, re.IGNORECASE)
                if matches:
                    patterns_found.append(f"No Sponsorship ({confidence_level}): {pattern}")
                    score = 3 if confidence_level == 'high_confidence' else (2 if confidence_level == 'medium_confidence' else 1)
                    confidence_scores['no_sponsorship'] += score
        
        # Apply context modifiers
        for modifier_type, patterns in self.CONTEXT_MODIFIERS.items():
            for pattern in patterns:
                if re.search(pattern, text_lower):
                    if modifier_type == 'positive':
                        # Increase confidence for all detected requirements
                        for req in confidence_scores:
                            if confidence_scores[req] > 0:
                                confidence_scores[req] += 1
                    else:  # negative
                        # Decrease confidence for all detected requirements
                        for req in confidence_scores:
                            if confidence_scores[req] > 0:
                                confidence_scores[req] = max(0, confidence_scores[req] - 1)
        
        # Handle negation - reverse the meaning
        if has_negation:
            # If we found "no sponsorship" patterns, they override other patterns
            if confidence_scores['no_sponsorship'] > 0:
                confidence_scores['visa_sponsorship'] = 0
                confidence_scores['us_citizen'] = 0
                confidence_scores['green_card'] = 0
        
        # Determine authorization based on highest score
        max_score = max(confidence_scores.values())
        if max_score == 0:
            return {
                'authorization': 'Not Specified',
                'confidence': 'low',
                'reason': 'No authorization patterns found',
                'patterns_found': patterns_found
            }
        
        # Find the requirement with highest score
        if confidence_scores['us_citizen'] == max_score:
            authorization = 'US Citizen Required'
        elif confidence_scores['green_card'] == max_score:
            authorization = 'Green Card Required'
        elif confidence_scores['visa_sponsorship'] == max_score:
            authorization = 'Visa Sponsorship Available'
        elif confidence_scores['no_sponsorship'] == max_score:
            authorization = 'No Visa Sponsorship'
        else:
            authorization = 'Not Specified'
        
        # Determine confidence level
        if max_score >= 4:
            confidence = 'high'
        elif max_score >= 2:
            confidence = 'medium'
        else:
            confidence = 'low'
        
        return {
            'authorization': authorization,
            'confidence': confidence,
            'reason': f'Detected {authorization} with score {max_score}',
            'patterns_found': patterns_found,
            'scores': confidence_scores
        }
    
    def extract_authorization(self, job_description: str) -> Dict[str, Any]:
        """
        Extract authorization requirements from job description.
        
        Args:
            job_description: Job description text
            
        Returns:
            Dict containing final authorization determination
        """
        return self.extract_authorization_from_text(job_description)
    
    def process_dataframe(self, df: pd.DataFrame, 
                         description_column: str = 'job_description') -> pd.DataFrame:
        """
        Process a DataFrame to extract authorization requirements.
        
        Args:
            df: DataFrame containing job data
            description_column: Name of the job description column
            
        Returns:
            DataFrame with authorization information added
        """
        if df.empty:
            return df
        
        print(f"🔧 Extracting authorization requirements for {len(df)} jobs...")
        
        # Initialize authorization columns
        df['authorization'] = 'Not Specified'
        df['authorization_confidence'] = 'low'
        df['authorization_reason'] = ''
        df['authorization_patterns'] = ''
        
        for idx, row in df.iterrows():
            job_description = row.get(description_column, '')
            
            try:
                result = self.extract_authorization(job_description)
                
                df.at[idx, 'authorization'] = result['authorization']
                df.at[idx, 'authorization_confidence'] = result['confidence']
                df.at[idx, 'authorization_reason'] = result['reason']
                df.at[idx, 'authorization_patterns'] = '; '.join(result.get('patterns_found', []))
                
            except Exception as e:
                print(f"⚠️ Error extracting authorization for job {idx}: {str(e)}")
                df.at[idx, 'authorization'] = 'Not Specified'
                df.at[idx, 'authorization_confidence'] = 'low'
                df.at[idx, 'authorization_reason'] = f'Error: {str(e)}'
                df.at[idx, 'authorization_patterns'] = ''
        
        # Print summary
        auth_counts = df['authorization'].value_counts()
        print(f"✅ Authorization extraction completed:")
        for auth, count in auth_counts.items():
            print(f"   • {auth}: {count}")
        
        return df
