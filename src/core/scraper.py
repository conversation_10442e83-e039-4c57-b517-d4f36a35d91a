"""
Core Scraper Module

Handles all scraping operations using BrightData API for Indeed and LinkedIn.
"""

import requests
import datetime
import json
import os
import time
from typing import Dict, List, Any, Optional

class BrightDataScraper:
    """
    BrightData API scraper for Indeed and LinkedIn job data.
    Dynamically creates separate API calls for each input category.
    """
    
    def __init__(self, config_file: str = "config/scraping_inputs.json", brightdata_config_file: str = "config/brightdata_config.json"):
        """
        Initialize the scraper with configuration.
        
        Args:
            config_file: Path to JSON file with input configurations
            brightdata_config_file: Path to JSON file with BrightData API configuration
        """
        self.config_file = config_file
        self.brightdata_config_file = brightdata_config_file
        
        # Load BrightData API configuration
        self.load_brightdata_config()
        
        # Load input configurations
        self.load_configurations()
    
    def load_brightdata_config(self):
        """Load BrightData API configuration from JSON file."""
        try:
            with open(self.brightdata_config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            brightdata_config = config_data.get('brightdata', {})
            
            self.api_config = {
                "url": brightdata_config.get("url", "https://api.brightdata.com/datasets/v3/trigger"),
                "headers": {
                    "Authorization": f"Bearer {brightdata_config.get('api_key', '')}",
                    "Content-Type": "application/json",
                },
                "aws_credentials": brightdata_config.get("aws_credentials", {}),
                "bucket": brightdata_config.get("bucket", "bright-data-api")
            }
            
            # Dataset IDs
            self.dataset_ids = brightdata_config.get("dataset_ids", {
                "indeed": "gd_l4dx9j9sscpvs7no2",
                "linkedin": "gd_lpfll7v5hcqtkxl6l"
            })
            
            # Limits per platform
            self.limits = brightdata_config.get("limits", {
                "indeed": 1,
                "linkedin": 1
            })
            
            print(f"✅ Loaded BrightData API configuration from '{self.brightdata_config_file}'")
            
        except FileNotFoundError:
            print(f"❌ BrightData config file '{self.brightdata_config_file}' not found")
            # Fallback to hardcoded values
            self.api_config = {
                "url": "https://api.brightdata.com/datasets/v3/trigger",
                "headers": {
                    "Authorization": "Bearer YOUR_API_KEY_HERE",
                    "Content-Type": "application/json",
                },
                "aws_credentials": {
                    "aws-access-key": "********************",
                    "aws-secret-key": "n+6MxKRIu03f4kN4/EvZc6fy5ZAB98CKKd/wLWzW"
                },
                "bucket": "bright-data-api"
            }
            self.dataset_ids = {
                "indeed": "gd_l4dx9j9sscpvs7no2",
                "linkedin": "gd_lpfll7v5hcqtkxl6l"
            }
            self.limits = {
                "indeed": 1,
                "linkedin": 1
            }
        except Exception as e:
            print(f"❌ Error loading BrightData configuration: {str(e)}")
            raise
    
    def load_configurations(self):
        """Load input configurations from JSON file."""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            self.indeed_inputs = config_data.get('indeed', {})
            self.linkedin_inputs = config_data.get('linkedin', {})
            self.metadata = config_data.get('metadata', {})
            
            print(f"📂 Loaded configurations from '{self.config_file}'")
            print(f"   • Indeed categories: {len(self.indeed_inputs)}")
            print(f"   • LinkedIn categories: {len(self.linkedin_inputs)}")
            print(f"   • Total Indeed inputs: {sum(len(inputs) for inputs in self.indeed_inputs.values())}")
            print(f"   • Total LinkedIn inputs: {sum(len(inputs) for inputs in self.linkedin_inputs.values())}")
            
        except FileNotFoundError:
            print(f"❌ Configuration file '{self.config_file}' not found")
            self.indeed_inputs = {}
            self.linkedin_inputs = {}
            self.metadata = {}
        except Exception as e:
            print(f"❌ Error loading configurations: {str(e)}")
            self.indeed_inputs = {}
            self.linkedin_inputs = {}
            self.metadata = {}
    
    def create_s3_directory_path(self, platform: str, category: str) -> str:
        """Create S3 directory path for storing results."""
        today = datetime.datetime.now().strftime("%Y-%m-%d")
        return f"{today}/{platform}/{category}/"
    
    def clean_inputs_for_platform(self, platform: str, inputs: List[Dict]) -> List[Dict]:
        """Clean inputs based on platform requirements."""
        cleaned_inputs = []
        
        for input_dict in inputs:
            cleaned_input = input_dict.copy()
            
            if platform.lower() == 'indeed':
                # Indeed uses different field structure
                if 'keyword' in cleaned_input:
                    del cleaned_input['keyword']
                
                # Keep required Indeed fields
                allowed_fields = {
                    'keyword_search', 'location', 'country', 'domain',
                    'date_posted', 'posted_by', 'location_radius'
                }
                
                # Keep only allowed fields and remove empty values
                cleaned_input = {k: v for k, v in cleaned_input.items() 
                               if k in allowed_fields and v != ""}
                
            elif platform.lower() == 'linkedin':
                # LinkedIn allowed fields
                allowed_fields = {
                    'keyword', 'location', 'country', 'time_range', 'job_type',
                    'experience_level', 'remote', 'company', 'location_radius'
                }
                
                # Keep only allowed fields
                cleaned_input = {k: v for k, v in cleaned_input.items() if k in allowed_fields}
            
            # Remove empty string values to clean up the input
            cleaned_input = {k: v for k, v in cleaned_input.items() if v != ""}
            
            cleaned_inputs.append(cleaned_input)
        
        return cleaned_inputs

    def trigger_single_category_scraping(self, platform: str, category: str, inputs: List[Dict]) -> Dict[str, Any]:
        """Trigger scraping for a single category (one API call)."""
        if not inputs:
            return {"error": "No inputs provided"}
        
        # Clean inputs for the specific platform
        cleaned_inputs = self.clean_inputs_for_platform(platform, inputs)
        
        if not cleaned_inputs:
            return {"error": "No valid inputs after cleaning"}
        
        limit_per_input = self.limits[platform]
        directory = self.create_s3_directory_path(platform, category)
        
        # Show sample of cleaned input for debugging
        if cleaned_inputs:
            sample_input = cleaned_inputs[0]
            print(f"   🔍 Sample cleaned input: {sample_input}")
        
        # Prepare API request
        params = {
            "dataset_id": self.dataset_ids[platform],
            "include_errors": "true",
            "type": "discover_new",
            "discover_by": "keyword",
        }
        
        # Both Indeed and LinkedIn use the same delivery structure
        data = {
            "deliver": {
                "type": "s3",
                "filename": {"template": "{[snapshot_id]}", "extension": "csv"},
                "bucket": self.api_config["bucket"],
                "credentials": self.api_config["aws_credentials"],
                "directory": directory
            },
            "input": cleaned_inputs
        }
        
        try:
            # Make API request
            print(f"🌐 Making API request to: {self.api_config['url']}")
            print(f"🔑 Using API key: {self.api_config['headers']['Authorization'][:20]}...")
            
            response = requests.post(
                self.api_config["url"],
                headers=self.api_config["headers"],
                params=params,
                json=data
            )
            
            print(f"📡 Response status code: {response.status_code}")
            print(f"📄 Response headers: {dict(response.headers)}")
            print(f"📝 Response text: {response.text[:500]}...")
            
            if response.status_code != 200:
                error_result = {
                    "error": f"HTTP {response.status_code}: {response.text}",
                    "category": category,
                    "platform": platform,
                    "input_count": len(inputs)
                }
                print(f"❌ HTTP Error {response.status_code}: {response.text}")
                return error_result
            
            result = response.json()
            
            # Log the request
            print(f"✅ {platform.upper()} - {category}")
            print(f"   📊 {len(inputs)} inputs × {limit_per_input} results = ~{len(inputs) * limit_per_input} total results")
            print(f"   📁 S3 Path: {directory}")
            print(f"   🆔 Snapshot ID: {result.get('snapshot_id', 'N/A')}")
            
            # Add metadata to result
            result['category'] = category
            result['platform'] = platform
            result['input_count'] = len(inputs)
            result['expected_results'] = len(inputs) * limit_per_input
            result['s3_directory'] = directory
            
            return result
            
        except json.JSONDecodeError as e:
            error_result = {
                "error": f"JSON decode error: {str(e)}. Response: {response.text[:200]}",
                "category": category,
                "platform": platform,
                "input_count": len(inputs)
            }
            print(f"❌ JSON decode error: {str(e)}")
            print(f"📄 Raw response: {response.text[:500]}")
            return error_result
        except Exception as e:
            error_result = {
                "error": str(e),
                "category": category,
                "platform": platform,
                "input_count": len(inputs)
            }
            print(f"❌ Error scraping {platform} - {category}: {str(e)}")
            return error_result
    
    def scrape_all_platforms(self, delay_seconds: int = 5) -> Dict[str, Dict[str, Dict[str, Any]]]:
        """Scrape all categories from both platforms."""
        print("🚀 STARTING COMPLETE SCRAPING - ALL PLATFORMS")
        print("=" * 70)
        
        all_results = {}
        
        # Scrape Indeed
        if self.indeed_inputs:
            indeed_results = self.scrape_indeed_all_categories(delay_seconds=2)
            all_results['indeed'] = indeed_results
            
            if self.linkedin_inputs and delay_seconds > 0:
                print(f"\n⏳ Waiting {delay_seconds} seconds before switching to LinkedIn...")
                time.sleep(delay_seconds)
        
        # Scrape LinkedIn
        if self.linkedin_inputs:
            linkedin_results = self.scrape_linkedin_all_categories(delay_seconds=2)
            all_results['linkedin'] = linkedin_results
        
        # Overall summary
        total_categories = len(self.indeed_inputs) + len(self.linkedin_inputs)
        total_successful = 0
        total_expected_results = 0
        
        for platform_results in all_results.values():
            total_successful += sum(1 for r in platform_results.values() if 'snapshot_id' in r)
            total_expected_results += sum(r.get('expected_results', 0) for r in platform_results.values())
        
        print(f"\n🎉 COMPLETE SCRAPING FINISHED!")
        print(f"📊 OVERALL SUMMARY:")
        print(f"   • Total categories scraped: {total_categories}")
        print(f"   • Successful API calls: {total_successful}")
        print(f"   • Expected total results: ~{total_expected_results:,}")
        print(f"   • Data saved to S3 in date-organized folders")
        
        # Save results summary
        self.save_scraping_results(all_results)
        
        return all_results
    
    def scrape_indeed_all_categories(self, delay_seconds: int = 2) -> Dict[str, Dict[str, Any]]:
        """Scrape all Indeed categories (separate API call for each category)."""
        print(f"🎯 STARTING INDEED SCRAPING - {len(self.indeed_inputs)} CATEGORIES")
        print("=" * 60)
        
        results = {}
        
        for i, (category, inputs) in enumerate(self.indeed_inputs.items(), 1):
            print(f"\n📋 Category {i}/{len(self.indeed_inputs)}: {category}")
            print("-" * 40)
            
            result = self.trigger_single_category_scraping('indeed', category, inputs)
            results[category] = result
            
            # Add delay between requests (except for the last one)
            if i < len(self.indeed_inputs) and delay_seconds > 0:
                print(f"⏳ Waiting {delay_seconds} seconds before next request...")
                time.sleep(delay_seconds)
        
        # Summary
        successful = sum(1 for r in results.values() if 'snapshot_id' in r)
        total_expected = sum(r.get('expected_results', 0) for r in results.values())
        
        print(f"\n📊 INDEED SCRAPING SUMMARY:")
        print(f"   • Total categories: {len(self.indeed_inputs)}")
        print(f"   • Successful API calls: {successful}")
        print(f"   • Failed API calls: {len(results) - successful}")
        print(f"   • Expected total results: ~{total_expected:,}")
        
        return results
    
    def scrape_linkedin_all_categories(self, delay_seconds: int = 2) -> Dict[str, Dict[str, Any]]:
        """Scrape all LinkedIn categories (separate API call for each category)."""
        print(f"🔗 STARTING LINKEDIN SCRAPING - {len(self.linkedin_inputs)} CATEGORIES")
        print("=" * 60)
        
        results = {}
        
        for i, (category, inputs) in enumerate(self.linkedin_inputs.items(), 1):
            print(f"\n📋 Category {i}/{len(self.linkedin_inputs)}: {category}")
            print("-" * 40)
            
            result = self.trigger_single_category_scraping('linkedin', category, inputs)
            results[category] = result
            
            # Add delay between requests (except for the last one)
            if i < len(self.linkedin_inputs) and delay_seconds > 0:
                print(f"⏳ Waiting {delay_seconds} seconds before next request...")
                time.sleep(delay_seconds)
        
        # Summary
        successful = sum(1 for r in results.values() if 'snapshot_id' in r)
        total_expected = sum(r.get('expected_results', 0) for r in results.values())
        
        print(f"\n📊 LINKEDIN SCRAPING SUMMARY:")
        print(f"   • Total categories: {len(self.linkedin_inputs)}")
        print(f"   • Successful API calls: {successful}")
        print(f"   • Failed API calls: {len(results) - successful}")
        print(f"   • Expected total results: ~{total_expected:,}")
        
        return results
    
    def save_scraping_results(self, results: Dict[str, Dict[str, Dict[str, Any]]]):
        """Save scraping results summary to JSON file."""
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"scraping_results_{timestamp}.json"
        
        summary_data = {
            "metadata": {
                "scraping_timestamp": datetime.datetime.now().isoformat(),
                "config_file_used": self.config_file,
                "total_api_calls": sum(len(platform_results) for platform_results in results.values()),
                "platforms_scraped": list(results.keys())
            },
            "results": results
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(summary_data, f, indent=2, ensure_ascii=False)
            print(f"💾 Scraping results saved to '{filename}'")
        except Exception as e:
            print(f"⚠️  Could not save results: {str(e)}")
