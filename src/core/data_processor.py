"""
Core Data Processor Module

Handles all data processing operations including loading, cleaning, filtering,
and combining LinkedIn and Indeed job data with redirect extraction.
"""

import os
import json
import re
import pandas as pd
import numpy as np
import time
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

from .redirect_handler import <PERSON>irectHandler
from .job_description_summarizer import JobDescriptionSummarizer
from .job_role_filter import Job<PERSON><PERSON>Filter
# Work mode and authorization extractors removed - will use LLM processing

# LLM integration for LinkedIn and Indeed jobs
from llm.linkedin_processor import LinkedInJobProcessor, LinkedInProcessorConfig
from llm.indeed_processor import IndeedJobProcessor, IndeedProcessorConfig
from llm.groq_client import GroqStructuredClient, GroqConfig

class DataProcessor:
    """
    Main data processor for job data from LinkedIn and Indeed.
    """
    
    def __init__(self, 
                 linkedin_folder: str = "data/raw/linkedin_data",
                 indeed_folder: str = "data/raw/indeed_data",
                 output_folder: str = "data/processed/combined_data",
                 extract_redirects: bool = True,
                 redirect_delay: int = 2,
                 linkedin_only: bool = False,
                 indeed_only: bool = False,
                 use_llm_filter: bool = False):
        """
        Initialize the data processor.
        
        Args:
            linkedin_folder: Path to LinkedIn data folder
            indeed_folder: Path to Indeed data folder
            output_folder: Path to output folder
            extract_redirects: Whether to extract final apply links
            redirect_delay: Delay between redirect requests
            linkedin_only: Whether to process only LinkedIn jobs
            indeed_only: Whether to process only Indeed jobs
            use_llm_filter: Whether to use LLM-based filtering for Indeed jobs
        """
        self.linkedin_folder = linkedin_folder
        self.indeed_folder = indeed_folder
        self.output_folder = output_folder
        self.extract_redirects = extract_redirects
        self.linkedin_only = linkedin_only
        self.indeed_only = indeed_only
        self.use_llm_filter = use_llm_filter
        
        # Initialize redirect handler if needed
        if self.extract_redirects:
            self.redirect_handler = RedirectHandler(delay_between_requests=redirect_delay)
        else:
            self.redirect_handler = None
        
        # Initialize new processing components
        self.job_summarizer = JobDescriptionSummarizer()
        self.job_filter = JobRoleFilter()
        # Work mode and authorization extractors removed - will use LLM processing
        
        # Initialize LLM processors for LinkedIn and Indeed jobs
        self.linkedin_llm_processor = None
        self.indeed_llm_processor = None
        self._initialize_llm_processor()
        
        # Create output folder if it doesn't exist
        Path(self.output_folder).mkdir(parents=True, exist_ok=True)
    
    def _initialize_llm_processor(self):
        """Initialize the LinkedIn LLM processor using centralized Groq client."""
        try:
            # Load LLM configuration
            config_path = "config/llm_config.json"
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config_data = json.load(f)
                
                llm_config = config_data.get('llm', {})
                
                # Use centralized Groq client configuration
                groq_config = GroqConfig(
                    api_key=llm_config.get('api_key', ''),
                    model=llm_config.get('model', 'openai/gpt-oss-120b'),
                    temperature=llm_config.get('temperature', 0.1),
                    max_tokens=llm_config.get('max_tokens', 10000),
                    timeout=llm_config.get('timeout', 30),
                    max_retries=llm_config.get('max_retries', 3),
                    rate_limit_delay=llm_config.get('rate_limit_delay', 1.0)
                )
                
                # Create Groq client instance using centralized client
                groq_client = GroqStructuredClient(groq_config)
                
                processor_config = LinkedInProcessorConfig(
                    batch_size=10,  # Production batch size
                    max_workers=3,  # Production workers
                    delay_between_batches=1.0,  # Production delay
                    enable_parallel_processing=True,  # Enable parallel processing
                    use_processed_description=True
                )
                
                # Initialize LinkedIn processor with the centralized Groq client
                self.linkedin_llm_processor = LinkedInJobProcessor(groq_config, processor_config)
                print("✅ LinkedIn LLM processor initialized with centralized Groq client")
                
                # Initialize Indeed processor with the same configuration
                indeed_config = IndeedProcessorConfig(
                    batch_size=10,  # Production batch size
                    max_workers=3,  # Production workers
                    delay_between_batches=1.0,  # Production delay
                    enable_parallel_processing=True,  # Enable parallel processing
                    use_processed_description=True
                )
                
                self.indeed_llm_processor = IndeedJobProcessor(groq_config, indeed_config)
                print("✅ Indeed LLM processor initialized with centralized Groq client")
            else:
                print("⚠️ LLM config file not found, LLM processing disabled")
                self.linkedin_llm_processor = None
                self.indeed_llm_processor = None
                
        except Exception as e:
            print(f"❌ Error initializing LLM processors: {str(e)}")
            self.linkedin_llm_processor = None
            self.indeed_llm_processor = None
    
    def load_linkedin_data(self) -> pd.DataFrame:
        """Load and concatenate all CSV files from the LinkedIn folder."""
        all_dfs = []
        if not os.path.exists(self.linkedin_folder):
            print(f"⚠️ Folder {self.linkedin_folder} does not exist")
            return pd.DataFrame()
        
        csv_files = [f for f in os.listdir(self.linkedin_folder) if f.endswith('.csv')]
        if not csv_files:
            print(f"⚠️ No CSV files found in {self.linkedin_folder}")
            return pd.DataFrame()
        
        for filename in csv_files:
            file_path = os.path.join(self.linkedin_folder, filename)
            try:
                df = pd.read_csv(file_path)
                all_dfs.append(df)
                print(f"✅ LinkedIn {filename}: {len(df)} rows loaded")
            except Exception as e:
                print(f"❌ Error loading {filename}: {str(e)}")
        
        if all_dfs:
            combined_df = pd.concat(all_dfs, ignore_index=True)
            print(f"📊 LinkedIn: {len(combined_df)} rows from {len(all_dfs)} files")
            return combined_df
        else:
            return pd.DataFrame()
    
    def load_indeed_data(self) -> pd.DataFrame:
        """Load and concatenate all CSV files from the Indeed folder."""
        all_dfs = []
        if not os.path.exists(self.indeed_folder):
            print(f"⚠️ Folder {self.indeed_folder} does not exist")
            return pd.DataFrame()
        
        csv_files = [f for f in os.listdir(self.indeed_folder) if f.endswith('.csv')]
        if not csv_files:
            print(f"⚠️ No CSV files found in {self.indeed_folder}")
            return pd.DataFrame()
        
        for filename in csv_files:
            file_path = os.path.join(self.indeed_folder, filename)
            try:
                df = pd.read_csv(file_path)
                all_dfs.append(df)
                print(f"✅ Indeed {filename}: {len(df)} rows loaded")
            except Exception as e:
                print(f"❌ Error loading {filename}: {str(e)}")
        
        if all_dfs:
            combined_df = pd.concat(all_dfs, ignore_index=True)
            print(f"📊 Indeed: {len(combined_df)} rows from {len(all_dfs)} files")
            return combined_df
        else:
            return pd.DataFrame()
    
    def extract_years_of_experience(self, text):
        """Extract years of experience from job description or title."""
        if pd.isna(text):
            return ""
        
        text_lower = text.lower()
        
        # More specific regex patterns to avoid dates and other numbers
        experience_patterns = [
            r"(?:at least|minimum|over|more than|with|have|require[sd]?|need|must have)\s*(\d+)[\s]*(?:\+|plus)?\s*(?:[-–—to]\s*(\d+)\s*)?years?\s*(?:of\s*)?(?:experience|exp)\b",
            r"\b(\d+)[\s]*(?:\+|plus)?\s*(?:[-–—to]\s*(\d+)\s*)?years?\s*(?:of\s*)?(?:experience|exp)\b",
            r"\b(\d+)[\s]*(?:\+|plus)?\s*(?:[-–—to]\s*(\d+)\s*)?(?:yrs?|years?)\s*(?:experience|exp)\b"
        ]
        
        all_numbers = []
        
        for pattern in experience_patterns:
            matches = re.findall(pattern, text_lower, re.IGNORECASE)
            
            for match in matches:
                if match[0]:  # First number
                    try:
                        num = int(match[0])
                        if 0 <= num <= 3:  # Only entry-level (0-3 years)
                            all_numbers.append(num)
                    except ValueError:
                        continue
                if match[1]:  # Second number (in range)
                    try:
                        num = int(match[1])
                        if 0 <= num <= 3:  # Only entry-level
                            all_numbers.append(num)
                    except ValueError:
                        continue
        
        if not all_numbers:
            return ""
        
        # Always return the minimum requirement (entry-level focus)
        return str(min(all_numbers))
    
    def filter_senior_roles_linkedin(self, df):
        """Filter out senior roles from LinkedIn data based on job_seniority column."""
        if df.empty:
            return df
        
        initial_count = len(df)
        
        # First check if we have the seniority column
        if 'job_seniority_level' in df.columns:
            seniority_col = 'job_seniority_level'
        elif 'job_seniority' in df.columns:
            seniority_col = 'job_seniority'
        else:
            print("⚠️ No seniority column found, applying title-based filtering only")
            return self.filter_by_job_title(df, 'LinkedIn')
        
        # Define roles to keep (junior/entry level roles)
        keep_roles = ['Associate', 'Entry level', 'Internship', 'Not Applicable']
        
        # Show current seniority distribution for debugging
        if seniority_col in df.columns:
            seniority_counts = df[seniority_col].value_counts()
            print(f"🔍 LinkedIn seniority distribution: {dict(seniority_counts)}")
        
        # Filter to keep only junior roles
        filtered_df = df[df[seniority_col].isin(keep_roles)].copy()
        
        print(f"🔍 LinkedIn seniority filtering: {initial_count} → {len(filtered_df)} jobs (removed {initial_count - len(filtered_df)} senior roles)")
        
        # Additional title-based filtering for LinkedIn
        if not filtered_df.empty:
            filtered_df = self.filter_by_job_title(filtered_df, 'LinkedIn')
        
        return filtered_df
    
    def filter_senior_roles_indeed(self, df):
        """Filter out senior roles from Indeed data based on job title."""
        if df.empty:
            return df
        
        # Apply title-based filtering
        filtered_df = self.filter_by_job_title(df, 'Indeed')
        
        return filtered_df
    
    def filter_by_job_title(self, df, source_name):
        """Filter jobs based on job title keywords - comprehensive filtering."""
        if df.empty or 'job_title' not in df.columns:
            return df
        
        # Comprehensive list of keywords to exclude based on reference
        KEYWORDS_TO_EXCLUDE = [
            'Senior', 'Lead', 'Chief', 'CEO', 'CTO', 'CFO', 'COO', 'Director', 'Executive', 'Principal', 'Architect',
            'Supervisor', 'VP', 'Vice President', 'Contract', 'Contractor', 'Part-time',
            'mid-level', 'Clearance', 'Specialist', 'Consultant', 'Worker', 'Technician',
            'Advisor', 'Administrative', 'Clerk', 'teacher', 'tutor', 'educator',
            'instructor', 'professor', 'faculty', 'representative', 'Shift', 'Laborer',
            'Rep', 'operator', 'handler', 'inspector', 'Head', 'Apprentice',
            'lister', 'officer', 'writer', 'advisor', 'Banker', 'training', 'instruct',
            'staff', 'Facilitator', 'Appraiser', 'Auditor', 'Nurse', 'Representative',
            'Radiologist', 'Dealer', 'Sales', 'Counselor', 'Broker', 'Archaeologist',
            'agent', 'Agency', 'Floral', 'Interior', 'nails', 'part-time', 'therapist',
            'contract', 'certified', 'inn', 'restaurant', 'Sr.', 'Sr', 'coordinator', 'Buyer',
            'Bartender', 'cleaner', 'Cook', 'Software Engineer III', 'Data Engineer III'
        ]
        
        # PM role keywords - these should be kept even if they contain excluded keywords
        # BUT only if they don't have senior prefixes
        PM_KEYWORDS = ['product manager', 'project manager', 'program manager']
        
        def should_keep_job(row):
            job_title = str(row.get('job_title', '')).lower()
            
            # Check if it's a PM role (exception) BUT not senior
            for pm_keyword in PM_KEYWORDS:
                if pm_keyword in job_title:
                    # Check if it has senior prefixes - if so, exclude it
                    senior_prefixes = ['senior', 'sr.', 'sr ', 'lead', 'principal', 'director']
                    has_senior_prefix = any(prefix in job_title for prefix in senior_prefixes)
                    if not has_senior_prefix:
                        return True
                    else:
                        return False  # It's a senior PM role, exclude it
            
            # Check if it contains any excluded keywords
            for keyword in KEYWORDS_TO_EXCLUDE:
                if keyword.lower() in job_title:
                    return False
            
            return True
        
        # Apply filtering
        initial_count = len(df)
        mask = df.apply(should_keep_job, axis=1)
        filtered_df = df[mask].copy()
        
        print(f"🔍 {source_name} title filtering: {initial_count} → {len(filtered_df)} jobs (removed {initial_count - len(filtered_df)} excluded roles)")
        
        return filtered_df
    
    def process_linkedin_jobs(self, df):
        """Process LinkedIn job DataFrame with selected columns only."""
        if df.empty:
            return df
        
        # Create a copy to avoid modifying original
        df = df.copy()
        
        # 1. Extract keyword from discovery_input
        if 'discovery_input' in df.columns:
            df['keyword'] = df['discovery_input'].apply(
                lambda x: json.loads(x).get('keyword') if pd.notna(x) and isinstance(x, str) else None
            )
        else:
            df['keyword'] = None

        # 2. Fill missing apply_link with url
        if 'apply_link' in df.columns and 'url' in df.columns:
            df['apply_link'] = df['apply_link'].replace('', np.nan)
            df['apply_link'] = df['apply_link'].fillna(df['url'])
        elif 'url' in df.columns:
            df['apply_link'] = df['url']

        # 3. Improved apply_link handling - use URL if apply_link is empty
        if 'apply_link' in df.columns and 'url' in df.columns:
            df['apply_link'] = df['apply_link'].replace('', np.nan)
            df['apply_link'] = df['apply_link'].fillna(df['url'])
        elif 'url' in df.columns:
            df['apply_link'] = df['url']
        
        # 4. Add external apply detection for redirect optimization
        def is_external_apply(apply_link):
            if pd.isna(apply_link) or apply_link == '':
                return False
            apply_link_str = str(apply_link).lower()
            # Check for external apply indicators in the URL
            external_indicators = ['external', 'redirect', 'applystart', 'indeed.com/applystart']
            return any(indicator in apply_link_str for indicator in external_indicators)
        
        df['is_external_apply'] = df['apply_link'].apply(is_external_apply)

        # 6. Enhanced job filtering (remove non-tech roles)
        print("🔍 Applying enhanced job filtering...")
        df = self.job_filter.filter_dataframe(df, 'job_title', 'job_description')

        # 7. Filter out senior roles (legacy method)
        df = self.filter_senior_roles_linkedin(df)

        # 8. Remove duplicates using job_posting_id
        df = self.remove_linkedin_duplicates(df)

        # 9. Add source column
        df['source'] = 'LinkedIn'

        # 10. Create standardized columns first
        df['job_description'] = df['job_summary']  # Map job_summary to job_description

        # 11. Process job descriptions (3-line summaries) - AFTER mapping
        print("📝 Processing job descriptions...")
        df = self.job_summarizer.process_job_descriptions(df, 'job_description')

        # 12. Initialize empty columns for future LLM processing
        print("🔧 Initializing columns for future LLM processing...")
        df['work_mode'] = 'Not Specified'
        df['authorization'] = 'Not Specified'
        df['qualifications'] = ''

        # 14. LLM Processing for LinkedIn jobs (Batch Workflow with Redirection)
        if self.linkedin_llm_processor:
            print("🤖 Processing LinkedIn jobs with LLM (Batch Workflow with Redirection)...")
            df = self.process_linkedin_jobs_with_redirection(df, batch_size=100)
        else:
            print("⚠️ LLM processor not available, skipping LLM processing")
        df['employment_type'] = df['job_employment_type']  # Map to standard name
        df['company_industry'] = df['job_industries']  # Map to standard name
        
        # Fix salary extraction for LinkedIn
        def extract_linkedin_salary(base_salary):
            if pd.isna(base_salary) or base_salary == '':
                return ''
            try:
                if isinstance(base_salary, str) and base_salary.startswith('{'):
                    import json
                    salary_data = json.loads(base_salary)
                    min_amt = salary_data.get('min_amount')
                    max_amt = salary_data.get('max_amount')
                    currency = salary_data.get('currency', 'USD')
                    period = salary_data.get('payment_period', 'year')
                    
                    # If all values are null, return empty string instead of JSON
                    if not min_amt and not max_amt:
                        return ''
                    
                    if min_amt and max_amt:
                        return f"${min_amt:,.0f} - ${max_amt:,.0f} {currency}/{period}"
                    elif min_amt:
                        return f"${min_amt:,.0f} {currency}/{period}"
                    elif max_amt:
                        return f"Up to ${max_amt:,.0f} {currency}/{period}"
                return str(base_salary)
            except:
                return str(base_salary)
        
        df['salary'] = df['base_salary'].apply(extract_linkedin_salary)
        # Replace NaN with empty string for LinkedIn salary
        df['salary'] = df['salary'].fillna('').astype(str)
        df['salary'] = df['salary'].replace('nan', '')
        df['job_seniority'] = df['job_seniority_level']  # Map to standard name
        df['company_link'] = df['company_url']  # Map to standard name
        
        # Use extracted data (work_mode, authorization, qualifications already processed)
        # Remove the old hardcoded extraction method calls
        
        # Fix timestamp mapping for LinkedIn - use job_posted_date as the main timestamp
        df['timestamp'] = df['job_posted_date']  # Map job_posted_date to timestamp for consistency
        
        # Add platform-specific columns for job posting
        df['company_logo'] = df.get('company_logo', '')  # Company logo for platform
        df['job_posting_id'] = df.get('job_posting_id', '')  # Job posting ID
        
        # Remove is_external_apply from final output (used only for redirect processing)
        if 'is_external_apply' in df.columns:
            df = df.drop(columns=['is_external_apply'])
        
        # 16. Select columns in the exact order from your table (simplified)
        linkedin_columns = [
            'job_title',             # job title
            'company_name',          # company name
            'apply_link',            # apply link
            'job_description',       # job describe
            'job_location',          # job location
            'employment_type',       # Employment type
            'company_industry',      # Company Industry
            'salary',                # salary
            'job_seniority',         # Job seniority
            'company_link',          # Company_link
            'work_mode',             # work mode (for future LLM processing)
            'authorization',         # authorization (for future LLM processing)
            'keyword',               # keyword
            'qualifications',        # qualifications (for future LLM processing)
            'job_posted_date',       # timestamp
            'company_logo',          # Company logo
            'job_posting_id',        # Job posting ID
            'timestamp',             # timestamp (mapped from job_posted_date)
            # Additional processed columns for analysis
            'processed_description', # Preprocessed job description (for future LLM processing)
            # LLM processing results
            'job_decision',          # KEEP/REJECT/ERROR
            'job_quality_score',     # 0-100 flexible score
            'llm_work_mode',         # On-site/Hybrid/Remote/Not Sure
            'llm_authorization',     # H1B Possible/Available/No Authorization/Not Sure
            'llm_qualifications',    # 3 bullet points
            'llm_reason',            # Explanation for decision
            'llm_is_professional',   # Boolean
            'llm_is_entry_level',    # Boolean
            'llm_is_relevant'        # Boolean
        ]
        
        # Filter to only include columns that exist in the dataframe
        available_columns = [col for col in linkedin_columns if col in df.columns]
        df = df[available_columns]
        
        print(f"📊 LinkedIn output: Selected {len(available_columns)} columns from {len(linkedin_columns)} specified")
        print(f"📋 Selected columns: {', '.join(available_columns)}")

        return df

    def process_linkedin_jobs_with_redirection(self, df: pd.DataFrame, batch_size: int = 100) -> pd.DataFrame:
        """
        Process LinkedIn jobs in batches with integrated redirection.
        
        Workflow:
        1. Process batch with LLM
        2. Extract redirections for KEPT jobs only
        3. Move to next batch
        
        Args:
            df: DataFrame containing LinkedIn job data
            batch_size: Number of jobs to process in each batch
            
        Returns:
            DataFrame with LLM assessment columns and redirections for KEPT jobs
        """
        if df.empty:
            logger.warning("Empty DataFrame provided for LinkedIn batch processing")
            return df
        
        start_time = time.time()
        total_jobs = len(df)
        logger.info(f"🚀 Starting LinkedIn batch processing with redirection: {total_jobs} jobs (batch_size={batch_size})")
        
        try:
            all_results = []
            processed_count = 0
            
            for i in range(0, total_jobs, batch_size):
                batch_end = min(i + batch_size, total_jobs)
                batch_df = df.iloc[i:batch_end].copy()
                batch_num = (i // batch_size) + 1
                total_batches = (total_jobs + batch_size - 1) // batch_size
                
                logger.info(f"📦 Processing batch {batch_num}/{total_batches} ({len(batch_df)} jobs)")
                
                # Step 1: Process batch with LLM
                logger.info(f"🤖 Step 1: LLM processing for batch {batch_num}")
                if self.linkedin_llm_processor.processor_config.enable_parallel_processing and len(batch_df) > 20:
                    batch_results = self.linkedin_llm_processor._process_parallel(batch_df)
                else:
                    batch_results = self.linkedin_llm_processor._process_sequential(batch_df)
                
                # Add assessment columns to batch
                batch_with_assessments = self.linkedin_llm_processor._add_assessment_columns(batch_df, batch_results)
                
                # Step 2: Extract redirections for KEPT jobs only
                if self.extract_redirects and self.redirect_handler:
                    kept_jobs = batch_with_assessments[batch_with_assessments['job_decision'] == 'KEEP']
                    if not kept_jobs.empty:
                        logger.info(f"🔄 Step 2: Extracting redirections for {len(kept_jobs)} KEPT jobs in batch {batch_num}")
                        kept_jobs_with_redirects = self.redirect_handler.extract_final_links(kept_jobs)
                        
                        # Update the batch with redirections
                        for idx, row in kept_jobs_with_redirects.iterrows():
                            if idx in batch_with_assessments.index:
                                batch_with_assessments.loc[idx, 'apply_link'] = row['apply_link']
                    else:
                        logger.info(f"ℹ️ No KEPT jobs in batch {batch_num}, skipping redirection")
                
                all_results.append(batch_with_assessments)
                processed_count += len(batch_df)
                
                # Log progress
                kept_in_batch = len(batch_with_assessments[batch_with_assessments['job_decision'] == 'KEEP'])
                rejected_in_batch = len(batch_with_assessments[batch_with_assessments['job_decision'] == 'REJECT'])
                error_in_batch = len(batch_with_assessments[batch_with_assessments['job_decision'] == 'ERROR'])
                
                logger.info(f"✅ Batch {batch_num} completed: {kept_in_batch} KEPT, {rejected_in_batch} REJECTED, {error_in_batch} ERRORS")
                logger.info(f"📊 Progress: {processed_count}/{total_jobs} jobs processed")
                
                # Delay between batches to respect rate limits
                if batch_num < total_batches and self.linkedin_llm_processor.processor_config.delay_between_batches > 0:
                    time.sleep(self.linkedin_llm_processor.processor_config.delay_between_batches)
            
            # Combine all batch results
            final_df = pd.concat(all_results, ignore_index=True)
            
            # Log final results
            kept_jobs = len(final_df[final_df['job_decision'] == 'KEEP'])
            rejected_jobs = len(final_df[final_df['job_decision'] == 'REJECT'])
            error_jobs = len(final_df[final_df['job_decision'] == 'ERROR'])
            
            processing_time = time.time() - start_time
            logger.info(f"🎉 LinkedIn batch processing with redirection completed!")
            logger.info(f"📊 Final Results: {kept_jobs} KEPT, {rejected_jobs} REJECTED, {error_jobs} ERRORS")
            logger.info(f"⏱️ Total processing time: {processing_time:.2f} seconds")
            
            return final_df
            
        except Exception as e:
            logger.error(f"❌ Error in LinkedIn batch processing with redirection: {e}")
            raise
    
    def process_indeed_jobs(self, df):
        """Process Indeed job DataFrame with same preprocessing as LinkedIn."""
        if df.empty:
            return df
        
        # Create a copy to avoid modifying original
        df = df.copy()
        
        # 1. Extract keyword from discovery_input
        if 'discovery_input' in df.columns:
            df['keyword'] = df['discovery_input'].apply(
                lambda x: json.loads(x).get('keyword_search') if pd.notna(x) and isinstance(x, str) else None
            )
        else:
            df['keyword'] = None

        # 2. Fill missing apply_link with url
        if 'apply_link' in df.columns and 'url' in df.columns:
            df['apply_link'] = df['apply_link'].replace('', np.nan)
            df['apply_link'] = df['apply_link'].fillna(df['url'])
        elif 'url' in df.columns:
            df['apply_link'] = df['url']

        # 3. Improved apply_link handling - use URL if apply_link is empty
        if 'apply_link' in df.columns and 'url' in df.columns:
            df['apply_link'] = df['apply_link'].replace('', np.nan)
            df['apply_link'] = df['apply_link'].fillna(df['url'])
        elif 'url' in df.columns:
            df['apply_link'] = df['url']
        
        # 4. Add external apply detection for redirect optimization
        def is_external_apply(apply_link):
            if pd.isna(apply_link) or apply_link == '':
                return False
            apply_link_str = str(apply_link).lower()
            # Check for external apply indicators in the URL
            external_indicators = ['external', 'redirect', 'applystart', 'indeed.com/applystart']
            return any(indicator in apply_link_str for indicator in external_indicators)
        
        df['is_external_apply'] = df['apply_link'].apply(is_external_apply)

        # 5. Enhanced job filtering (remove non-tech roles) - SAME AS LINKEDIN
        print("🔍 Applying enhanced job filtering to Indeed jobs...")
        df = self.job_filter.filter_dataframe(df, 'job_title', 'job_description')

        # 6. Filter out senior roles (title-based filtering for Indeed)
        df = self.filter_senior_roles_indeed(df)

        # 7. Remove duplicates using jobid (Indeed equivalent of job_posting_id)
        df = self.remove_indeed_duplicates(df)

        # 8. Add source column
        df['source'] = 'Indeed'

        # 9. Create standardized columns first
        df['job_description'] = df['description_text']  # Map description_text to job_description

        # 10. Process job descriptions (3-line summaries) - SAME AS LINKEDIN
        print("📝 Processing Indeed job descriptions...")
        df = self.job_summarizer.process_job_descriptions(df, 'job_description')

        # 11. Initialize empty columns for future LLM processing
        print("🔧 Initializing columns for future LLM processing...")
        df['work_mode'] = 'Not Specified'
        df['authorization'] = 'Not Specified'
        df['qualifications'] = ''

        # 12. Standardize company names
        print("🏢 Standardizing company names...")
        df = self.standardize_company_names(df)

        # 13. LLM Processing for Indeed jobs
        if self.indeed_llm_processor:
            print("🤖 Processing Indeed jobs with LLM...")
            df = self.indeed_llm_processor.process_indeed_jobs(df, test_mode=False)
        else:
            print("⚠️ Indeed LLM processor not available, skipping LLM processing")

        # 14. Map other standardized columns
        df['employment_type'] = df['job_type']  # Map to standard name
        df['company_industry'] = ''  # Empty column for future use
        df['salary'] = df['salary_formatted']  # Map to standard name
        # Replace NaN with empty string for Indeed salary
        df['salary'] = df['salary'].fillna('').astype(str)
        df['salary'] = df['salary'].replace('nan', '')
        df['benefit'] = df['benefits']  # Map benefits to benefit
        df['timestamp'] = df['date_posted_parsed']  # Map to standard name
        
        # Fix timestamp mapping for Indeed - also map to job_posted_date for consistency
        df['job_posted_date'] = df['date_posted_parsed']  # Map date_posted_parsed to job_posted_date for consistency
        df['timestamp'] = df['date_posted_parsed']  # Also ensure timestamp is properly set
        
        # Map LLM-processed columns if available
        if 'llm_job_seniority' in df.columns:
            df['job_seniority'] = df['llm_job_seniority']
        else:
            df['job_seniority'] = ''  # Empty column if LLM not processed
        if 'llm_work_mode' in df.columns:
            df['work_mode'] = df['llm_work_mode']
        if 'llm_authorization' in df.columns:
            df['authorization'] = df['llm_authorization']
        # Keep both NLTK qualifications and LLM qualifications separate
        if 'llm_qualifications' not in df.columns:
            df['llm_qualifications'] = [[] for _ in range(len(df))]  # Empty array if LLM not processed
        
        # Add platform-specific columns for job posting
        df['company_logo'] = df.get('logo_url', '')  # Company logo for platform
        df['job_posting_id'] = df.get('jobid', '')  # Job posting ID
        df['company_link'] = df.get('company_link', '')  # Company link
        df['job_function'] = ''  # Empty for Indeed
        df['job_employment_type'] = df.get('job_type', '')  # Employment type details
        df['job_base_pay_range'] = df.get('salary_formatted', '')  # Pay range details
        df['job_posted_time'] = df.get('date_posted_parsed', '')  # Posted time
        df['job_num_applicants'] = ''  # Empty for Indeed
        df['job_description_formatted'] = df.get('job_description_formatted', '')  # Formatted description
        df['is_external_apply'] = df.get('is_external_apply', False)  # External apply flag
        
        # 15. Select columns in the exact order from your table
        indeed_columns = [
            'job_title',             # job title
            'company_name',          # company name (standardized)
            'apply_link',            # apply link
            'job_description',       # job description (processed)
            'job_location',          # job location
            'employment_type',       # Employment type
            'company_industry',      # Company Industry
            'salary',                # salary
            'job_seniority',         # Job seniority (LLM processed)
            'company_link',          # Company_link
            'work_mode',             # work mode (LLM processed)
            'authorization',         # authorization (LLM processed)
            'keyword',               # keyword
            'qualifications',        # qualifications (NLTK processing)
            'benefit',               # benefit
            'job_posted_date',       # timestamp (mapped from date_posted_parsed)
            # Platform-specific columns
            'company_logo',          # Company logo
            'job_posting_id',        # Job posting ID
            'timestamp',             # timestamp (single timestamp column)
            # LLM assessment columns
            'job_decision',          # LLM decision (KEEP/REJECT)
            'job_quality_score',     # LLM quality score
            'llm_work_mode',         # LLM work mode
            'llm_authorization',     # LLM authorization
            'llm_job_seniority',     # LLM job seniority
            'llm_qualifications',    # LLM qualifications (array)
            'llm_reason',            # LLM reason
            'llm_is_professional',   # LLM is professional
            'llm_is_entry_level',    # LLM is entry level
            'llm_is_relevant'        # LLM is relevant
        ]
        
        # Filter to only include columns that exist in the dataframe
        available_columns = [col for col in indeed_columns if col in df.columns]
        df = df[available_columns]
        
        print(f"📊 Indeed output: Selected {len(available_columns)} columns from {len(indeed_columns)} specified")
        print(f"📋 Selected columns: {', '.join(available_columns)}")

        return df
    
    def remove_indeed_duplicates(self, df):
        """Remove duplicate Indeed jobs using jobid."""
        if df.empty:
            return df
        
        initial_count = len(df)
        
        # Remove duplicates based on jobid (Indeed's unique identifier)
        if 'jobid' in df.columns:
            df_no_duplicates = df.drop_duplicates(subset=['jobid'], keep='first')
            print(f"🔍 Indeed duplicate removal: {initial_count} → {len(df_no_duplicates)} jobs (removed {initial_count - len(df_no_duplicates)} duplicates)")
        else:
            print("⚠️ No jobid column found for Indeed duplicate removal")
            df_no_duplicates = df
        
        return df_no_duplicates
    
    def standardize_company_names(self, df):
        """Standardize company names for better consistency."""
        if df.empty or 'company_name' not in df.columns:
            return df
        
        # Company name standardization rules
        company_mappings = {
            'amazon.com services.llc': 'Amazon',
            'amazon web services': 'Amazon',
            'amazon.com': 'Amazon',
            'amazon': 'Amazon',
            'google llc': 'Google',
            'google': 'Google',
            'microsoft corporation': 'Microsoft',
            'microsoft': 'Microsoft',
            'apple inc.': 'Apple',
            'apple': 'Apple',
            'meta platforms inc.': 'Meta',
            'meta': 'Meta',
            'facebook': 'Meta',
            'netflix inc.': 'Netflix',
            'netflix': 'Netflix',
            'tesla inc.': 'Tesla',
            'tesla': 'Tesla'
        }
        
        def standardize_company_name(company_name):
            if pd.isna(company_name) or company_name == '':
                return company_name
            
            company_lower = str(company_name).lower().strip()
            
            # Check for exact matches first
            for old_name, new_name in company_mappings.items():
                if company_lower == old_name:
                    return new_name
            
            # Check for partial matches
            for old_name, new_name in company_mappings.items():
                if old_name in company_lower:
                    return new_name
            
            return company_name
        
        initial_count = len(df)
        df['company_name'] = df['company_name'].apply(standardize_company_name)
        
        # Count changes
        changes = 0
        for old_name, new_name in company_mappings.items():
            if old_name != new_name:
                changes += len(df[df['company_name'] == new_name])
        
        if changes > 0:
            print(f"🏢 Company name standardization: {changes} companies renamed")
        
        return df
    
    def remove_linkedin_duplicates(self, df):
        """Remove duplicate LinkedIn jobs using job_posting_id."""
        if df.empty:
            return df
        
        initial_count = len(df)
        
        # Check if job_posting_id column exists
        if 'job_posting_id' not in df.columns:
            print("⚠️ No job_posting_id column found for LinkedIn duplicate removal")
            return df
        
        # Remove duplicates based on job_posting_id, keeping the first occurrence
        df_no_duplicates = df.drop_duplicates(subset=['job_posting_id'], keep='first')
        
        removed_count = initial_count - len(df_no_duplicates)
        print(f"🔍 LinkedIn duplicate removal: {initial_count} → {len(df_no_duplicates)} jobs (removed {removed_count} duplicates)")
        
        return df_no_duplicates
    
    def combine_datasets(self, linkedin_df, indeed_df):
        """Combine LinkedIn and Indeed datasets into a unified format."""
        combined_dfs = []
        
        if not linkedin_df.empty:
            combined_dfs.append(linkedin_df)
            print(f"📊 LinkedIn processed: {len(linkedin_df)} rows")
        
        if not indeed_df.empty:
            combined_dfs.append(indeed_df)
            print(f"📊 Indeed processed: {len(indeed_df)} rows")
        
        if combined_dfs:
            combined_df = pd.concat(combined_dfs, ignore_index=True, sort=False)
            print(f"🔗 Combined dataset: {len(combined_df)} total rows")
            return combined_df
        else:
            print("⚠️ No data to combine")
            return pd.DataFrame()
    
    def add_hardcoded_extraction(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add hardcoded data extraction for work mode, authorization, etc.
        
        Args:
            df: DataFrame containing job data
            
        Returns:
            DataFrame with extracted data added
        """
        if df.empty:
            return df
        
        print(f"🔧 Adding hardcoded data extraction to {len(df)} jobs...")
        
        # Initialize columns if they don't exist
        if 'work_mode' not in df.columns:
            df['work_mode'] = ''
        if 'authorization' not in df.columns:
            df['authorization'] = ''
        if 'qualifications' not in df.columns:
            df['qualifications'] = ''
        if 'benefit' not in df.columns:
            df['benefit'] = ''
        
        # Extract work mode from job description
        df['work_mode'] = df.apply(self.extract_work_mode, axis=1)
        
        # Extract authorization requirements
        df['authorization'] = df.apply(self.extract_authorization, axis=1)
        
        # Extract basic qualifications
        df['qualifications'] = df.apply(self.extract_basic_qualifications, axis=1)
        
        print(f"✅ Hardcoded data extraction completed")
        return df
    
    def extract_work_mode(self, row) -> str:
        """Extract work mode from job description using hardcoded patterns."""
        description = str(row.get('job_description', '')).lower()
        location = str(row.get('job_location', '')).lower()
        
        # Check for remote work indicators
        remote_keywords = ['remote', 'work from home', 'wfh', 'virtual', 'telecommute', 'distributed']
        if any(keyword in description for keyword in remote_keywords):
            return 'Remote'
        
        # Check for hybrid work indicators
        hybrid_keywords = ['hybrid', 'flexible', 'part remote', 'part time remote', 'mix of remote']
        if any(keyword in description for keyword in hybrid_keywords):
            return 'Hybrid'
        
        # Check for on-site indicators
        onsite_keywords = ['on-site', 'onsite', 'in office', 'in-person', 'office based']
        if any(keyword in description for keyword in onsite_keywords):
            return 'On-site'
        
        # Check location for remote indicators
        if 'remote' in location or 'anywhere' in location:
            return 'Remote'
        
        return 'Not Specified'
    
    def extract_authorization(self, row) -> str:
        """Extract work authorization requirements using hardcoded patterns."""
        description = str(row.get('job_description', '')).lower()
        
        # Check for US citizen requirements
        citizen_keywords = ['us citizen', 'u.s. citizen', 'citizen', 'must be a citizen']
        if any(keyword in description for keyword in citizen_keywords):
            return 'US Citizen Required'
        
        # Check for green card requirements
        green_card_keywords = ['green card', 'permanent resident', 'pr status']
        if any(keyword in description for keyword in green_card_keywords):
            return 'Green Card Required'
        
        # Check for visa sponsorship
        visa_keywords = ['visa sponsorship', 'h1b', 'sponsor visa', 'work visa']
        if any(keyword in description for keyword in visa_keywords):
            return 'Visa Sponsorship Available'
        
        # Check for no sponsorship
        no_sponsor_keywords = ['no sponsorship', 'no visa', 'no h1b', 'citizens only']
        if any(keyword in description for keyword in no_sponsor_keywords):
            return 'No Visa Sponsorship'
        
        return 'Not Specified'
    
    def extract_basic_qualifications(self, row) -> str:
        """Extract basic qualifications using hardcoded patterns."""
        description = str(row.get('job_description', '')).lower()
        qualifications = []
        
        # Check for degree requirements
        if 'bachelor' in description or 'bachelor\'s' in description:
            qualifications.append('Bachelor\'s degree')
        elif 'master' in description or 'master\'s' in description:
            qualifications.append('Master\'s degree')
        elif 'phd' in description or 'doctorate' in description:
            qualifications.append('PhD/Doctorate')
        
        # Check for experience requirements
        if '0-2 years' in description or 'entry level' in description:
            qualifications.append('0-2 years experience')
        elif '2-4 years' in description:
            qualifications.append('2-4 years experience')
        
        # Check for specific skills
        if 'python' in description:
            qualifications.append('Python programming')
        elif 'javascript' in description:
            qualifications.append('JavaScript programming')
        elif 'java' in description:
            qualifications.append('Java programming')
        
        return '; '.join(qualifications[:3]) if qualifications else 'Not Specified'
    
    def save_combined_data(self, df):
        """Save the combined dataset to CSV files."""
        if df.empty:
            print("⚠️ No data to save")
            return
        
        # Generate timestamp for filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save complete dataset
        complete_filename = f"combined_jobs.csv"
        complete_path = os.path.join(self.output_folder, complete_filename)
        
        # Ensure salary column is properly handled for CSV
        if 'salary' in df.columns:
            df['salary'] = df['salary'].fillna('').astype(str)
            df['salary'] = df['salary'].replace('nan', '')
        
        df.to_csv(complete_path, index=False, na_rep='')
        print(f"💾 Complete dataset saved: {complete_path}")
        
        # Save by source with cleaned columns
        if 'source' in df.columns:
            for source in df['source'].unique():
                source_df = df[df['source'] == source].copy()
                
                # Clean empty columns (columns with all NaN/empty values)
                source_df = source_df.dropna(axis=1, how='all')
                
                # Also remove columns that are all empty strings
                for col in source_df.columns:
                    if source_df[col].dtype == 'object':  # String columns
                        if source_df[col].fillna('').str.strip().eq('').all():
                            source_df = source_df.drop(columns=[col])
                
                # Ensure salary column is properly handled for CSV
                if 'salary' in source_df.columns:
                    source_df['salary'] = source_df['salary'].fillna('').astype(str)
                    source_df['salary'] = source_df['salary'].replace('nan', '')
                
                source_filename = f"{source.lower()}_jobs_{timestamp}.csv"
                source_path = os.path.join(self.output_folder, source_filename)
                source_df.to_csv(source_path, index=False, na_rep='')
                print(f"💾 {source} dataset saved: {source_path} ({len(source_df)} rows, {len(source_df.columns)} columns)")
        
        # Save summary statistics
        summary_filename = f"data_summary_{timestamp}.txt"
        summary_path = os.path.join(self.output_folder, summary_filename)
        
        with open(summary_path, 'w') as f:
            f.write("Job Data Processing Summary\n")
            f.write("=" * 30 + "\n\n")
            f.write(f"Processing Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Total Records: {len(df)}\n\n")
            
            if 'source' in df.columns:
                f.write("Records by Source:\n")
                source_counts = df['source'].value_counts()
                for source, count in source_counts.items():
                    f.write(f"  {source}: {count}\n")
                f.write("\n")
            
            if 'keyword' in df.columns:
                f.write("Top Keywords:\n")
                keyword_counts = df['keyword'].value_counts().head(10)
                for keyword, count in keyword_counts.items():
                    f.write(f"  {keyword}: {count}\n")
                f.write("\n")
            
            if 'work_mode' in df.columns:
                f.write("Work Mode Distribution:\n")
                work_mode_counts = df['work_mode'].value_counts()
                for mode, count in work_mode_counts.items():
                    f.write(f"  {mode}: {count}\n")
                f.write("\n")
            
            if 'work_authorization' in df.columns:
                f.write("Work Authorization Distribution:\n")
                auth_counts = df['work_authorization'].value_counts()
                for auth, count in auth_counts.items():
                    f.write(f"  {auth}: {count}\n")
                f.write("\n")
        
        print(f"📊 Summary saved: {summary_path}")
    
    def process_all_data(self):
        """Main processing function that handles the complete pipeline."""
        print("🚀 Starting job data processing...")
        print("=" * 50)
        
        # Load raw data based on mode
        print("\n📂 Loading raw data...")
        if self.linkedin_only:
            print("🔗 LinkedIn-only mode enabled")
            linkedin_raw = self.load_linkedin_data()
            indeed_raw = pd.DataFrame()  # Skip Indeed data
        elif self.indeed_only:
            print("💼 Indeed-only mode enabled")
            linkedin_raw = pd.DataFrame()  # Skip LinkedIn data
            indeed_raw = self.load_indeed_data()
        else:
            linkedin_raw = self.load_linkedin_data()
            indeed_raw = self.load_indeed_data()
        
        # Process data
        print("\n⚙️ Processing data...")
        if not linkedin_raw.empty:
            linkedin_processed = self.process_linkedin_jobs(linkedin_raw)
        else:
            linkedin_processed = pd.DataFrame()
        
        if not indeed_raw.empty:
            indeed_processed = self.process_indeed_jobs(indeed_raw)
        else:
            indeed_processed = pd.DataFrame()
        
        # Combine datasets
        print("\n🔗 Combining datasets...")
        combined_data = self.combine_datasets(linkedin_processed, indeed_processed)
        
        # Save results
        print("\n💾 Saving results...")
        self.save_combined_data(combined_data)
        
        print("\n✅ Preprocessing completed successfully!")
        print(f"📊 Final dataset contains {len(combined_data)} job records")
        
        return combined_data
