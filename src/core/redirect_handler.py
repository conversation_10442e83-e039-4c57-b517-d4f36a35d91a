"""
Redirect Handler Module

Handles extraction of final apply links from LinkedIn job postings
by following redirects from initial LinkedIn apply_link URLs.
"""

import requests
import pandas as pd
import time
import logging
from urllib.parse import urljoin, urlparse
from datetime import datetime

logger = logging.getLogger(__name__)

class RedirectHandler:
    """
    Handles redirect extraction for job apply links.
    """
    
    def __init__(self, delay_between_requests=2, max_redirects=10, timeout=15):
        """
        Initialize the redirect handler.
        
        Args:
            delay_between_requests: Seconds to wait between requests
            max_redirects: Maximum number of redirects to follow
            timeout: Request timeout in seconds
        """
        self.delay_between_requests = delay_between_requests
        self.max_redirects = max_redirects
        self.timeout = timeout
        
        # Session for connection reuse
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def get_final_url(self, url):
        """
        Follow redirects to get the final destination URL.
        
        Args:
            url: Initial apply link
            
        Returns:
            str: Final destination URL or original URL if failed
        """
        if not url or pd.isna(url) or url.strip() == '':
            return ''
        
        try:
            # Clean the URL
            url = url.strip()
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            logger.debug(f"Following redirects for: {url}")
            
            # Follow redirects
            response = self.session.get(
                url, 
                allow_redirects=True, 
                timeout=self.timeout,
                stream=True  # Don't download content, just headers
            )
            
            final_url = response.url
            
            # Check if we got a different URL
            if final_url != url:
                logger.debug(f"Redirect successful: {url} → {final_url}")
                return final_url
            else:
                logger.debug(f"No redirect: {url}")
                return url
                
        except requests.exceptions.Timeout:
            logger.warning(f"Timeout for: {url}")
            return url
        except requests.exceptions.TooManyRedirects:
            logger.warning(f"Too many redirects for: {url}")
            return url
        except requests.exceptions.RequestException as e:
            logger.warning(f"Request failed for {url}: {str(e)}")
            return url
        except Exception as e:
            logger.error(f"Unexpected error for {url}: {str(e)}")
            return url
    
    def extract_final_links(self, df, apply_link_column='apply_link', external_apply_column='is_external_apply'):
        """
        Extract final apply links for jobs with external apply only.
        
        Args:
            df: DataFrame with job data
            apply_link_column: Name of the column containing apply links
            external_apply_column: Name of the column indicating external apply
            
        Returns:
            DataFrame: Original DataFrame with added 'final_apply_link' column
        """
        if df.empty:
            logger.warning("Empty DataFrame provided")
            return df
        
        if apply_link_column not in df.columns:
            logger.error(f"Column '{apply_link_column}' not found in DataFrame")
            return df
        
        # Filter to only external apply jobs
        if external_apply_column in df.columns:
            external_jobs = df[df[external_apply_column] == True]
            logger.info(f"Found {len(external_jobs)} external apply jobs out of {len(df)} total jobs")
        else:
            external_jobs = df
            logger.warning(f"Column '{external_apply_column}' not found, processing all jobs")
        
        if external_jobs.empty:
            logger.info("No external apply jobs found, skipping redirect extraction")
            df['final_apply_link'] = df[apply_link_column]
            return df
        
        logger.info(f"Starting redirect extraction for {len(external_jobs)} external apply jobs")
        logger.info(f"Delay between requests: {self.delay_between_requests} seconds")
        
        # Initialize final_apply_link column with original apply_link
        df = df.copy()
        df['final_apply_link'] = df[apply_link_column]
        
        final_links = []
        successful_redirects = 0
        failed_redirects = 0
        
        for idx, row in external_jobs.iterrows():
            original_url = row.get(apply_link_column, '')
            
            if original_url and str(original_url).strip() != '':
                final_url = self.get_final_url(original_url)
                final_links.append(final_url)
                
                if final_url != original_url:
                    successful_redirects += 1
                    # Update the final_apply_link for this row
                    df.loc[idx, 'final_apply_link'] = final_url
                else:
                    failed_redirects += 1
            else:
                final_links.append('')
                failed_redirects += 1
            
            # Rate limiting
            if idx < len(external_jobs) - 1:  # Don't delay after the last request
                time.sleep(self.delay_between_requests)
            
            # Progress update every 10 jobs
            if (idx + 1) % 10 == 0:
                logger.info(f"Progress: {idx + 1}/{len(external_jobs)} external apply jobs processed")
        
        # Summary
        logger.info(f"Redirect extraction completed!")
        logger.info(f"Results: {successful_redirects} successful redirects, {failed_redirects} failed/no redirects")
        logger.info(f"Success rate: {(successful_redirects / len(external_jobs)) * 100:.1f}%")
        
        return df
