"""
Work Mode Extractor Module

Advanced work mode detection using enhanced pattern matching and context analysis.
"""

import re
import pandas as pd
from typing import Dict, Any, Tuple

class WorkModeExtractor:
    """
    Advanced work mode extractor with context analysis and confidence scoring.
    """
    
    def __init__(self):
        """Initialize the work mode extractor with comprehensive patterns."""
        
        # Remote work indicators with confidence scores
        self.REMOTE_PATTERNS = {
            'high_confidence': [
                r'\b(fully remote|100% remote|completely remote|entirely remote)\b',
                r'\b(work from home|wfh|remote work|remote position)\b',
                r'\b(anywhere in the world|work from anywhere|location independent)\b',
                r'\b(no office|no commute|home office|virtual office)\b',
                r'\b(remote|virtual|distributed|telecommute|telework)\b'
            ],
            'medium_confidence': [
                r'\b(flexible location|location flexible|remote friendly)\b',
                r'\b(work from home|wfh)\b',
                r'\b(remote option|remote available|remote possible)\b'
            ],
            'low_confidence': [
                r'\b(some remote|partially remote|hybrid remote)\b'
            ]
        }
        
        # Hybrid work indicators
        self.HYBRID_PATTERNS = {
            'high_confidence': [
                r'\b(hybrid|flexible hybrid|hybrid work|hybrid model)\b',
                r'\b(part remote|part time remote|mix of remote and office)\b',
                r'\b(2-3 days remote|3 days in office|flexible schedule)\b',
                r'\b(remote and office|office and remote|split time)\b'
            ],
            'medium_confidence': [
                r'\b(flexible work|flexible arrangement|flexible schedule)\b',
                r'\b(combination of remote|mix of work|blended work)\b',
                r'\b(partial remote|some days remote|occasional office)\b'
            ],
            'low_confidence': [
                r'\b(flexible|adaptable|adjustable)\b',
                r'\b(work life balance|work life integration)\b'
            ]
        }
        
        # On-site work indicators
        self.ONSITE_PATTERNS = {
            'high_confidence': [
                r'\b(on-site|onsite|in office|in-person|office based)\b',
                r'\b(not remote|no remote|office only|office based)\b',
                r'\b(required to be in office|must be in office)\b',
                r'\b(physical presence|in person attendance)\b'
            ],
            'medium_confidence': [
                r'\b(office|workplace|facility|location)\b',
                r'\b(commute|travel to office|come to office)\b',
                r'\b(relocation required|must relocate)\b'
            ],
            'low_confidence': [
                r'\b(team environment|collaborative space)\b',
                r'\b(face to face|in person meetings)\b'
            ]
        }
        
        # Location-based indicators
        self.LOCATION_PATTERNS = {
            'remote_indicators': [
                r'\b(remote|anywhere|global|worldwide|distributed)\b',
                r'\b(no location|location independent|location flexible)\b'
            ],
            'onsite_indicators': [
                r'\b(specific location|office location|workplace)\b',
                r'\b(relocation|must be located|based in)\b'
            ]
        }
        
        # Context patterns that modify confidence
        self.CONTEXT_MODIFIERS = {
            'positive': [
                r'\b(required|must|need|essential|critical)\b',
                r'\b(only|exclusively|solely|purely)\b',
                r'\b(permanent|long term|ongoing)\b'
            ],
            'negative': [
                r'\b(not|no|never|avoid|prevent)\b',
                r'\b(sometimes|occasionally|rarely|seldom)\b',
                r'\b(temporary|short term|limited)\b'
            ]
        }
    
    def extract_work_mode_from_text(self, text: str) -> Dict[str, Any]:
        """
        Extract work mode from text with confidence scoring.
        
        Args:
            text: Text to analyze (job description or location)
            
        Returns:
            Dict containing work mode and confidence details
        """
        if not text or pd.isna(text):
            return {
                'work_mode': 'Not Specified',
                'confidence': 'low',
                'reason': 'No text provided',
                'patterns_found': []
            }
        
        text_lower = text.lower()
        patterns_found = []
        confidence_scores = {'remote': 0, 'hybrid': 0, 'onsite': 0}
        
        # Check remote patterns
        for confidence_level, patterns in self.REMOTE_PATTERNS.items():
            for pattern in patterns:
                matches = re.findall(pattern, text_lower, re.IGNORECASE)
                if matches:
                    patterns_found.append(f"Remote ({confidence_level}): {pattern}")
                    if confidence_level == 'high_confidence':
                        confidence_scores['remote'] += 3
                    elif confidence_level == 'medium_confidence':
                        confidence_scores['remote'] += 2
                    else:
                        confidence_scores['remote'] += 1
        
        # Check hybrid patterns
        for confidence_level, patterns in self.HYBRID_PATTERNS.items():
            for pattern in patterns:
                matches = re.findall(pattern, text_lower, re.IGNORECASE)
                if matches:
                    patterns_found.append(f"Hybrid ({confidence_level}): {pattern}")
                    if confidence_level == 'high_confidence':
                        confidence_scores['hybrid'] += 3
                    elif confidence_level == 'medium_confidence':
                        confidence_scores['hybrid'] += 2
                    else:
                        confidence_scores['hybrid'] += 1
        
        # Check onsite patterns
        for confidence_level, patterns in self.ONSITE_PATTERNS.items():
            for pattern in patterns:
                matches = re.findall(pattern, text_lower, re.IGNORECASE)
                if matches:
                    patterns_found.append(f"On-site ({confidence_level}): {pattern}")
                    if confidence_level == 'high_confidence':
                        confidence_scores['onsite'] += 3
                    elif confidence_level == 'medium_confidence':
                        confidence_scores['onsite'] += 2
                    else:
                        confidence_scores['onsite'] += 1
        
        # Apply context modifiers
        for modifier_type, patterns in self.CONTEXT_MODIFIERS.items():
            for pattern in patterns:
                if re.search(pattern, text_lower):
                    if modifier_type == 'positive':
                        # Increase confidence for all detected modes
                        for mode in confidence_scores:
                            if confidence_scores[mode] > 0:
                                confidence_scores[mode] += 1
                    else:  # negative
                        # Decrease confidence for all detected modes
                        for mode in confidence_scores:
                            if confidence_scores[mode] > 0:
                                confidence_scores[mode] = max(0, confidence_scores[mode] - 1)
        
        # Determine work mode based on highest score
        max_score = max(confidence_scores.values())
        if max_score == 0:
            return {
                'work_mode': 'Not Specified',
                'confidence': 'low',
                'reason': 'No work mode patterns found',
                'patterns_found': patterns_found
            }
        
        # Find the mode with highest score
        if confidence_scores['remote'] == max_score:
            work_mode = 'Remote'
        elif confidence_scores['hybrid'] == max_score:
            work_mode = 'Hybrid'
        elif confidence_scores['onsite'] == max_score:
            work_mode = 'On-site'
        else:
            work_mode = 'Not Specified'
        
        # Determine confidence level
        if max_score >= 4:
            confidence = 'high'
        elif max_score >= 2:
            confidence = 'medium'
        else:
            confidence = 'low'
        
        return {
            'work_mode': work_mode,
            'confidence': confidence,
            'reason': f'Detected {work_mode} with score {max_score}',
            'patterns_found': patterns_found,
            'scores': confidence_scores
        }
    
    def extract_work_mode_from_location(self, location: str) -> Dict[str, Any]:
        """
        Extract work mode hints from job location.
        
        Args:
            location: Job location text
            
        Returns:
            Dict containing work mode hints
        """
        if not location or pd.isna(location):
            return {'work_mode': 'Not Specified', 'confidence': 'low'}
        
        location_lower = location.lower()
        
        # Check for remote indicators in location
        for pattern in self.LOCATION_PATTERNS['remote_indicators']:
            if re.search(pattern, location_lower):
                return {
                    'work_mode': 'Remote',
                    'confidence': 'high',
                    'reason': f'Location indicates remote: {location}'
                }
        
        # Check for onsite indicators in location
        for pattern in self.LOCATION_PATTERNS['onsite_indicators']:
            if re.search(pattern, location_lower):
                return {
                    'work_mode': 'On-site',
                    'confidence': 'medium',
                    'reason': f'Location indicates onsite: {location}'
                }
        
        return {'work_mode': 'Not Specified', 'confidence': 'low'}
    
    def extract_work_mode(self, job_description: str, job_location: str = "") -> Dict[str, Any]:
        """
        Extract work mode from job description and location.
        
        Args:
            job_description: Job description text
            job_location: Job location text (optional)
            
        Returns:
            Dict containing final work mode determination
        """
        # Extract from description
        desc_result = self.extract_work_mode_from_text(job_description)
        
        # Extract from location if provided
        location_result = self.extract_work_mode_from_location(job_location)
        
        # Combine results with priority to description
        if desc_result['confidence'] != 'low':
            return desc_result
        elif location_result['confidence'] != 'low':
            return location_result
        else:
            return {
                'work_mode': 'Not Specified',
                'confidence': 'low',
                'reason': 'No clear work mode indicators found',
                'patterns_found': []
            }
    
    def process_dataframe(self, df: pd.DataFrame, 
                         description_column: str = 'job_description',
                         location_column: str = 'job_location') -> pd.DataFrame:
        """
        Process a DataFrame to extract work modes.
        
        Args:
            df: DataFrame containing job data
            description_column: Name of the job description column
            location_column: Name of the job location column
            
        Returns:
            DataFrame with work mode information added
        """
        if df.empty:
            return df
        
        print(f"🔧 Extracting work modes for {len(df)} jobs...")
        
        # Initialize work mode columns
        df['work_mode'] = 'Not Specified'
        df['work_mode_confidence'] = 'low'
        df['work_mode_reason'] = ''
        df['work_mode_patterns'] = ''
        
        for idx, row in df.iterrows():
            job_description = row.get(description_column, '')
            job_location = row.get(location_column, '')
            
            try:
                result = self.extract_work_mode(job_description, job_location)
                
                df.at[idx, 'work_mode'] = result['work_mode']
                df.at[idx, 'work_mode_confidence'] = result['confidence']
                df.at[idx, 'work_mode_reason'] = result['reason']
                df.at[idx, 'work_mode_patterns'] = '; '.join(result.get('patterns_found', []))
                
            except Exception as e:
                print(f"⚠️ Error extracting work mode for job {idx}: {str(e)}")
                df.at[idx, 'work_mode'] = 'Not Specified'
                df.at[idx, 'work_mode_confidence'] = 'low'
                df.at[idx, 'work_mode_reason'] = f'Error: {str(e)}'
                df.at[idx, 'work_mode_patterns'] = ''
        
        # Print summary
        work_mode_counts = df['work_mode'].value_counts()
        print(f"✅ Work mode extraction completed:")
        for mode, count in work_mode_counts.items():
            print(f"   • {mode}: {count}")
        
        return df
