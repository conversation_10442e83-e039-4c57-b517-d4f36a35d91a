"""
Main Job Scraper Application

A comprehensive job scraping and processing system that:
1. <PERSON>rapes job data from Indeed and LinkedIn using BrightData API
2. Downloads raw data from S3
3. Processes and cleans the data
4. Extracts final apply links from LinkedIn redirects
5. Creates organized Excel sheets
6. Uploads to Google Drive
7. Clears raw data files

Usage:
    python main.py --action [scrape|fetch|process|excel|upload|clear|all]
"""

import argparse
import sys
import os
from datetime import datetime

# Add src to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.scraper import BrightDataScraper
from core.data_processor import DataProcessor
from utils.s3_handler import S3Handler
from utils.file_handler import FileHandler
from utils.logger import setup_logger, get_log_file_path

def main():
    """Main application entry point."""
    parser = argparse.ArgumentParser(description="Job Scraper Application")
    parser.add_argument(
        "--action", 
        choices=["scrape", "fetch", "process", "excel", "upload", "clear", "all"],
        default="all",
        help="Action to perform (default: all)"
    )
    parser.add_argument(
        "--config",
        default="config/scraping_inputs.json",
        help="Path to scraping configuration file"
    )
    parser.add_argument(
        "--extract-redirects",
        action="store_true",
        default=False,
        help="Extract final apply links from LinkedIn redirects"
    )
    parser.add_argument(
        "--linkedin-only",
        action="store_true",
        default=False,
        help="Process only LinkedIn jobs (skip Indeed)"
    )
    parser.add_argument(
        "--indeed-only",
        action="store_true",
        default=False,
        help="Process only Indeed jobs (skip LinkedIn)"
    )
    parser.add_argument(
        "--redirect-delay",
        type=int,
        default=2,
        help="Delay between redirect requests in seconds"
    )
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level"
    )
    parser.add_argument(
        "--clear-indeed-only",
        action="store_true",
        default=False,
        help="Clear only Indeed raw data files"
    )
    parser.add_argument(
        "--clear-linkedin-only",
        action="store_true",
        default=False,
        help="Clear only LinkedIn raw data files"
    )
    # LLM integration removed - using hardcoded methods only
    
    args = parser.parse_args()
    
    # Setup logging
    log_file = get_log_file_path("job_scraper")
    logger = setup_logger("job_scraper", args.log_level, log_file)
    
    logger.info("🚀 Job Scraper Application Started")
    logger.info(f"Action: {args.action}")
    logger.info(f"Config: {args.config}")
    logger.info(f"Extract redirects: {args.extract_redirects}")
    logger.info(f"Redirect delay: {args.redirect_delay}s")
    logger.info(f"LinkedIn only: {args.linkedin_only}")
    logger.info(f"Indeed only: {args.indeed_only}")
    
    try:
        if args.action in ["scrape", "all"]:
            scrape_jobs(args.config, logger)
        
        if args.action in ["fetch", "all"]:
            fetch_data(logger)
        
        if args.action in ["process", "all"]:
            process_data(args.extract_redirects, args.redirect_delay, args.linkedin_only, args.indeed_only, logger)
        
        if args.action in ["excel", "all"]:
            create_excel(logger)
        
        if args.action in ["upload", "all"]:
            upload_to_drive(logger)
        
        if args.action in ["clear", "all"]:
            clear_raw_data(args.clear_indeed_only, args.clear_linkedin_only, logger)
        
        logger.info("✅ Application completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Application failed: {str(e)}")
        sys.exit(1)

def scrape_jobs(config_file, logger):
    """Scrape job data using BrightData API."""
    logger.info("🎯 Starting job scraping...")
    
    try:
        scraper = BrightDataScraper(config_file)
        results = scraper.scrape_all_platforms(delay_seconds=5)
        
        # Log summary
        total_categories = sum(len(platform_results) for platform_results in results.values())
        total_successful = sum(
            sum(1 for r in platform_results.values() if 'snapshot_id' in r)
            for platform_results in results.values()
        )
        
        logger.info(f"📊 Scraping completed: {total_successful}/{total_categories} categories successful")
        
    except Exception as e:
        logger.error(f"❌ Scraping failed: {str(e)}")
        raise

def fetch_data(logger):
    """Fetch raw data from S3."""
    logger.info("📥 Fetching data from S3...")
    
    try:
        s3_handler = S3Handler()
        results = s3_handler.fetch_today_data()
        
        total_files = results['indeed'] + results['linkedin']
        logger.info(f"📊 Downloaded {total_files} files from S3")
        
    except Exception as e:
        logger.error(f"❌ Data fetching failed: {str(e)}")
        raise

def process_data(extract_redirects, redirect_delay, linkedin_only, indeed_only, logger):
    """Process and clean the job data."""
    logger.info("⚙️ Processing job data...")
    
    try:
        # Initialize data processor with LLM processing enabled
        processor = DataProcessor(
            extract_redirects=extract_redirects,
            redirect_delay=redirect_delay,
            linkedin_only=linkedin_only,
            indeed_only=indeed_only,
            use_llm_filter=True  # Enabled - using LLM processing for LinkedIn
        )
        
        combined_data = processor.process_all_data()
        
        logger.info(f"📊 Processing completed: {len(combined_data)} jobs processed")
        
        if extract_redirects and 'final_apply_link' in combined_data.columns:
            redirect_success = combined_data['final_apply_link'].notna().sum()
            logger.info(f"🔗 Redirect extraction: {redirect_success} successful extractions")
        
    except Exception as e:
        logger.error(f"❌ Data processing failed: {str(e)}")
        raise

def create_excel(logger):
    """Create Excel sheets from processed data."""
    logger.info("📊 Creating Excel sheets...")
    
    try:
        file_handler = FileHandler()
        
        # Look for the most recent combined data file
        combined_data_dir = "data/processed/combined_data"
        if not os.path.exists(combined_data_dir):
            logger.error(f"❌ Combined data directory not found: {combined_data_dir}")
            return
        
        csv_files = [f for f in os.listdir(combined_data_dir) if f.endswith('.csv')]
        if not csv_files:
            logger.error("❌ No CSV files found in combined data directory")
            return
        
        # Use the most recent file
        latest_csv = max(csv_files, key=lambda f: os.path.getctime(os.path.join(combined_data_dir, f)))
        csv_path = os.path.join(combined_data_dir, latest_csv)
        
        logger.info(f"📂 Processing file: {latest_csv}")
        
        excel_path = file_handler.process_csv_to_excel(csv_path)
        
        if excel_path:
            logger.info(f"✅ Excel file created: {excel_path}")
        else:
            logger.error("❌ Excel creation failed")
            
    except Exception as e:
        logger.error(f"❌ Excel creation failed: {str(e)}")
        raise

def upload_to_drive(logger):
    """Upload Excel file to Google Drive."""
    logger.info("📤 Uploading to Google Drive...")
    
    try:
        file_handler = FileHandler()
        
        # Look for the Excel file
        excel_dir = "data/exports/excel"
        if not os.path.exists(excel_dir):
            logger.error(f"❌ Excel directory not found: {excel_dir}")
            return
        
        excel_files = [f for f in os.listdir(excel_dir) if f.endswith('.xlsx')]
        if not excel_files:
            logger.error("❌ No Excel files found")
            return
        
        # Use the most recent file
        latest_excel = max(excel_files, key=lambda f: os.path.getctime(os.path.join(excel_dir, f)))
        excel_path = os.path.join(excel_dir, latest_excel)
        
        logger.info(f"📂 Uploading file: {latest_excel}")
        
        file_id = file_handler.upload_to_google_drive(excel_path)
        
        if file_id:
            logger.info(f"✅ Upload successful: {file_id}")
        else:
            logger.error("❌ Upload failed")
            
    except Exception as e:
        logger.error(f"❌ Upload failed: {str(e)}")
        raise

def clear_raw_data(clear_indeed_only=False, clear_linkedin_only=False, logger=None):
    """Clear raw data files for Indeed and LinkedIn."""
    logger.info("🗑️ Clearing raw data files...")
    
    try:
        # Define raw data directories
        indeed_dir = "data/raw/indeed_data"
        linkedin_dir = "data/raw/linkedin_data"
        
        cleared_files = 0
        
        # Determine what to clear based on flags
        clear_indeed = clear_indeed_only or (not clear_indeed_only and not clear_linkedin_only)
        clear_linkedin = clear_linkedin_only or (not clear_indeed_only and not clear_linkedin_only)
        
        # Clear Indeed data
        if clear_indeed and os.path.exists(indeed_dir):
            indeed_files = [f for f in os.listdir(indeed_dir) if f.endswith('.csv')]
            for file in indeed_files:
                file_path = os.path.join(indeed_dir, file)
                os.remove(file_path)
                cleared_files += 1
            logger.info(f"📊 Cleared {len(indeed_files)} Indeed files from {indeed_dir}")
        elif clear_indeed and not os.path.exists(indeed_dir):
            logger.warning(f"⚠️ Indeed directory not found: {indeed_dir}")
        
        # Clear LinkedIn data
        if clear_linkedin and os.path.exists(linkedin_dir):
            linkedin_files = [f for f in os.listdir(linkedin_dir) if f.endswith('.csv')]
            for file in linkedin_files:
                file_path = os.path.join(linkedin_dir, file)
                os.remove(file_path)
                cleared_files += 1
            logger.info(f"📊 Cleared {len(linkedin_files)} LinkedIn files from {linkedin_dir}")
        elif clear_linkedin and not os.path.exists(linkedin_dir):
            logger.warning(f"⚠️ LinkedIn directory not found: {linkedin_dir}")
        
        logger.info(f"✅ Raw data clearing completed: {cleared_files} files removed")
        
    except Exception as e:
        logger.error(f"❌ Raw data clearing failed: {str(e)}")
        raise

if __name__ == "__main__":
    main()
