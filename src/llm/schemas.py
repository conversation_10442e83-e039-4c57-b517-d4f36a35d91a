"""
Enhanced Pydantic schemas for job filtering and data extraction
"""

from pydantic import BaseModel, Field
from enum import Enum
from typing import List, Optional

class JobDecision(str, Enum):
    """Job filtering decision enum"""
    KEEP = "KEEP"
    REJECT = "REJECT"
    ERROR = "ERROR"

class WorkMode(str, Enum):
    """Work mode enum"""
    ONSITE = "On-site"
    HYBRID = "Hybrid"
    REMOTE = "Remote"
    NOT_SURE = "Not Sure"

class Authorization(str, Enum):
    """Work authorization enum"""
    H1B_POSSIBLE = "H1B Possible"
    H1B_AVAILABLE = "H1B Available"
    NO_AUTHORIZATION = "No Authorization"
    NOT_SURE = "Not Sure"

class JobSeniority(str, Enum):
    """Job seniority level enum"""
    ENTRY_LEVEL = "Entry Level"
    JUNIOR = "Junior"
    MID_LEVEL = "Mid Level"
    SENIOR = "Senior"
    LEAD = "Lead"
    PRINCIPAL = "Principal"
    DIRECTOR = "Director"
    EXECUTIVE = "Executive"
    NOT_SURE = "Not Sure"

class EmploymentType(str, Enum):
    """Employment type enum"""
    FULL_TIME = "Full-time"
    PART_TIME = "Part-time"
    INTERNSHIP = "Internship"
    CONTRACT = "Contract"
    VOLUNTEER = "Volunteer"
    TEMPORARY = "Temporary"
    NOT_SURE = "Not Sure"

class JobFilterResult(BaseModel):
    """Enhanced job filtering result with data extraction"""
    decision: JobDecision = Field(description="KEEP or REJECT")
    quality_score: int = Field(ge=0, le=100, description="Quality score 0-100")
    reason: str = Field(default="No reason provided", description="Brief explanation")
    
    # Filtering criteria
    is_professional: bool = Field(default=False, description="Is this a professional white-collar job?")
    is_entry_level: bool = Field(default=False, description="Is this entry-level (0-3 years)?")
    is_relevant: bool = Field(default=False, description="Is this job relevant to the keyword?")
    
    # Data extraction fields
    qualifications: List[str] = Field(default=[], max_items=3, description="Top 3 qualifications required")
    employment_type: EmploymentType = Field(default=EmploymentType.NOT_SURE, description="Type of employment")
    industry_keywords: List[str] = Field(default=[], max_items=5, description="4-5 industry keywords")
    
    class Config:
        use_enum_values = True

class LinkedInJobResult(BaseModel):
    """Enhanced LinkedIn job processing result with all required fields"""
    decision: JobDecision = Field(description="KEEP, REJECT, or ERROR")
    quality_score: int = Field(ge=0, le=100, description="Flexible quality score 0-100")
    reason: str = Field(description="Detailed explanation for the decision")
    
    # Work mode detection
    work_mode: WorkMode = Field(description="On-site, Hybrid, Remote, or Not Sure")
    
    # Authorization detection
    authorization: Authorization = Field(description="H1B Possible, H1B Available, No Authorization, or Not Sure")
    
    # Qualifications (3 bullet points)
    qualifications: List[str] = Field(max_items=3, description="Top 3 qualifications for the internship")
    
    # Filtering criteria
    is_professional: bool = Field(description="Is this a professional white-collar job?")
    is_entry_level: bool = Field(description="Is this entry-level (0-3 years experience)?")
    is_relevant: bool = Field(description="Is this job relevant to the search keyword?")
    
    class Config:
        use_enum_values = True

class IndeedJobResult(BaseModel):
    """Indeed job processing result with all required fields including job seniority"""
    decision: JobDecision = Field(description="KEEP, REJECT, or ERROR")
    quality_score: int = Field(ge=0, le=100, description="Flexible quality score 0-100")
    reason: str = Field(description="Detailed explanation for the decision")
    
    # Work mode detection (using job_location + description)
    work_mode: WorkMode = Field(description="On-site, Hybrid, Remote, or Not Sure")
    
    # Authorization detection
    authorization: Authorization = Field(description="H1B Possible, H1B Available, No Authorization, or Not Sure")
    
    # Job seniority extraction (new for Indeed)
    job_seniority: JobSeniority = Field(description="Entry Level, Junior, Mid Level, Senior, Lead, Principal, Director, Executive, or Not Sure")
    
    # Qualifications (3 bullet points)
    qualifications: List[str] = Field(max_items=3, description="Top 3 qualifications for the job")
    
    # Filtering criteria
    is_professional: bool = Field(description="Is this a professional white-collar job?")
    is_entry_level: bool = Field(description="Is this entry-level (0-3 years experience)?")
    is_relevant: bool = Field(description="Is this job relevant to the search keyword?")
    
    class Config:
        use_enum_values = True
