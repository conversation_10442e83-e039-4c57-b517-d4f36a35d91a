"""
LLM Client Module

Production-ready LLM client using LangChain and Groq for job processing.
Uses Pydantic for structured output and proper error handling.
"""

import os
import time
import logging
import json
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from langchain_groq import ChatGroq
from langchain.schema import HumanMessage, SystemMessage
from langchain_core.output_parsers import PydanticOutputParser
from pydantic import ValidationError

from .schemas import JobFilterResult, JobDecision

logger = logging.getLogger(__name__)

@dataclass
class LLMConfig:
    """Configuration for LLM client."""
    api_key: str
    model: str = "llama3-8b-8192"
    temperature: float = 0.1
    max_tokens: int = 150
    timeout: int = 30
    max_retries: int = 3
    rate_limit_delay: float = 1.0

class LLMClient:
    """
    Production-ready LLM client using LangChain and Groq with Pydantic structured output.
    
    Features:
    - Structured output using Pydantic
    - Rate limiting and retry logic
    - Error handling and logging
    - Batch processing support
    - Response validation
    """
    
    def __init__(self, config: LLMConfig):
        """
        Initialize LLM client.
        
        Args:
            config: LLM configuration
        """
        self.config = config
        self.llm = ChatGroq(
            groq_api_key=config.api_key,
            model_name=config.model,
            temperature=config.temperature,
            max_tokens=config.max_tokens,
            timeout=config.timeout
        )
        
        # Setup Pydantic output parser
        self.output_parser = PydanticOutputParser(pydantic_object=JobFilterResult)
        
        self.last_request_time = 0
        self.request_count = 0
        self.max_requests_per_minute = 50
        
        logger.info(f"LLM Client initialized with model: {config.model}")
        logger.info("Using Pydantic for structured output")
    
    def _rate_limit(self):
        """Apply rate limiting between requests."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.config.rate_limit_delay:
            sleep_time = self.config.rate_limit_delay - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
        self.request_count += 1
    
    def _validate_response(self, response: str) -> bool:
        """Validate LLM response format."""
        if not response or not isinstance(response, str):
            return False
        
        # Check for expected response format
        response_lower = response.lower().strip()
        return any(keyword in response_lower for keyword in ['keep', 'reject', 'yes', 'no'])
    
    def filter_job(self, job_data: Dict[str, Any]) -> JobFilterResult:
        """
        Filter a single job using LLM with simple structured output.
        
        Args:
            job_data: Dictionary containing job information
            
        Returns:
            JobFilterResult with simple assessment
        """
        try:
            self._rate_limit()
            
            # Create structured prompt
            prompt = self._create_structured_prompt(job_data)
            
            # Make LLM call
            messages = [
                SystemMessage(content=self._get_system_prompt()),
                HumanMessage(content=prompt)
            ]
            
            response = self.llm.invoke(messages)
            response_text = response.content.strip()
            
            # Parse using Pydantic
            try:
                filter_result = self.output_parser.parse(response_text)
            except ValidationError as e:
                logger.warning(f"Pydantic validation failed: {e}")
                logger.warning(f"Response text: '{response_text}'")
                # Fallback to manual parsing
                filter_result = self._parse_fallback_response(response_text)
            
            logger.debug(f"Job filtered: {job_data.get('job_title', 'Unknown')} -> {filter_result.decision} (Score: {filter_result.quality_score})")
            return filter_result
            
        except Exception as e:
            logger.error(f"Error filtering job {job_data.get('job_title', 'Unknown')}: {str(e)}")
            return self._create_error_result(job_data, str(e))
    
    def filter_jobs_batch(self, jobs_data: List[Dict[str, Any]]) -> List[JobFilterResult]:
        """
        Filter multiple jobs in batch with simple structured output.
        
        Args:
            jobs_data: List of job dictionaries
            
        Returns:
            List of JobFilterResult objects
        """
        results = []
        
        for i, job_data in enumerate(jobs_data):
            try:
                result = self.filter_job(job_data)
                results.append(result)
                
                # Progress logging
                if (i + 1) % 10 == 0:
                    logger.info(f"Processed {i + 1}/{len(jobs_data)} jobs")
                    
            except Exception as e:
                logger.error(f"Error in batch processing job {i}: {str(e)}")
                results.append(self._create_error_result(job_data, str(e)))
        
        return results
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for simple job filtering."""
        return """You are a job filter. Analyze jobs and decide if they should be KEPT or REJECTED.

Respond with ONLY this JSON format:
{
  "decision": "KEEP" or "REJECT",
  "quality_score": number 0-100,
  "reason": "brief explanation",
  "is_professional": true or false,
  "is_entry_level": true or false,
  "is_relevant": true or false
}

KEEP a job if ALL 3 criteria are met:
1. PROFESSIONAL: White-collar job (not cook, driver, cashier, waiter, etc.)
2. ENTRY-LEVEL: Intern, Junior, or 0-3 years experience required
3. RELEVANT: Matches the search keyword

REJECT if any criteria fails.

SCORING:
- 90-100: Perfect match (all 3 criteria + great description)
- 70-89: Good match (all 3 criteria met)
- 50-69: Partial match (2/3 criteria)
- 0-49: Poor match (1/3 or 0/3 criteria)

Respond with valid JSON only."""

    def _create_structured_prompt(self, job_data: Dict[str, Any]) -> str:
        """Create simple prompt for job data."""
        job_title = job_data.get('job_title', 'N/A')
        company_name = job_data.get('company_name', 'N/A')
        description = job_data.get('job_summary', 'N/A')
        keyword = job_data.get('keyword', 'N/A')
        
        # Truncate description if too long
        if len(description) > 1000:
            description = description[:1000] + "..."
        
        prompt = f"""Job: {job_title}
Company: {company_name}
Keyword: {keyword}
Description: {description}

Analyze: Is this professional, entry-level, and relevant?

JSON:"""
        return prompt
    
    def _parse_fallback_response(self, response: str) -> JobFilterResult:
        """Fallback parsing when Pydantic validation fails."""
        # Handle empty or very short responses
        if not response or len(response.strip()) < 5:
            return JobFilterResult(
                decision=JobDecision.REJECT,
                quality_score=20,
                reason="Empty or invalid response",
                is_professional=False,
                is_entry_level=False,
                is_relevant=False
            )
        
        try:
            # Try to extract JSON from response
            if "{" in response and "}" in response:
                start = response.find("{")
                end = response.rfind("}") + 1
                json_str = response[start:end]
                data = json.loads(json_str)
                
                # Handle partial responses
                decision = data.get('decision', 'REJECT')
                if isinstance(decision, str):
                    decision = JobDecision.KEEP if decision.upper() == 'KEEP' else JobDecision.REJECT
                else:
                    decision = JobDecision.REJECT
                
                return JobFilterResult(
                    decision=decision,
                    quality_score=data.get('quality_score', 50),
                    reason=data.get('reason', 'Parsed from fallback'),
                    is_professional=data.get('is_professional', False),
                    is_entry_level=data.get('is_entry_level', False),
                    is_relevant=data.get('is_relevant', False)
                )
        except Exception as e:
            logger.warning(f"Fallback parsing failed: {e}")
        
        # Try to extract basic info from text
        response_lower = response.lower()
        if 'keep' in response_lower:
            decision = JobDecision.KEEP
            quality_score = 70
            reason = "Basic parsing - appears to be KEEP"
        elif 'reject' in response_lower:
            decision = JobDecision.REJECT
            quality_score = 30
            reason = "Basic parsing - appears to be REJECT"
        else:
            decision = JobDecision.REJECT
            quality_score = 20
            reason = "Unable to parse response - defaulting to REJECT"
        
        # Ultimate fallback
        return JobFilterResult(
            decision=decision,
            quality_score=quality_score,
            reason=reason,
            is_professional=False,
            is_entry_level=False,
            is_relevant=False
        )
    
    def _create_error_result(self, job_data: Dict[str, Any], error_message: str) -> JobFilterResult:
        """Create error result for failed requests."""
        return JobFilterResult(
            decision=JobDecision.REJECT,
            quality_score=0,
            reason=f"Processing error: {error_message}",
            is_professional=False,
            is_entry_level=False,
            is_relevant=False
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """Get client statistics."""
        return {
            'total_requests': self.request_count,
            'model': self.config.model,
            'rate_limit_delay': self.config.rate_limit_delay
        }
