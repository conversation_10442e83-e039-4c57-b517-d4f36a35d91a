"""
Indeed Job LLM Processor

Production-ready Indeed job processing using Groq and GPT OSS 120B.
Handles comprehensive analysis including filtering, scoring, work mode, authorization, 
job seniority, and qualifications. Uses job_location to help determine work mode.
"""

import time
import logging
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import json

from .groq_client import GroqStructuredClient, GroqConfig
from .schemas import Indeed<PERSON><PERSON><PERSON><PERSON>ult, JobDecision, WorkMode, Authorization, JobSeniority

logger = logging.getLogger(__name__)

@dataclass
class IndeedProcessorConfig:
    """Configuration for Indeed job processing."""
    batch_size: int = 10
    max_workers: int = 3
    delay_between_batches: float = 1.0
    retry_failed_jobs: bool = True
    max_retries: int = 2
    enable_parallel_processing: bool = True
    use_processed_description: bool = True  # Use processed_description instead of raw description

class IndeedJobProcessor:
    """
    Production-ready Indeed job processor using LLM.
    
    Features:
    - Comprehensive job analysis (filtering, scoring, work mode, authorization, job seniority, qualifications)
    - Uses job_location to help determine work mode (unlike LinkedIn)
    - Extracts job seniority from job title and description
    - Batch processing with rate limiting
    - Parallel processing for better performance
    - Error handling and retry logic
    - Progress tracking and logging
    - Uses processed_description for analysis
    """
    
    def __init__(self, groq_config: GroqConfig, processor_config: IndeedProcessorConfig = None):
        """
        Initialize Indeed job processor.
        
        Args:
            groq_config: Groq API configuration
            processor_config: Processing configuration
        """
        self.groq_client = GroqStructuredClient(groq_config)
        self.processor_config = processor_config or IndeedProcessorConfig()
        self.stats = {
            'total_processed': 0,
            'kept': 0,
            'rejected': 0,
            'errors': 0,
            'processing_time': 0
        }
        self.lock = threading.Lock()
        
        logger.info(f"Indeed Job Processor initialized with batch_size={self.processor_config.batch_size}")
        logger.info(f"Using processed_description: {self.processor_config.use_processed_description}")
    
    def process_indeed_jobs(self, df: pd.DataFrame, test_mode: bool = False) -> pd.DataFrame:
        """
        Process Indeed jobs using LLM for comprehensive analysis.
        
        Args:
            df: DataFrame containing Indeed job data
            test_mode: If True, don't remove rows, just add assessment columns
            
        Returns:
            DataFrame with LLM assessment columns (and filtered if not test_mode)
        """
        if df.empty:
            logger.warning("Empty DataFrame provided for Indeed processing")
            return df
        
        start_time = time.time()
        logger.info(f"🔍 Starting Indeed LLM processing with {len(df)} jobs...")
        if test_mode:
            logger.info("🧪 TEST MODE: Will not remove rows, only add assessment columns")
        
        # Reset stats
        self._reset_stats()
        
        try:
            # Process jobs and get structured results
            if self.processor_config.enable_parallel_processing and len(df) > 20:
                results = self._process_parallel(df)
            else:
                results = self._process_sequential(df)
            
            # Add assessment columns to DataFrame
            df_with_assessments = self._add_assessment_columns(df, results)
            
            # Keep both KEEP and REJECT jobs for analysis
            logger.info(f"📊 Processed {len(df_with_assessments)} jobs with LLM (keeping both KEEP and REJECT)")
            
            # Update stats
            self.stats['processing_time'] = time.time() - start_time
            self._log_stats()
            
            return df_with_assessments
            
        except Exception as e:
            logger.error(f"❌ Error in Indeed LLM processing: {e}")
            raise
    
    def _process_parallel(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Process jobs in parallel batches."""
        logger.info(f"🔄 Processing {len(df)} jobs in parallel batches...")
        
        # Split into batches
        batches = [df.iloc[i:i + self.processor_config.batch_size] for i in range(0, len(df), self.processor_config.batch_size)]
        
        all_results = []
        
        with ThreadPoolExecutor(max_workers=self.processor_config.max_workers) as executor:
            # Submit all batches
            future_to_batch = {
                executor.submit(self._process_batch, batch, batch_idx): batch_idx 
                for batch_idx, batch in enumerate(batches)
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_batch):
                batch_idx = future_to_batch[future]
                try:
                    batch_results = future.result()
                    all_results.extend(batch_results)
                    logger.info(f"✅ Completed batch {batch_idx + 1}/{len(batches)}")
                except Exception as e:
                    logger.error(f"❌ Error in batch {batch_idx + 1}: {e}")
                    # Add error results for this batch
                    batch = batches[batch_idx]
                    error_results = [self._create_error_result() for _ in range(len(batch))]
                    all_results.extend(error_results)
        
        return all_results
    
    def _process_sequential(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Process jobs sequentially in batches."""
        logger.info(f"🔄 Processing {len(df)} jobs sequentially...")
        
        all_results = []
        batches = [df.iloc[i:i + self.processor_config.batch_size] for i in range(0, len(df), self.processor_config.batch_size)]
        
        for batch_idx, batch in enumerate(batches):
            try:
                batch_results = self._process_batch(batch, batch_idx)
                all_results.extend(batch_results)
                logger.info(f"✅ Completed batch {batch_idx + 1}/{len(batches)}")
                
                # Add delay between batches
                if batch_idx < len(batches) - 1:
                    time.sleep(self.processor_config.delay_between_batches)
                    
            except Exception as e:
                logger.error(f"❌ Error in batch {batch_idx + 1}: {e}")
                # Add error results for this batch
                error_results = [self._create_error_result() for _ in range(len(batch))]
                all_results.extend(error_results)
        
        return all_results
    
    def _process_batch(self, batch_df: pd.DataFrame, batch_idx: int) -> List[Dict[str, Any]]:
        """Process a single batch of jobs."""
        results = []
        
        for idx, row in batch_df.iterrows():
            try:
                # Prepare job data for LLM processing
                job_data = self._prepare_job_data(row)
                
                # Process with LLM
                result = self.groq_client.process_indeed_job(job_data)
                
                # Convert to dict
                result_dict = result.model_dump()
                results.append(result_dict)
                
                # Update stats
                with self.lock:
                    self.stats['total_processed'] += 1
                    if result_dict['decision'] == 'KEEP':
                        self.stats['kept'] += 1
                    elif result_dict['decision'] == 'REJECT':
                        self.stats['rejected'] += 1
                    else:
                        self.stats['errors'] += 1
                
            except Exception as e:
                logger.error(f"❌ Error processing job {idx}: {e}")
                results.append(self._create_error_result())
                
                with self.lock:
                    self.stats['total_processed'] += 1
                    self.stats['errors'] += 1
        
        return results
    
    def _prepare_job_data(self, row: pd.Series) -> Dict[str, Any]:
        """Prepare job data for LLM processing."""
        # Use processed description if available, otherwise use raw description
        if self.processor_config.use_processed_description and 'processed_description' in row:
            description = row.get('processed_description', '')
        else:
            description = row.get('job_description', '')
        
        return {
            'job_title': str(row.get('job_title', '')),
            'company_name': str(row.get('company_name', '')),
            'job_description': str(description),
            'job_location': str(row.get('job_location', '')),
            'employment_type': str(row.get('employment_type', '')),
            'salary': str(row.get('salary', '')),
            'keyword': str(row.get('keyword', '')),
            'benefit': str(row.get('benefit', ''))
        }
    
    def _create_error_result(self) -> Dict[str, Any]:
        """Create an error result for failed processing."""
        return {
            'decision': 'ERROR',
            'quality_score': 0,
            'reason': 'Processing error',
            'work_mode': 'Not Sure',
            'authorization': 'Not Sure',
            'job_seniority': 'Not Sure',
            'qualifications': [],
            'is_professional': False,
            'is_entry_level': False,
            'is_relevant': False
        }
    
    def _add_assessment_columns(self, df: pd.DataFrame, results: List[Dict[str, Any]]) -> pd.DataFrame:
        """Add LLM assessment columns to DataFrame."""
        df_result = df.copy()
        
        # Add LLM assessment columns
        df_result['job_decision'] = [r['decision'] for r in results]
        df_result['job_quality_score'] = [r['quality_score'] for r in results]
        df_result['llm_work_mode'] = [r['work_mode'] for r in results]
        df_result['llm_authorization'] = [r['authorization'] for r in results]
        df_result['llm_job_seniority'] = [r['job_seniority'] for r in results]
        df_result['llm_qualifications'] = [r['qualifications'] for r in results]  # Array format
        df_result['llm_reason'] = [r['reason'] for r in results]
        df_result['llm_is_professional'] = [r['is_professional'] for r in results]
        df_result['llm_is_entry_level'] = [r['is_entry_level'] for r in results]
        df_result['llm_is_relevant'] = [r['is_relevant'] for r in results]
        
        return df_result
    
    def _reset_stats(self):
        """Reset processing statistics."""
        self.stats = {
            'total_processed': 0,
            'kept': 0,
            'rejected': 0,
            'errors': 0,
            'processing_time': 0
        }
    
    def _log_stats(self):
        """Log processing statistics."""
        stats = self.stats
        logger.info(f"📊 Indeed LLM Processing Stats:")
        logger.info(f"  Total processed: {stats['total_processed']}")
        logger.info(f"  Kept: {stats['kept']}")
        logger.info(f"  Rejected: {stats['rejected']}")
        logger.info(f"  Errors: {stats['errors']}")
        logger.info(f"  Processing time: {stats['processing_time']:.2f}s")
        
        if stats['total_processed'] > 0:
            keep_rate = (stats['kept'] / stats['total_processed']) * 100
            logger.info(f"  Keep rate: {keep_rate:.1f}%")
