"""
Job Filter Module

Production-ready job filtering pipeline using LLM for quality assessment.
Handles batch processing, error handling, and performance optimization.
"""

import time
import logging
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import threading

from .llm_client import LL<PERSON>lient, LLMConfig
from .schemas import JobFilterResult, JobDecision

logger = logging.getLogger(__name__)

@dataclass
class FilterConfig:
    """Configuration for job filtering."""
    batch_size: int = 10
    max_workers: int = 3
    delay_between_batches: float = 1.0
    retry_failed_jobs: bool = True
    max_retries: int = 2
    enable_parallel_processing: bool = True
    quality_threshold: int = 50  # Minimum quality score to keep
    use_decision_enum: bool = True  # Use enum decision instead of score

class JobFilter:
    """
    Production-ready job filtering pipeline.
    
    Features:
    - Batch processing with rate limiting
    - Parallel processing for better performance
    - Error handling and retry logic
    - Progress tracking and logging
    - Quality metrics and reporting
    """
    
    def __init__(self, llm_client_or_config, filter_config: FilterConfig = None):
        """
        Initialize job filter.
        
        Args:
            llm_client_or_config: Either LLMClient instance, GroqStructuredClient instance, or LLMConfig
            filter_config: Filter processing configuration
        """
        # Handle both client instances and config objects
        if hasattr(llm_client_or_config, 'filter_job'):
            # It's already a client instance
            self.llm_client = llm_client_or_config
        else:
            # It's a config, create LLMClient
            self.llm_client = LLMClient(llm_client_or_config)
        
        self.filter_config = filter_config or FilterConfig()
        self.stats = {
            'total_processed': 0,
            'kept': 0,
            'rejected': 0,
            'errors': 0,
            'processing_time': 0
        }
        self.lock = threading.Lock()
        
        logger.info(f"JobFilter initialized with batch_size={self.filter_config.batch_size}")
    
    def filter_high_quality_jobs(self, df: pd.DataFrame, test_mode: bool = False) -> pd.DataFrame:
        """
        Filter jobs using LLM to keep only high-quality, entry-level positions.
        
        Args:
            df: DataFrame containing job data
            test_mode: If True, don't remove rows, just add assessment columns
            
        Returns:
            DataFrame with LLM assessment columns (and filtered if not test_mode)
        """
        if df.empty:
            logger.warning("Empty DataFrame provided for filtering")
            return df
        
        start_time = time.time()
        logger.info(f"🔍 Starting LLM filtering with {len(df)} jobs...")
        if test_mode:
            logger.info("🧪 TEST MODE: Will not remove rows, only add assessment columns")
        
        # Reset stats
        self._reset_stats()
        
        try:
            # Process jobs and get structured results
            if self.filter_config.enable_parallel_processing and len(df) > 20:
                results = self._process_parallel(df)
            else:
                results = self._process_sequential(df)
            
            # Add assessment columns to DataFrame
            df_with_assessments = self._add_assessment_columns(df, results)
            
            # Filter based on decision or score if not in test mode
            if not test_mode:
                if self.filter_config.use_decision_enum:
                    filtered_df = df_with_assessments[df_with_assessments['llm_decision'] == 'KEEP'].copy()
                else:
                    filtered_df = df_with_assessments[df_with_assessments['llm_quality_score'] >= self.filter_config.quality_threshold].copy()
            else:
                filtered_df = df_with_assessments
            
            # Update final stats
            self.stats['processing_time'] = time.time() - start_time
            self._log_filtering_results(len(df), len(filtered_df), test_mode)
            
            return filtered_df
            
        except Exception as e:
            logger.error(f"Error in job filtering: {str(e)}")
            # Return original DataFrame on critical error
            return df
    
    def _process_sequential(self, df: pd.DataFrame) -> List[JobFilterResult]:
        """Process jobs sequentially and return simple structured results."""
        all_results = []
        
        for i in range(0, len(df), self.filter_config.batch_size):
            batch = df.iloc[i:i+self.filter_config.batch_size]
            batch_results = self._process_batch(batch, i // self.filter_config.batch_size + 1)
            all_results.extend(batch_results)
            
            # Rate limiting
            if i + self.filter_config.batch_size < len(df):
                time.sleep(self.filter_config.delay_between_batches)
        
        return all_results
    
    def _process_parallel(self, df: pd.DataFrame) -> List[JobFilterResult]:
        """Process jobs using parallel processing and return simple structured results."""
        all_results = []
        
        # Split DataFrame into batches
        batches = [df.iloc[i:i+self.filter_config.batch_size] 
                  for i in range(0, len(df), self.filter_config.batch_size)]
        
        with ThreadPoolExecutor(max_workers=self.filter_config.max_workers) as executor:
            # Submit batches for processing
            future_to_batch = {
                executor.submit(self._process_batch, batch, idx + 1): idx 
                for idx, batch in enumerate(batches)
            }
            
            # Collect results
            for future in as_completed(future_to_batch):
                try:
                    batch_results = future.result()
                    all_results.extend(batch_results)
                except Exception as e:
                    logger.error(f"Error in parallel batch processing: {str(e)}")
        
        return all_results
    
    def _process_batch(self, batch_df: pd.DataFrame, batch_num: int) -> List[JobFilterResult]:
        """Process a batch of jobs and return simple structured results."""
        results = []
        
        logger.info(f"📦 Processing batch {batch_num} ({len(batch_df)} jobs)")
        
        for idx, row in batch_df.iterrows():
            try:
                job_data = self._prepare_job_data(row)
                result = self.llm_client.filter_job(job_data)
                results.append(result)
                
                self._update_stats(result)
                
                # Handle decision comparison properly
                decision = result.decision
                if hasattr(decision, 'value'):
                    decision = decision.value
                elif isinstance(decision, str):
                    decision = decision
                else:
                    decision = 'REJECT'
                
                score = result.quality_score
                logger.debug(f"{'✅ KEEP' if decision == 'KEEP' else '❌ REJECT'}: {job_data.get('job_title', 'Unknown')} (Score: {score}) - {result.reason}")
                
            except Exception as e:
                logger.error(f"Error processing job {idx}: {str(e)}")
                self._update_error_stats()
                # Create error result
                error_result = self.llm_client._create_error_result(job_data, str(e))
                results.append(error_result)
        
        kept_count = 0
        for r in results:
            decision = r.decision
            if hasattr(decision, 'value'):
                decision = decision.value
            elif isinstance(decision, str):
                decision = decision
            else:
                decision = 'REJECT'
            
            if decision == 'KEEP':
                kept_count += 1
        logger.info(f"✅ Batch {batch_num} completed: {kept_count}/{len(batch_df)} jobs kept")
        return results
    
    def _prepare_job_data(self, row: pd.Series) -> Dict[str, Any]:
        """Prepare job data for LLM processing."""
        # Get the appropriate description based on source
        source = str(row.get('source', 'N/A'))
        if source == 'Indeed':
            # For Indeed jobs, use 'description' column as it has the full content
            job_description = str(row.get('description', row.get('description_text', 'N/A')))
        else:
            # For LinkedIn jobs, use 'job_summary' column
            job_description = str(row.get('job_summary', 'N/A'))
        
        return {
            'job_title': str(row.get('job_title', 'N/A')),
            'company_name': str(row.get('company_name', 'N/A')),
            'job_summary': job_description,  # Use the correct description column
            'keyword': str(row.get('keyword', 'N/A')),
            'source': source,
            'apply_link': str(row.get('apply_link', 'N/A')),
            'job_location': str(row.get('job_location', 'N/A'))
        }
    
    def _add_assessment_columns(self, df: pd.DataFrame, results: List[JobFilterResult]) -> pd.DataFrame:
        """Add simple LLM assessment columns to DataFrame."""
        df_copy = df.copy()
        
        # Initialize simple assessment columns
        df_copy['llm_decision'] = 'REJECT'
        df_copy['llm_quality_score'] = 0
        df_copy['llm_reason'] = ''
        df_copy['llm_is_professional'] = False
        df_copy['llm_is_entry_level'] = False
        df_copy['llm_is_relevant'] = False
        
        # Add results to DataFrame
        for i, result in enumerate(results):
            if i < len(df_copy):
                # Handle decision enum properly
                decision_value = result.decision
                if hasattr(decision_value, 'value'):
                    decision_value = decision_value.value
                elif isinstance(decision_value, str):
                    decision_value = decision_value
                else:
                    decision_value = 'REJECT'
                
                df_copy.iloc[i, df_copy.columns.get_loc('llm_decision')] = decision_value
                df_copy.iloc[i, df_copy.columns.get_loc('llm_quality_score')] = result.quality_score
                df_copy.iloc[i, df_copy.columns.get_loc('llm_reason')] = result.reason
                df_copy.iloc[i, df_copy.columns.get_loc('llm_is_professional')] = result.is_professional
                df_copy.iloc[i, df_copy.columns.get_loc('llm_is_entry_level')] = result.is_entry_level
                df_copy.iloc[i, df_copy.columns.get_loc('llm_is_relevant')] = result.is_relevant
        
        return df_copy
    
    def _update_stats(self, result: JobFilterResult):
        """Update filtering statistics."""
        with self.lock:
            self.stats['total_processed'] += 1
            # Handle decision comparison properly
            decision = result.decision
            if hasattr(decision, 'value'):
                decision = decision.value
            elif isinstance(decision, str):
                decision = decision
            else:
                decision = 'REJECT'
            
            if decision == 'KEEP':
                self.stats['kept'] += 1
            else:
                self.stats['rejected'] += 1
    
    def _update_error_stats(self):
        """Update error statistics."""
        with self.lock:
            self.stats['errors'] += 1
    
    def _reset_stats(self):
        """Reset filtering statistics."""
        with self.lock:
            self.stats = {
                'total_processed': 0,
                'kept': 0,
                'rejected': 0,
                'errors': 0,
                'processing_time': 0
            }
    
    def _log_filtering_results(self, original_count: int, filtered_count: int, test_mode: bool = False):
        """Log filtering results and statistics."""
        kept_percentage = (filtered_count / original_count * 100) if original_count > 0 else 0
        error_percentage = (self.stats['errors'] / self.stats['total_processed'] * 100) if self.stats['total_processed'] > 0 else 0
        
        logger.info("=" * 60)
        if test_mode:
            logger.info("🧪 LLM TESTING RESULTS")
        else:
            logger.info("🎯 LLM FILTERING RESULTS")
        logger.info("=" * 60)
        logger.info(f"📊 Original jobs: {original_count}")
        if test_mode:
            logger.info(f"📋 Jobs with assessments: {filtered_count}")
            logger.info(f"✅ Jobs marked KEEP: {self.stats['kept']}")
            logger.info(f"❌ Jobs marked REJECT: {self.stats['rejected']}")
        else:
            logger.info(f"✅ Jobs kept: {filtered_count} ({kept_percentage:.1f}%)")
            logger.info(f"❌ Jobs rejected: {self.stats['rejected']}")
        logger.info(f"⚠️  Errors: {self.stats['errors']} ({error_percentage:.1f}%)")
        logger.info(f"⏱️  Processing time: {self.stats['processing_time']:.2f} seconds")
        logger.info(f"🚀 Jobs per second: {self.stats['total_processed'] / self.stats['processing_time']:.2f}")
        logger.info("=" * 60)
    
    def get_filtering_stats(self) -> Dict[str, Any]:
        """Get current filtering statistics."""
        with self.lock:
            return self.stats.copy()
    
    def get_quality_metrics(self) -> Dict[str, Any]:
        """Get quality metrics for the filtering process."""
        stats = self.get_filtering_stats()
        
        if stats['total_processed'] == 0:
            return {'error': 'No jobs processed yet'}
        
        return {
            'total_processed': stats['total_processed'],
            'kept_percentage': (stats['kept'] / stats['total_processed']) * 100,
            'rejected_percentage': (stats['rejected'] / stats['total_processed']) * 100,
            'error_percentage': (stats['errors'] / stats['total_processed']) * 100,
            'processing_speed': stats['total_processed'] / stats['processing_time'] if stats['processing_time'] > 0 else 0,
            'average_time_per_job': stats['processing_time'] / stats['total_processed'] if stats['total_processed'] > 0 else 0
        }
    
    def validate_job_data(self, job_data: Dict[str, Any]) -> bool:
        """Validate job data before processing."""
        required_fields = ['job_title', 'company_name', 'job_summary']
        
        for field in required_fields:
            if not job_data.get(field) or str(job_data[field]).strip() == 'N/A':
                logger.warning(f"Missing required field: {field}")
                return False
        
        return True
    
    def filter_single_job(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """Filter a single job (for testing purposes)."""
        if not self.validate_job_data(job_data):
            return {
                'keep': False,
                'reason': 'Invalid job data',
                'error': True
            }
        
        result = self.llm_client.filter_job(job_data)
        self._update_stats(result)
        
        return result
