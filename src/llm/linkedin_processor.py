"""
LinkedIn Job LLM Processor

Production-ready LinkedIn job processing using Groq and GPT OSS 20B.
Handles comprehensive analysis including filtering, scoring, work mode, authorization, and qualifications.
"""

import time
import logging
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import json

from .groq_client import GroqStructuredClient, GroqConfig
from .schemas import LinkedInJobResult, JobDecision, WorkMode, Authorization

logger = logging.getLogger(__name__)

@dataclass
class LinkedInProcessorConfig:
    """Configuration for LinkedIn job processing."""
    batch_size: int = 10
    max_workers: int = 3
    delay_between_batches: float = 1.0
    retry_failed_jobs: bool = True
    max_retries: int = 2
    enable_parallel_processing: bool = True
    use_processed_description: bool = True  # Use processed_description instead of raw description

class LinkedInJobProcessor:
    """
    Production-ready LinkedIn job processor using LLM.
    
    Features:
    - Comprehensive job analysis (filtering, scoring, work mode, authorization, qualifications)
    - Batch processing with rate limiting
    - Parallel processing for better performance
    - Error handling and retry logic
    - Progress tracking and logging
    - Uses processed_description for analysis
    """
    
    def __init__(self, groq_config: GroqConfig, processor_config: LinkedInProcessorConfig = None):
        """
        Initialize LinkedIn job processor.
        
        Args:
            groq_config: Groq API configuration
            processor_config: Processing configuration
        """
        self.groq_client = GroqStructuredClient(groq_config)
        self.processor_config = processor_config or LinkedInProcessorConfig()
        self.stats = {
            'total_processed': 0,
            'kept': 0,
            'rejected': 0,
            'errors': 0,
            'processing_time': 0
        }
        self.lock = threading.Lock()
        
        logger.info(f"LinkedIn Job Processor initialized with batch_size={self.processor_config.batch_size}")
        logger.info(f"Using processed_description: {self.processor_config.use_processed_description}")
    
    def process_linkedin_jobs(self, df: pd.DataFrame, test_mode: bool = False) -> pd.DataFrame:
        """
        Process LinkedIn jobs using LLM for comprehensive analysis.
        
        Args:
            df: DataFrame containing LinkedIn job data
            test_mode: If True, don't remove rows, just add assessment columns
            
        Returns:
            DataFrame with LLM assessment columns (and filtered if not test_mode)
        """
        if df.empty:
            logger.warning("Empty DataFrame provided for LinkedIn processing")
            return df
        
        start_time = time.time()
        logger.info(f"🔍 Starting LinkedIn LLM processing with {len(df)} jobs...")
        if test_mode:
            logger.info("🧪 TEST MODE: Will not remove rows, only add assessment columns")
        
        # Reset stats
        self._reset_stats()
        
        try:
            # Process jobs and get structured results
            if self.processor_config.enable_parallel_processing and len(df) > 20:
                results = self._process_parallel(df)
            else:
                results = self._process_sequential(df)
            
            # Add assessment columns to DataFrame
            df_with_assessments = self._add_assessment_columns(df, results)
            
            # Keep both KEEP and REJECT jobs for analysis
            filtered_df = df_with_assessments
            
            # Update final stats
            self.stats['processing_time'] = time.time() - start_time
            self._log_processing_results(len(df), len(filtered_df), test_mode)
            
            return filtered_df
            
        except Exception as e:
            logger.error(f"Error in LinkedIn job processing: {str(e)}")
            # Return original DataFrame on critical error
            return df
    
    def _process_sequential(self, df: pd.DataFrame) -> List[LinkedInJobResult]:
        """Process LinkedIn jobs sequentially and return structured results."""
        all_results = []
        
        for i in range(0, len(df), self.processor_config.batch_size):
            batch = df.iloc[i:i+self.processor_config.batch_size]
            batch_results = self._process_batch(batch, i // self.processor_config.batch_size + 1)
            all_results.extend(batch_results)
            
            # Rate limiting
            if i + self.processor_config.batch_size < len(df):
                time.sleep(self.processor_config.delay_between_batches)
        
        return all_results
    
    def _process_parallel(self, df: pd.DataFrame) -> List[LinkedInJobResult]:
        """Process LinkedIn jobs using parallel processing and return structured results."""
        all_results = []
        
        # Split DataFrame into batches
        batches = [df.iloc[i:i+self.processor_config.batch_size] 
                  for i in range(0, len(df), self.processor_config.batch_size)]
        
        with ThreadPoolExecutor(max_workers=self.processor_config.max_workers) as executor:
            # Submit batches for processing
            future_to_batch = {
                executor.submit(self._process_batch, batch, idx + 1): idx 
                for idx, batch in enumerate(batches)
            }
            
            # Collect results
            for future in as_completed(future_to_batch):
                try:
                    batch_results = future.result()
                    all_results.extend(batch_results)
                except Exception as e:
                    logger.error(f"Error in parallel batch processing: {str(e)}")
        
        return all_results
    
    def _process_batch(self, batch_df: pd.DataFrame, batch_num: int) -> List[LinkedInJobResult]:
        """Process a batch of LinkedIn jobs and return structured results."""
        results = []
        
        logger.info(f"📦 Processing LinkedIn batch {batch_num} ({len(batch_df)} jobs)")
        
        for idx, row in batch_df.iterrows():
            try:
                job_data = self._prepare_linkedin_job_data(row)
                result = self.groq_client.process_linkedin_job(job_data)
                results.append(result)
                
                self._update_stats(result)
                
                # Handle decision comparison properly
                decision = result.decision
                if hasattr(decision, 'value'):
                    decision = decision.value
                elif isinstance(decision, str):
                    decision = decision
                else:
                    decision = 'ERROR'
                
                score = result.quality_score
                logger.debug(f"{'✅ KEEP' if decision == 'KEEP' else '❌ REJECT' if decision == 'REJECT' else '⚠️ ERROR'}: {job_data.get('job_title', 'Unknown')} (Score: {score}) - {result.reason}")
                
            except Exception as e:
                logger.error(f"Error processing LinkedIn job {idx}: {str(e)}")
                self._update_error_stats()
                # Create error result
                error_result = self.groq_client._create_linkedin_error_result(str(e))
                results.append(error_result)
        
        kept_count = 0
        for r in results:
            decision = r.decision
            if hasattr(decision, 'value'):
                decision = decision.value
            elif isinstance(decision, str):
                decision = decision
            else:
                decision = 'REJECT'
            
            if decision == 'KEEP':
                kept_count += 1
        logger.info(f"✅ LinkedIn batch {batch_num} completed: {kept_count}/{len(batch_df)} jobs kept")
        return results
    
    def _prepare_linkedin_job_data(self, row: pd.Series) -> Dict[str, Any]:
        """Prepare LinkedIn job data for LLM processing with enhanced context."""
        # Use processed_description if available and configured
        if self.processor_config.use_processed_description and 'processed_description' in row:
            job_description = str(row.get('processed_description', ''))
            if not job_description or job_description.strip() == '':
                job_description = str(row.get('job_description', 'N/A'))
        else:
            job_description = str(row.get('job_description', 'N/A'))
        
        return {
            'job_title': str(row.get('job_title', 'N/A')),
            'company_name': str(row.get('company_name', 'N/A')),
            'processed_description': job_description,
            'job_description': job_description,  # Fallback
            'keyword': str(row.get('keyword', 'N/A')),
            'source': 'LinkedIn',
            'apply_link': str(row.get('apply_link', 'N/A')),
            'company_industry': str(row.get('company_industry', 'N/A')),
            'job_seniority': str(row.get('job_seniority', 'N/A')),
            'employment_type': str(row.get('employment_type', 'N/A'))
        }
    
    def _add_assessment_columns(self, df: pd.DataFrame, results: List[LinkedInJobResult]) -> pd.DataFrame:
        """Add LLM assessment columns to DataFrame."""
        df_copy = df.copy()
        
        # Initialize assessment columns
        df_copy['job_decision'] = 'ERROR'
        df_copy['job_quality_score'] = 0
        df_copy['llm_work_mode'] = 'Not Sure'
        df_copy['llm_authorization'] = 'Not Sure'
        df_copy['llm_qualifications'] = [[] for _ in range(len(df_copy))]  # Array format
        df_copy['llm_reason'] = ''
        df_copy['llm_is_professional'] = False
        df_copy['llm_is_entry_level'] = False
        df_copy['llm_is_relevant'] = False
        
        # Add results to DataFrame
        for i, result in enumerate(results):
            if i < len(df_copy):
                # Handle decision enum properly
                decision_value = result.decision
                if hasattr(decision_value, 'value'):
                    decision_value = decision_value.value
                elif isinstance(decision_value, str):
                    decision_value = decision_value
                else:
                    decision_value = 'ERROR'
                
                # Handle work mode enum
                work_mode_value = result.work_mode
                if hasattr(work_mode_value, 'value'):
                    work_mode_value = work_mode_value.value
                elif isinstance(work_mode_value, str):
                    work_mode_value = work_mode_value
                else:
                    work_mode_value = 'Not Sure'
                
                # Handle authorization enum
                auth_value = result.authorization
                if hasattr(auth_value, 'value'):
                    auth_value = auth_value.value
                elif isinstance(auth_value, str):
                    auth_value = auth_value
                else:
                    auth_value = 'Not Sure'
                
                df_copy.iloc[i, df_copy.columns.get_loc('job_decision')] = decision_value
                df_copy.iloc[i, df_copy.columns.get_loc('job_quality_score')] = result.quality_score
                df_copy.iloc[i, df_copy.columns.get_loc('llm_work_mode')] = work_mode_value
                df_copy.iloc[i, df_copy.columns.get_loc('llm_authorization')] = auth_value
                df_copy.at[df_copy.index[i], 'llm_qualifications'] = result.qualifications  # Array format
                df_copy.iloc[i, df_copy.columns.get_loc('llm_reason')] = result.reason
                df_copy.iloc[i, df_copy.columns.get_loc('llm_is_professional')] = result.is_professional
                df_copy.iloc[i, df_copy.columns.get_loc('llm_is_entry_level')] = result.is_entry_level
                df_copy.iloc[i, df_copy.columns.get_loc('llm_is_relevant')] = result.is_relevant
        
        return df_copy
    
    def _update_stats(self, result: LinkedInJobResult):
        """Update processing statistics."""
        with self.lock:
            self.stats['total_processed'] += 1
            # Handle decision comparison properly
            decision = result.decision
            if hasattr(decision, 'value'):
                decision = decision.value
            elif isinstance(decision, str):
                decision = decision
            else:
                decision = 'ERROR'
            
            if decision == 'KEEP':
                self.stats['kept'] += 1
            elif decision == 'REJECT':
                self.stats['rejected'] += 1
            else:  # ERROR
                self.stats['errors'] += 1
    
    def _update_error_stats(self):
        """Update error statistics."""
        with self.lock:
            self.stats['errors'] += 1
    
    def _reset_stats(self):
        """Reset processing statistics."""
        with self.lock:
            self.stats = {
                'total_processed': 0,
                'kept': 0,
                'rejected': 0,
                'errors': 0,
                'processing_time': 0
            }
    
    def _log_processing_results(self, original_count: int, filtered_count: int, test_mode: bool = False):
        """Log processing results and statistics."""
        kept_percentage = (filtered_count / original_count * 100) if original_count > 0 else 0
        error_percentage = (self.stats['errors'] / self.stats['total_processed'] * 100) if self.stats['total_processed'] > 0 else 0
        
        logger.info("=" * 60)
        if test_mode:
            logger.info("🧪 LINKEDIN LLM TESTING RESULTS")
        else:
            logger.info("🎯 LINKEDIN LLM PROCESSING RESULTS")
        logger.info("=" * 60)
        logger.info(f"📊 Original jobs: {original_count}")
        if test_mode:
            logger.info(f"📋 Jobs with assessments: {filtered_count}")
            logger.info(f"✅ Jobs marked KEEP: {self.stats['kept']}")
            logger.info(f"❌ Jobs marked REJECT: {self.stats['rejected']}")
            logger.info(f"⚠️ Jobs marked ERROR: {self.stats['errors']}")
        else:
            logger.info(f"✅ Jobs kept: {filtered_count} ({kept_percentage:.1f}%)")
            logger.info(f"❌ Jobs rejected: {self.stats['rejected']}")
        logger.info(f"⚠️  Errors: {self.stats['errors']} ({error_percentage:.1f}%)")
        logger.info(f"⏱️  Processing time: {self.stats['processing_time']:.2f} seconds")
        logger.info(f"🚀 Jobs per second: {self.stats['total_processed'] / self.stats['processing_time']:.2f}")
        logger.info("=" * 60)
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get current processing statistics."""
        with self.lock:
            return self.stats.copy()
    
    def get_quality_metrics(self) -> Dict[str, Any]:
        """Get quality metrics for the processing."""
        stats = self.get_processing_stats()
        
        if stats['total_processed'] == 0:
            return {'error': 'No jobs processed yet'}
        
        return {
            'total_processed': stats['total_processed'],
            'kept_percentage': (stats['kept'] / stats['total_processed']) * 100,
            'rejected_percentage': (stats['rejected'] / stats['total_processed']) * 100,
            'error_percentage': (stats['errors'] / stats['total_processed']) * 100,
            'processing_speed': stats['total_processed'] / stats['processing_time'] if stats['processing_time'] > 0 else 0,
            'average_time_per_job': stats['processing_time'] / stats['total_processed'] if stats['total_processed'] > 0 else 0
        }
    
    def validate_linkedin_job_data(self, job_data: Dict[str, Any]) -> bool:
        """Validate LinkedIn job data before processing."""
        required_fields = ['job_title', 'company_name', 'processed_description']
        
        for field in required_fields:
            if not job_data.get(field) or str(job_data[field]).strip() == 'N/A':
                logger.warning(f"Missing required field: {field}")
                return False
        
        return True
    
    def process_single_linkedin_job(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single LinkedIn job (for testing purposes)."""
        if not self.validate_linkedin_job_data(job_data):
            return {
                'decision': 'ERROR',
                'reason': 'Invalid job data',
                'error': True
            }
        
        result = self.groq_client.process_linkedin_job(job_data)
        self._update_stats(result)
        
        return result
