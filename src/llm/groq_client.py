"""
Groq Structured Output Client

Direct Groq API client using structured output for reliable JSON schema compliance.
"""

import os
import time
import json
import logging
from typing import Dict, List, Any
from dataclasses import dataclass

from groq import Groq
from pydantic import ValidationError

from .schemas import JobFilterResult, JobDecision, LinkedInJobResult, IndeedJobResult, WorkMode, Authorization, JobSeniority

logger = logging.getLogger(__name__)

@dataclass
class GroqConfig:
    """Configuration for Groq client."""
    api_key: str
    model: str = "openai/gpt-oss-120b"  # GPT OSS 120B model as requested
    temperature: float = 0.1
    max_tokens: int = 10000  # Higher limit for better detailed responses
    timeout: int = 30
    max_retries: int = 3
    rate_limit_delay: float = 1.0

class GroqStructuredClient:
    """
    Groq client with native structured output support.
    
    Features:
    - Guaranteed JSON schema compliance
    - Direct Pydantic model validation
    - No fallback parsing needed
    - Higher reliability and performance
    """
    
    def __init__(self, config: GroqConfig):
        """
        Initialize Groq structured client.
        
        Args:
            config: Groq configuration
        """
        self.config = config
        self.client = Groq(api_key=config.api_key)
        self.last_request_time = 0
        self.request_count = 0
        self.max_requests_per_minute = 50
        
        logger.info(f"Groq Structured Client initialized with model: {config.model}")
        logger.info("Using native structured output - guaranteed schema compliance")
    
    def _rate_limit(self):
        """Apply rate limiting between requests."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.config.rate_limit_delay:
            time.sleep(self.config.rate_limit_delay - time_since_last)
        
        self.last_request_time = time.time()
        self.request_count += 1
    
    def filter_job(self, job_data: Dict[str, Any]) -> JobFilterResult:
        """
        Filter a single job using Groq structured output.
        
        Args:
            job_data: Dictionary containing job information
            
        Returns:
            JobFilterResult with structured assessment
        """
        try:
            self._rate_limit()
            
            # Create the prompt
            prompt = self._create_prompt(job_data)
            
            # Make Groq API call with structured output
            response = self.client.chat.completions.create(
                model=self.config.model,
                messages=[
                    {"role": "system", "content": self._get_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                response_format={
                    "type": "json_schema",
                    "json_schema": {
                        "name": "job_filter_result",
                        "schema": JobFilterResult.model_json_schema()
                    }
                },
                temperature=self.config.temperature,
                max_tokens=self.config.max_tokens
            )
            
            # Parse response directly with Pydantic
            response_content = response.choices[0].message.content
            response_data = json.loads(response_content)
            
            # Ensure boolean fields are properly handled
            if 'is_professional' in response_data and response_data['is_professional'] is None:
                response_data['is_professional'] = False
            if 'is_entry_level' in response_data and response_data['is_entry_level'] is None:
                response_data['is_entry_level'] = False
            if 'is_relevant' in response_data and response_data['is_relevant'] is None:
                response_data['is_relevant'] = False
            
            # Ensure lists are properly handled
            if 'qualifications' not in response_data or response_data['qualifications'] is None:
                response_data['qualifications'] = []
            if 'industry_keywords' not in response_data or response_data['industry_keywords'] is None:
                response_data['industry_keywords'] = []
            
            filter_result = JobFilterResult.model_validate(response_data)
            
            logger.debug(f"Job filtered: {job_data.get('job_title', 'Unknown')} -> {filter_result.decision} (Score: {filter_result.quality_score})")
            return filter_result
            
        except Exception as e:
            logger.error(f"Error filtering job {job_data.get('job_title', 'Unknown')}: {str(e)}")
            return self._create_error_result(str(e))
    
    def filter_jobs_batch(self, jobs_data: List[Dict[str, Any]]) -> List[JobFilterResult]:
        """
        Filter multiple jobs in batch with structured output.
        
        Args:
            jobs_data: List of job dictionaries
            
        Returns:
            List of JobFilterResult objects
        """
        results = []
        
        for i, job_data in enumerate(jobs_data):
            try:
                result = self.filter_job(job_data)
                results.append(result)
                
                # Progress logging
                if (i + 1) % 10 == 0:
                    logger.info(f"Processed {i + 1}/{len(jobs_data)} jobs")
                    
            except Exception as e:
                logger.error(f"Error in batch processing job {i}: {str(e)}")
                results.append(self._create_error_result(str(e)))
        
        return results
    
    def process_linkedin_job(self, job_data: Dict[str, Any]) -> LinkedInJobResult:
        """
        Process a single LinkedIn job with comprehensive analysis.
        
        Args:
            job_data: Dictionary containing LinkedIn job information
            
        Returns:
            LinkedInJobResult with comprehensive analysis
        """
        try:
            self._rate_limit()
            
            # Create the prompt for LinkedIn job processing
            prompt = self._create_linkedin_prompt(job_data)
            
            # Make Groq API call with structured output
            response = self.client.chat.completions.create(
                model=self.config.model,
                messages=[
                    {"role": "system", "content": self._get_linkedin_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                response_format={
                    "type": "json_schema",
                    "json_schema": {
                        "name": "linkedin_job_result",
                        "schema": LinkedInJobResult.model_json_schema()
                    }
                },
                temperature=self.config.temperature,
                max_tokens=self.config.max_tokens
            )
            
            # Parse response directly with Pydantic
            response_content = response.choices[0].message.content
            response_data = json.loads(response_content)
            
            # Ensure boolean fields are properly handled
            if 'is_professional' in response_data and response_data['is_professional'] is None:
                response_data['is_professional'] = False
            if 'is_entry_level' in response_data and response_data['is_entry_level'] is None:
                response_data['is_entry_level'] = False
            if 'is_relevant' in response_data and response_data['is_relevant'] is None:
                response_data['is_relevant'] = False
            
            # Ensure lists are properly handled
            if 'qualifications' not in response_data or response_data['qualifications'] is None:
                response_data['qualifications'] = []
            
            # Ensure enums are properly handled
            if 'decision' in response_data and response_data['decision'] not in ['KEEP', 'REJECT', 'ERROR']:
                response_data['decision'] = 'ERROR'
            if 'work_mode' in response_data and response_data['work_mode'] not in ['On-site', 'Hybrid', 'Remote', 'Not Sure']:
                response_data['work_mode'] = 'Not Sure'
            if 'authorization' in response_data and response_data['authorization'] not in ['H1B Possible', 'H1B Available', 'No Authorization', 'Not Sure']:
                response_data['authorization'] = 'Not Sure'
            
            linkedin_result = LinkedInJobResult.model_validate(response_data)
            
            logger.debug(f"LinkedIn job processed: {job_data.get('job_title', 'Unknown')} -> {linkedin_result.decision} (Score: {linkedin_result.quality_score})")
            return linkedin_result
            
        except Exception as e:
            logger.error(f"Error processing LinkedIn job {job_data.get('job_title', 'Unknown')}: {str(e)}")
            return self._create_linkedin_error_result(str(e))
    
    def process_linkedin_jobs_batch(self, jobs_data: List[Dict[str, Any]]) -> List[LinkedInJobResult]:
        """
        Process multiple LinkedIn jobs in batch with structured output.
        
        Args:
            jobs_data: List of LinkedIn job dictionaries
            
        Returns:
            List of LinkedInJobResult objects
        """
        results = []
        
        for i, job_data in enumerate(jobs_data):
            try:
                result = self.process_linkedin_job(job_data)
                results.append(result)
                
                # Progress logging
                if (i + 1) % 10 == 0:
                    logger.info(f"Processed {i + 1}/{len(jobs_data)} LinkedIn jobs")
                    
            except Exception as e:
                logger.error(f"Error in batch processing LinkedIn job {i}: {str(e)}")
                results.append(self._create_linkedin_error_result(str(e)))
        
        return results
    
    def process_indeed_job(self, job_data: Dict[str, Any]) -> IndeedJobResult:
        """
        Process a single Indeed job with comprehensive analysis.
        
        Args:
            job_data: Dictionary containing Indeed job information
            
        Returns:
            IndeedJobResult with comprehensive analysis
        """
        try:
            self._rate_limit()
            
            # Create the prompt for Indeed job processing
            prompt = self._create_indeed_prompt(job_data)
            
            # Make Groq API call with structured output
            response = self.client.chat.completions.create(
                model=self.config.model,
                messages=[
                    {"role": "system", "content": self._get_indeed_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                response_format={
                    "type": "json_schema",
                    "json_schema": {
                        "name": "indeed_job_result",
                        "schema": IndeedJobResult.model_json_schema()
                    }
                },
                temperature=self.config.temperature,
                max_tokens=self.config.max_tokens
            )
            
            # Parse response directly with Pydantic
            response_content = response.choices[0].message.content
            response_data = json.loads(response_content)
            
            # Ensure boolean fields are properly handled
            if 'is_professional' in response_data and response_data['is_professional'] is None:
                response_data['is_professional'] = False
            if 'is_entry_level' in response_data and response_data['is_entry_level'] is None:
                response_data['is_entry_level'] = False
            if 'is_relevant' in response_data and response_data['is_relevant'] is None:
                response_data['is_relevant'] = False
            
            # Ensure lists are properly handled
            if 'qualifications' not in response_data or response_data['qualifications'] is None:
                response_data['qualifications'] = []
            
            indeed_result = IndeedJobResult.model_validate(response_data)
            
            logger.debug(f"Indeed job processed: {job_data.get('job_title', 'Unknown')} -> {indeed_result.decision} (Score: {indeed_result.quality_score})")
            return indeed_result
            
        except Exception as e:
            logger.error(f"Error processing Indeed job {job_data.get('job_title', 'Unknown')}: {str(e)}")
            return self._create_indeed_error_result(str(e))
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for job filtering and data extraction."""
        return """You are a job filter and data extractor. Analyze jobs and decide if they should be KEPT or REJECTED, then extract key information.

FILTERING CRITERIA:
KEEP a job if ALL 3 criteria are met:
1. PROFESSIONAL: White-collar job (not cook, driver, cashier, waiter, etc.)
2. ENTRY-LEVEL: Intern, Junior, or 0-3 years experience required
3. RELEVANT: Matches the search keyword

REJECT if any criteria fails.

SCORING:
- 90-100: Perfect match (all 3 criteria + great description)
- 70-89: Good match (all 3 criteria met)
- 50-69: Partial match (2/3 criteria)
- 0-49: Poor match (1/3 or 0/3 criteria)

DATA EXTRACTION:
1. QUALIFICATIONS: Extract top 3 key qualifications/requirements (max 3)
2. EMPLOYMENT_TYPE: Choose from: Full-time, Part-time, Internship, Contract, Volunteer, Temporary, Not Sure
3. INDUSTRY_KEYWORDS: Generate 4-5 industry keywords that best describe the company/role

IMPORTANT: All boolean fields (is_professional, is_entry_level, is_relevant) must be true or false, never null or undefined.

Provide structured assessment with decision, score, reason, criteria evaluation, and extracted data."""

    def _create_prompt(self, job_data: Dict[str, Any]) -> str:
        """Create prompt for job data with enhanced context."""
        job_title = job_data.get('job_title', 'N/A')
        company_name = job_data.get('company_name', 'N/A')
        description = job_data.get('job_summary', 'N/A')
        keyword = job_data.get('keyword', 'N/A')
        location = job_data.get('job_location', 'N/A')
        company_industry = job_data.get('company_industry', 'N/A')
        
        # Truncate description if too long
        if len(description) > 1500:
            description = description[:1500] + "..."
        
        prompt = f"""Job Title: {job_title}
Company: {company_name}


Search Keyword: {keyword}

Job Description: {description}

TASK:
1. FILTER: Is this professional, entry-level, and relevant to "{keyword}"?
2. EXTRACT QUALIFICATIONS: What are the top 3 key requirements/qualifications?
3. DETERMINE EMPLOYMENT TYPE: What type of employment is this?
4. GENERATE INDUSTRY KEYWORDS: What 4-5 keywords best describe this company/role's industry?

Analyze and provide structured response."""
        return prompt
    
    def _get_linkedin_system_prompt(self) -> str:
        """Get the system prompt for LinkedIn job processing."""
        return """You are an expert job analyst specializing in entry-level and internship positions. Your task is to carefully analyze job postings and extract accurate information.

CRITICAL FILTERING PRINCIPLES:
1. Work Mode Detection: Analyze job descriptions to determine work arrangements accurately
2. Entry-Level Assessment: Evaluate if positions are suitable for 0-3 years experience candidates
3. Professional Role Evaluation: Determine if roles are professional and suitable for career development
4. Quality Scoring: Assign realistic scores based on job quality, company reputation, and role relevance
5. Authorization Detection: Look for visa sponsorship, H1B, international candidate requirements
6. Qualifications Extraction: Extract 3 concise requirements with key technologies and tools mentioned
7. Strict Filtering: Reject jobs requiring specialized certifications, non-professional roles, or senior positions

IMPORTANT: Always provide qualifications for KEEP decisions. Reject jobs that require specialized certifications or are non-professional.

Return structured JSON with concise reasoning and concise qualifications."""

    def _create_linkedin_prompt(self, job_data: Dict[str, Any]) -> str:
        """Create prompt for LinkedIn job data with enhanced context."""
        job_title = job_data.get('job_title', 'N/A')
        company_name = job_data.get('company_name', 'N/A')
        processed_description = job_data.get('processed_description', 'N/A')
        keyword = job_data.get('keyword', 'N/A')
        location = job_data.get('job_location', 'N/A')
        company_industry = job_data.get('company_industry', 'N/A')
        job_seniority = job_data.get('job_seniority', 'N/A')
        employment_type = job_data.get('employment_type', 'N/A')
        
        # Use processed_description for analysis
        if processed_description == 'N/A' or not processed_description:
            processed_description = job_data.get('job_description', 'N/A')
        
        # Truncate description if too long (allow more context for better analysis)
        if len(processed_description) > 2000:
            processed_description = processed_description[:2000] + "..."
        
        prompt = f"""Job Title: {job_title}
Company: {company_name}
Job Seniority: {job_seniority}
Job Description: {processed_description}

Analyze this job posting and return JSON with:
- decision (KEEP/REJECT)
- quality_score (0-100)
- work_mode (On-site/Hybrid/Remote/Not Sure)
- authorization (H1B Possible/H1B Available/No Authorization/Not Sure)
- qualifications (3 concise requirements with key technologies/tools)
- is_professional, is_entry_level, is_relevant (boolean)
- reason (brief explanation)

CRITICAL FILTERING RULES:
1. REJECT if job requires specialized certifications (medical, legal, coaching, watchmaking, etc.)
2. REJECT if job is non-professional (retail, food service, manual labor, watchmaker, etc.)
3. REJECT if job title indicates seniority (Senior, Lead, Principal, Director, II, III, IV)
4. KEEP only if truly entry-level (0-3 years experience) or Internship and professional
5. ALWAYS provide qualifications for KEEP decisions - extract from the actual job description
6. If job description is unclear or doesn't match the title, REJECT

AUTHORIZATION DETECTION:
- "H1B Possible" if job mentions considering international candidates or visa sponsorship
- "H1B Available" if job explicitly offers visa sponsorship  
- "No Authorization" if job states "US citizens only" or "no visa sponsorship"
- "Not Sure" only if no authorization information is mentioned

IMPORTANT: Base your analysis on the job description provided. Do not hallucinate qualifications that aren't mentioned."""
        return prompt
    
    def _create_error_result(self, error_message: str) -> JobFilterResult:
        """Create error result for failed requests."""
        return JobFilterResult(
            decision=JobDecision.REJECT,
            quality_score=0,
            reason=f"ERROR: {error_message}",
            is_professional=False,
            is_entry_level=False,
            is_relevant=False,
            qualifications=[],
            employment_type="Not Sure",
            industry_keywords=[]
        )
    
    def _create_linkedin_error_result(self, error_message: str) -> LinkedInJobResult:
        """Create error result for failed LinkedIn job requests."""
        return LinkedInJobResult(
            decision=JobDecision.ERROR,
            quality_score=0,
            reason=f"ERROR: {error_message}",
            work_mode=WorkMode.NOT_SURE,
            authorization=Authorization.NOT_SURE,
            qualifications=[],
            is_professional=False,
            is_entry_level=False,
            is_relevant=False
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """Get client statistics."""
        return {
            'total_requests': self.request_count,
            'requests_per_minute': self.request_count / (time.time() - self.last_request_time) * 60 if self.last_request_time > 0 else 0
        }
    
    def _get_indeed_system_prompt(self) -> str:
        """Get the system prompt for Indeed job processing."""
        return """You are an expert job analyst specializing in Indeed job postings. Analyze jobs comprehensively and extract key information.

FILTERING CRITERIA:
KEEP a job if ALL 3 criteria are met:
1. PROFESSIONAL: White-collar job (not cook, driver, cashier, waiter, etc.)
2. ENTRY-LEVEL: Intern, Junior, or 0-3 years experience required
3. RELEVANT: Matches the search keyword

REJECT if any criteria fails.

SCORING:
- 90-100: Perfect match (all 3 criteria + great description)
- 70-89: Good match (all 3 criteria met)
- 50-69: Partial match (2/3 criteria)
- 0-49: Poor match (1/3 or 0/3 criteria)

WORK MODE DETECTION (use job_location + description):
- REMOTE: "Remote", "Work from home", "Virtual", or location shows "Remote"
- HYBRID: "Hybrid", "Flexible", "2-3 days in office", or similar
- ON-SITE: Specific office location mentioned, "In-person", "Office-based"
- NOT_SURE: Unclear or conflicting information

AUTHORIZATION DETECTION:
- H1B_AVAILABLE: "H1B sponsorship", "Visa sponsorship", "Will sponsor"
- H1B_POSSIBLE: "Must be authorized to work", "US work authorization required"
- NO_AUTHORIZATION: "US citizens only", "No visa sponsorship", "Must be US citizen"
- NOT_SURE: Unclear or no mention

JOB SENIORITY EXTRACTION (from title + description):
- ENTRY_LEVEL: "Intern", "Entry", "Junior", "0-1 years", "New grad", "Associate"
- JUNIOR: "Junior", "1-2 years", "Associate", "Trainee"
- MID_LEVEL: "Mid", "2-4 years", "Intermediate", "Specialist"
- SENIOR: "Senior", "4+ years", "Lead", "Principal", "Staff"
- LEAD: "Lead", "Team Lead", "Tech Lead", "Principal"
- PRINCIPAL: "Principal", "Staff", "Architect"
- DIRECTOR: "Director", "Head of", "VP"
- EXECUTIVE: "VP", "C-level", "Executive"
- NOT_SURE: Unclear or conflicting

QUALIFICATIONS: Extract top 3 key qualifications/requirements (max 3)

IMPORTANT: All boolean fields must be true or false, never null."""

    def _create_indeed_prompt(self, job_data: Dict[str, Any]) -> str:
        """Create prompt for Indeed job data with enhanced context."""
        job_title = job_data.get('job_title', 'N/A')
        company_name = job_data.get('company_name', 'N/A')
        description = job_data.get('job_description', 'N/A')
        keyword = job_data.get('keyword', 'N/A')
        location = job_data.get('job_location', 'N/A')
        employment_type = job_data.get('employment_type', 'N/A')
        salary = job_data.get('salary', 'N/A')
        benefit = job_data.get('benefit', 'N/A')
        
        # Truncate description if too long
        if len(description) > 2000:
            description = description[:2000] + "..."
        
        prompt = f"""Job Title: {job_title}
Company: {company_name}
Location: {location}
Employment Type: {employment_type}
Salary: {salary}
Search Keyword: {keyword}

Job Description: {description}

Benefits: {benefit}

Analyze this Indeed job posting and return JSON with:
- decision (KEEP/REJECT)
- quality_score (0-100)
- work_mode (On-site/Hybrid/Remote/Not Sure) - USE LOCATION + DESCRIPTION
- authorization (H1B Possible/H1B Available/No Authorization/Not Sure)
- job_seniority (Entry Level/Junior/Mid Level/Senior/Lead/Principal/Director/Executive/Not Sure)
- qualifications (3 concise requirements with key technologies/tools)
- is_professional, is_entry_level, is_relevant (boolean)
- reason (brief explanation)

CRITICAL FILTERING RULES:
1. REJECT if job requires specialized certifications (medical, legal, coaching, etc.)
2. REJECT if clearly non-professional (cook, driver, cashier, waiter, etc.)
3. REJECT if senior management (VP, Director, C-level) unless it's a PM role
4. REJECT if not relevant to the search keyword
5. KEEP if it's a professional tech/business role that matches the keyword

WORK MODE ANALYSIS:
- Look at location field: "Remote" = Remote, specific city = On-site, "Hybrid" = Hybrid
- Look in description for work mode keywords
- Use both location and description for best accuracy

JOB SENIORITY ANALYSIS:
- Analyze job title for seniority indicators
- Look in description for experience requirements
- Consider both title and description together

Provide structured assessment with decision, score, reason, and extracted data."""

        return prompt
    
    def _create_indeed_error_result(self, error_message: str) -> IndeedJobResult:
        """Create an error result for Indeed job processing failures."""
        return IndeedJobResult(
            decision=JobDecision.ERROR,
            quality_score=0,
            reason=f"Processing error: {error_message}",
            work_mode=WorkMode.NOT_SURE,
            authorization=Authorization.NOT_SURE,
            job_seniority=JobSeniority.NOT_SURE,
            qualifications=[],
            is_professional=False,
            is_entry_level=False,
            is_relevant=False
        )
