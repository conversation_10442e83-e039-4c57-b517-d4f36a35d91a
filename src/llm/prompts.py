"""
LLM Prompts Module

Centralized prompt templates for job filtering and analysis.
All prompts are optimized for Groq LLM models.
"""

from typing import Dict, Any

class FilterPrompts:
    """Prompt templates for job filtering."""
    
    @staticmethod
    def get_job_filter_prompt() -> str:
        """Get the main job filtering prompt."""
        return """
You are an expert job analyst specializing in entry-level positions. Your task is to analyze job postings and determine if they are suitable for candidates with 0-3 years of experience.

ANALYSIS CRITERIA:

KEEP the job if it meets ALL of these criteria:
1. Experience Level: Intern, Entry-level, Junior, or 0-3 years experience required
2. Job Type: Full-time or Internship position
3. Professional Level: White-collar, professional job
4. Quality: Clear, well-structured job description
5. Relevance: Relevant to the search keyword
6. Legitimacy: Appears to be from a legitimate company

REJECT the job if it meets ANY of these criteria:
1. Seniority: Senior, Lead, Principal, Director, Manager, VP, CEO, CTO, Head of
2. Experience: 4+ years experience required
3. Job Type: Part-time, Contract, Freelance (unless it's an internship)
4. Professional Level: Non-professional jobs (cook, driver, cashier, waiter, cleaner, etc.)
5. Quality: Unclear, spam, or suspicious job posting
6. Relevance: Completely irrelevant to the search keyword

RESPONSE FORMAT:
Decision: [KEEP/REJECT]
Reason: [Brief explanation of your decision]
Confidence: [High/Medium/Low]
"""
    
    @staticmethod
    def get_quality_assessment_prompt() -> str:
        """Get prompt for job quality assessment."""
        return """
Assess the quality of this job posting on a scale of 1-10.

Quality factors to consider:
- Job description clarity and completeness
- Company information and legitimacy
- Requirements specificity
- Benefits and compensation information
- Professional presentation

Score: [1-10]
Reason: [Brief explanation]
"""
    
    @staticmethod
    def get_experience_level_prompt() -> str:
        """Get prompt for experience level extraction."""
        return """
Extract the experience level requirement from this job posting.

Experience levels:
- Intern: 0 years, internship, student
- Entry: 0-2 years, fresh graduate, new grad
- Junior: 1-3 years, junior level
- Mid: 3-5 years, mid-level
- Senior: 5+ years, senior level

Extract the minimum and maximum years of experience required.

Response format:
Min Experience: [X years or Intern/Entry/Junior/Mid/Senior]
Max Experience: [X years or Intern/Entry/Junior/Mid/Senior]
Level: [Intern/Entry/Junior/Mid/Senior]
"""
    
    @staticmethod
    def get_work_mode_prompt() -> str:
        """Get prompt for work mode classification."""
        return """
Classify the work mode for this job posting.

Work modes:
- Remote: Fully remote, work from home, virtual
- Hybrid: Mix of remote and office, flexible location
- Onsite: Office-based, in-person, location-specific
- Not Specified: No clear indication

Response format:
Work Mode: [Remote/Hybrid/Onsite/Not Specified]
Confidence: [High/Medium/Low]
"""
    
    @staticmethod
    def get_work_authorization_prompt() -> str:
        """Get prompt for work authorization detection."""
        return """
Determine if this job posting mentions work authorization or visa sponsorship.

Look for:
- H1B sponsorship
- Visa sponsorship
- Work authorization
- Green card sponsorship
- International candidates welcome
- No H1B/visa sponsorship

Response format:
Authorization: [Yes/No/Not Specified]
Details: [Brief explanation of what was found]
Confidence: [High/Medium/Low]
"""

class PromptBuilder:
    """Builder class for creating dynamic prompts."""
    
    @staticmethod
    def build_job_filter_prompt(job_data: Dict[str, Any]) -> str:
        """Build a complete job filtering prompt with job data."""
        base_prompt = FilterPrompts.get_job_filter_prompt()
        
        job_title = job_data.get('job_title', 'N/A')
        company_name = job_data.get('company_name', 'N/A')
        description = job_data.get('job_summary', 'N/A')
        keyword = job_data.get('keyword', 'N/A')
        source = job_data.get('source', 'N/A')
        
        # Truncate description if too long
        if len(description) > 2000:
            description = description[:2000] + "..."
        
        job_details = f"""
JOB DETAILS TO ANALYZE:
- Title: {job_title}
- Company: {company_name}
- Source: {source}
- Search Keyword: {keyword}
- Description: {description}

Now analyze this job posting according to the criteria above.
"""
        
        return base_prompt + job_details
    
    @staticmethod
    def build_batch_filter_prompt(jobs_data: list) -> str:
        """Build a prompt for batch job filtering."""
        base_prompt = FilterPrompts.get_job_filter_prompt()
        
        jobs_text = "JOBS TO ANALYZE:\n\n"
        for i, job in enumerate(jobs_data, 1):
            job_title = job.get('job_title', 'N/A')
            company_name = job.get('company_name', 'N/A')
            description = job.get('job_summary', 'N/A')[:500]  # Truncate for batch
            keyword = job.get('keyword', 'N/A')
            
            jobs_text += f"Job {i}:\n"
            jobs_text += f"- Title: {job_title}\n"
            jobs_text += f"- Company: {company_name}\n"
            jobs_text += f"- Keyword: {keyword}\n"
            jobs_text += f"- Description: {description}\n\n"
        
        return base_prompt + jobs_text



