#!/usr/bin/env python3
"""
Post-processing script for combined job data.

This script processes the combined job data to:
1. Map LLM job seniority to main job_seniority column (for Indeed)
2. Replace empty columns with LLM-processed ones (work_mode, authorization, qualifications)
3. Filter to KEEP only jobs (remove REJECT and ERROR)
4. Remove LLM columns and everything after company_logo
5. Make job_posted_date the last column
"""

import sys
import os
import pandas as pd
from pathlib import Path
import argparse
from datetime import datetime

def post_process_combined_data(input_file: str, output_file: str = None):
    """
    Post-process combined job data according to requirements.
    
    Args:
        input_file: Path to the combined jobs CSV file
        output_file: Path for the output file (optional)
    """
    print("🔄 Post-Processing Combined Job Data")
    print("=" * 50)
    
    try:
        # Load the combined data
        print(f"📂 Loading data from: {input_file}")
        df = pd.read_csv(input_file)
        print(f"📊 Loaded {len(df)} jobs")
        
        # Step 1: Map LLM job seniority to main job_seniority column (for Indeed)
        print("\n1️⃣ Mapping LLM job seniority to main column...")
        if 'llm_job_seniority' in df.columns:
            # For Indeed jobs, use LLM job seniority
            indeed_mask = df['llm_job_seniority'].notna() & (df['llm_job_seniority'] != '')
            df.loc[indeed_mask, 'job_seniority'] = df.loc[indeed_mask, 'llm_job_seniority']
            print(f"✅ Mapped LLM job seniority for {indeed_mask.sum()} Indeed jobs")
        else:
            print("⚠️ No llm_job_seniority column found")
        
        # Step 2: Replace empty columns with LLM-processed ones
        print("\n2️⃣ Replacing empty columns with LLM-processed ones...")
        
        # Replace work_mode with LLM work_mode
        if 'llm_work_mode' in df.columns:
            llm_work_mode_mask = df['llm_work_mode'].notna() & (df['llm_work_mode'] != '')
            df.loc[llm_work_mode_mask, 'work_mode'] = df.loc[llm_work_mode_mask, 'llm_work_mode']
            print(f"✅ Replaced work_mode for {llm_work_mode_mask.sum()} jobs")
        
        # Replace authorization with LLM authorization
        if 'llm_authorization' in df.columns:
            llm_auth_mask = df['llm_authorization'].notna() & (df['llm_authorization'] != '')
            df.loc[llm_auth_mask, 'authorization'] = df.loc[llm_auth_mask, 'llm_authorization']
            print(f"✅ Replaced authorization for {llm_auth_mask.sum()} jobs")
        
        # Replace qualifications with LLM qualifications (convert array to string)
        if 'llm_qualifications' in df.columns:
            def format_qualifications(quals):
                if pd.isna(quals) or quals == '' or quals == '[]':
                    return ''
                if isinstance(quals, list):
                    return ' | '.join(quals)
                return str(quals)
            
            llm_quals_mask = df['llm_qualifications'].notna() & (df['llm_qualifications'] != '') & (df['llm_qualifications'] != '[]')
            df.loc[llm_quals_mask, 'qualifications'] = df.loc[llm_quals_mask, 'llm_qualifications'].apply(format_qualifications)
            print(f"✅ Replaced qualifications for {llm_quals_mask.sum()} jobs")
        
        # Step 3: Filter to KEEP only jobs
        print("\n3️⃣ Filtering to KEEP only jobs...")
        initial_count = len(df)
        
        if 'job_decision' in df.columns:
            keep_mask = df['job_decision'] == 'KEEP'
            df = df[keep_mask].copy()
            removed_count = initial_count - len(df)
            print(f"✅ Kept {len(df)} jobs, removed {removed_count} jobs (REJECT/ERROR)")
        else:
            print("⚠️ No job_decision column found, keeping all jobs")
        
        # Step 4: Remove LLM columns and everything after company_logo
        print("\n4️⃣ Removing LLM columns and columns after company_logo...")
        
        # Find the company_logo column index
        if 'company_logo' in df.columns:
            company_logo_idx = df.columns.get_loc('company_logo')
            # Keep columns up to and including company_logo, plus job_posted_date
            columns_to_keep = df.columns[:company_logo_idx + 1].tolist()
            
            # Add job_posted_date if it exists and isn't already included
            if 'job_posted_date' in df.columns and 'job_posted_date' not in columns_to_keep:
                columns_to_keep.append('job_posted_date')
            
            df = df[columns_to_keep]
            print(f"✅ Kept {len(columns_to_keep)} columns")
        else:
            print("⚠️ No company_logo column found, keeping all columns")
        
        # Step 5: Make job_posted_date the last column
        print("\n5️⃣ Reordering columns to make job_posted_date last...")
        
        if 'job_posted_date' in df.columns:
            # Remove job_posted_date from its current position
            cols = [col for col in df.columns if col != 'job_posted_date']
            # Add it at the end
            cols.append('job_posted_date')
            df = df[cols]
            print("✅ Moved job_posted_date to last column")
        else:
            print("⚠️ No job_posted_date column found")
        
        # Generate output filename if not provided
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"data/processed/combined_data/post_processed_jobs_{timestamp}.csv"
        
        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # Save the processed data
        print(f"\n💾 Saving processed data to: {output_file}")
        df.to_csv(output_file, index=False)
        
        # Show final summary
        print("\n📊 Post-Processing Summary:")
        print(f"  Final job count: {len(df)}")
        print(f"  Final column count: {len(df.columns)}")
        print(f"  Columns: {', '.join(df.columns)}")
        
        # Show sample of processed data
        if len(df) > 0:
            print(f"\n📋 Sample Processed Job:")
            sample = df.iloc[0]
            print(f"  Title: {sample.get('job_title', 'N/A')}")
            print(f"  Company: {sample.get('company_name', 'N/A')}")
            print(f"  Work Mode: {sample.get('work_mode', 'N/A')}")
            print(f"  Authorization: {sample.get('authorization', 'N/A')}")
            print(f"  Job Seniority: {sample.get('job_seniority', 'N/A')}")
            print(f"  Qualifications: {str(sample.get('qualifications', 'N/A'))[:100]}...")
        
        print(f"\n🎉 Post-processing completed successfully!")
        print(f"✅ Output saved to: {output_file}")
        
        return output_file
        
    except Exception as e:
        print(f"❌ Error in post-processing: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Main function with command line interface."""
    parser = argparse.ArgumentParser(description="Post-process combined job data")
    parser.add_argument(
        "--input", 
        default="data/processed/combined_data/combined_jobs.csv",
        help="Input CSV file path (default: data/processed/combined_data/combined_jobs.csv)"
    )
    parser.add_argument(
        "--output",
        help="Output CSV file path (default: auto-generated with timestamp)"
    )
    
    args = parser.parse_args()
    
    # Check if input file exists
    if not os.path.exists(args.input):
        print(f"❌ Input file not found: {args.input}")
        return
    
    # Run post-processing
    output_file = post_process_combined_data(args.input, args.output)
    
    if output_file:
        print(f"\n🚀 Ready to use: {output_file}")

if __name__ == "__main__":
    main()


