# 🚀 Job Scraper & Data Processing Pipeline

## 📋 **What This Project Does**

This is a **job scraping and data processing system** that:
1. **Scrapes job data** from LinkedIn and Indeed using BrightData API
2. **Processes and cleans** the raw job data
3. **Extracts key information** like work mode, authorization requirements, and qualifications
4. **Filters out non-professional jobs** (cook, driver, nurse, etc.)
5. **Outputs clean, structured data** for analysis

## 🏗️ **Current Architecture (What We Have Now)**

### **Core Components:**

#### **1. Data Scraping (`core/scraper.py`)**
- Uses BrightData API to scrape jobs from LinkedIn and Indeed
- Handles API calls, rate limiting, and error handling
- Saves raw data to S3

#### **2. Data Processing (`core/data_processor.py`)**
- **Main orchestrator** that coordinates everything
- Loads raw data from CSV files
- Applies filtering, extraction, and cleaning
- Combines LinkedIn and Indeed data
- Saves final processed data

#### **3. Job Filtering (`core/job_role_filter.py`)**
- **Removes non-professional jobs** (cook, driver, nurse, sales associate, etc.)
- **Keeps all tech jobs** (software engineer, data scientist, etc.)
- **Keeps professional business roles** (marketing manager, financial analyst, etc.)
- **Removes senior management** (CEO, VP, Director) except PM roles

#### **4. Work Mode Extraction (`core/work_mode_extractor.py`)**
- Detects if job is **Remote**, **Hybrid**, or **On-site**
- Uses pattern matching on job descriptions and locations
- Provides confidence scores

#### **5. Authorization Extraction (`core/authorization_extractor.py`)**
- Detects work authorization requirements:
  - **US Citizen Required**
  - **Green Card Required** 
  - **Visa Sponsorship Available**
  - **No Visa Sponsorship**
- Uses pattern matching on job descriptions

#### **6. Job Description Processing (`core/job_description_summarizer.py`)**
- Uses **NLTK** for text preprocessing
- Creates **3-line summaries** of job descriptions
- Extracts **key phrases** and keywords
- Generates **qualifications** column

#### **7. Redirect Handling (`core/redirect_handler.py`)**
- Follows LinkedIn redirects to get final apply links
- Only processes jobs marked as "external apply"
- Handles rate limiting and timeouts

## 📊 **Current Data Flow**

```
Raw Job Data (CSV) 
    ↓
Job Filtering (Remove non-professional jobs)
    ↓
Seniority Filtering (Remove senior roles)
    ↓
Duplicate Removal
    ↓
NLTK Processing (3-line summaries)
    ↓
Work Mode Extraction (Remote/Hybrid/On-site)
    ↓
Authorization Extraction (US Citizen/Visa/etc.)
    ↓
Redirect Processing (Final apply links)
    ↓
Final Structured Output (CSV)
```

## 🎯 **Current Results**

**Input:** 1,812 LinkedIn jobs
**Output:** 729 professional jobs (60% reduction)
**Processing Time:** ~2 seconds
**Accuracy:**
- Job Filtering: ~70%
- Work Mode: ~60%
- Authorization: ~40%
- Qualifications: ~80% (using NLTK)

## ❌ **Current Problems**

1. **Work Mode Detection is Poor** (60% accuracy)
2. **Authorization Detection is Poor** (40% accuracy)
3. **Job Filtering Could Be Better** (70% accuracy)
4. **NLTK Qualifications are Basic** (80% accuracy)

## 🚀 **Future Plan: LLM Integration**

### **What We Want to Do Next:**

#### **Phase 1: Replace Poor Extractors with LLM**
- Replace work mode extraction with **Groq + GPT OSS 20B**
- Replace authorization extraction with **Groq + GPT OSS 20B**
- Keep existing qualifications API (it's already good)
- Improve job filtering with LLM

#### **Phase 2: Hybrid Architecture**
```
Raw Job Data
    ↓
LLM Processing (Work Mode + Authorization + Filtering)
    ↓
Existing Qualifications API
    ↓
Final Structured Output
```

### **Why LLM?**
- **Better Accuracy:** 85-90% vs current 40-60%
- **Cheap Cost:** $0.02 per 729 jobs
- **Better Understanding:** Can read context and nuances
- **Flexible:** Easy to adjust prompts and requirements

### **LLM Architecture (Planned):**

#### **1. Pydantic Models (Type Safety)**
```python
class JobClassification(BaseModel):
    work_mode: WorkMode  # remote/hybrid/onsite
    authorization: Authorization  # us_citizen/visa_sponsorship/etc
    decision: JobDecision  # keep/reject
    professional_score: int  # 0-100 (granular scoring)
    tech_relevance_score: int  # 0-100
    overall_quality_score: int  # 0-100
    confidence: float  # 0.0-1.0
    reasoning: str  # Why this decision?
    qualifications: List[str]  # 3-line summary
```

#### **2. Groq Integration**
- **Model:** `gpt-oss-20b` (via Groq)
- **Cost:** $0.27 per 1M tokens (ultra-cheap)
- **Speed:** 200+ tokens/second
- **JSON Schema:** Perfect for structured output

#### **3. Batch Processing**
- Process 10-20 jobs per API call
- Reduce costs by 60-80%
- Better error handling

## 📁 **File Structure (Clean)**

```
src/
├── main.py                          # Entry point
├── core/                           # Core processing logic
│   ├── data_processor.py           # Main orchestrator
│   ├── scraper.py                  # BrightData API scraper
│   ├── job_role_filter.py          # Job filtering (regex-based)
│   ├── work_mode_extractor.py      # Work mode detection (regex-based)
│   ├── authorization_extractor.py  # Authorization detection (regex-based)
│   ├── job_description_summarizer.py # NLTK text processing
│   └── redirect_handler.py         # LinkedIn redirect processing
├── llm/                            # LLM integration (future)
│   ├── groq_client.py              # Groq API client
│   ├── job_filter.py               # LLM-based job filtering
│   ├── llm_client.py               # Generic LLM client
│   ├── prompts.py                  # LLM prompts
│   └── schemas.py                  # Pydantic models
├── utils/                          # Utility functions
│   ├── file_handler.py             # File operations
│   ├── logger.py                   # Logging
│   └── s3_handler.py               # S3 operations
├── config/                         # Configuration files
│   ├── scraping_inputs.json        # Scraping parameters
│   └── llm_config.json             # LLM configuration
└── data/                           # Data storage
    ├── raw/                        # Raw scraped data
    └── processed/                  # Processed data
```

## 🎮 **How to Use (Current System)**

### **Basic Commands:**
```bash
# Process LinkedIn data only
python main.py --action process --linkedin-only

# Process with redirect extraction
python main.py --action process --linkedin-only --extract-redirects

# Full pipeline (scrape + process + excel)
python main.py --action all --linkedin-only
```

### **Output Files:**
- `data/processed/combined_data/combined_jobs.csv` - Main output
- `data/processed/combined_data/linkedin_jobs_YYYYMMDD_HHMMSS.csv` - LinkedIn-specific
- `data/processed/combined_data/data_summary_YYYYMMDD_HHMMSS.txt` - Processing summary

## 🔧 **Current Status**

### **✅ What's Working:**
- Job scraping from LinkedIn and Indeed
- Basic job filtering (removes non-professional jobs)
- NLTK-based qualifications extraction
- Data processing pipeline
- CSV output generation

### **❌ What Needs Improvement:**
- Work mode detection accuracy (60% → 85%+)
- Authorization detection accuracy (40% → 85%+)
- Job filtering accuracy (70% → 90%+)
- Better qualifications extraction

### **🚀 Next Steps:**
1. **Implement LLM integration** for work mode and authorization
2. **Keep existing qualifications API** (it's already good)
3. **Add LLM-based job filtering** for better accuracy
4. **Test and optimize** the hybrid approach

## 💰 **Cost Analysis**

### **Current (Regex-based):**
- **Cost:** $0.00 (free)
- **Accuracy:** 40-70%
- **Maintenance:** High (constant pattern updates)

### **Future (LLM-based):**
- **Cost:** $0.02 per 729 jobs
- **Accuracy:** 85-90%
- **Maintenance:** Low (just prompt updates)

## 🎯 **Success Metrics**

### **Current:**
- 729 jobs processed from 1,812 (60% reduction)
- 2-second processing time
- 40-70% accuracy on key extractions

### **Target (After LLM Integration):**
- 729 jobs processed from 1,812 (60% reduction)
- 5-second processing time (with LLM)
- 85-90% accuracy on key extractions
- $0.02 cost per run

---

## 🤔 **Questions for You:**

1. **Should we proceed with LLM integration?** (Recommended: Yes)
2. **Which components should we prioritize?** (Recommended: Work mode + Authorization first)
3. **Should we keep the existing qualifications API?** (Recommended: Yes, it's already good)
4. **What's the target accuracy you want?** (Recommended: 85%+)

**This document explains everything we have, what we're doing, and what we plan to do next. The codebase is now clean and organized!**
