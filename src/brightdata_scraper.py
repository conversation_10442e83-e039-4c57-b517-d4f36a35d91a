import requests
import datetime
import json
import os
from typing import Dict, List, Any, Optional
import time

class BrightDataScraper:
    """
    BrightData API scraper for Indeed and LinkedIn job data.
    Dynamically creates separate API calls for each input category.
    """
    
    def __init__(self, config_file: str = "scraping_inputs.json"):
        """
        Initialize the scraper with configuration.
        
        Args:
            config_file: Path to JSON file with input configurations
        """
        self.config_file = config_file
        self.api_config = {
            "url": "https://api.brightdata.com/datasets/v3/trigger",
            "headers": {
                "Authorization": "Bearer e3f3f5a1c1e6aeb7830744473d5ceb8e4f7c7399e2f5ac08527a8b853de8f321",
                "Content-Type": "application/json",
            },
            "aws_credentials": {
                "aws-access-key": "********************",
                "aws-secret-key": "n+6MxKRIu03f4kN4/EvZc6fy5ZAB98CKKd/wLWzW"
            },
            "bucket": "bright-data-api"
        }
        
        # Dataset IDs - Update these with your actual dataset IDs
        self.dataset_ids = {
            "indeed": "gd_l4dx9j9sscpvs7no2",  # Indeed dataset ID
            "linkedin": "gd_lpfll7v5hcqtkxl6l"  # LinkedIn dataset ID
        }
        
        # Limits per platform
        self.limits = {
            "indeed": 1,    # 200 results per input for Indeed
            "linkedin": 1   # 300 results per input for LinkedIn
        }
        
        # Load configurations
        self.load_configurations()
    
    def load_configurations(self):
        """Load input configurations from JSON file."""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            self.indeed_inputs = config_data.get('indeed', {})
            self.linkedin_inputs = config_data.get('linkedin', {})
            self.metadata = config_data.get('metadata', {})
            
            print(f"📂 Loaded configurations from '{self.config_file}'")
            print(f"   • Indeed categories: {len(self.indeed_inputs)}")
            print(f"   • LinkedIn categories: {len(self.linkedin_inputs)}")
            print(f"   • Total Indeed inputs: {sum(len(inputs) for inputs in self.indeed_inputs.values())}")
            print(f"   • Total LinkedIn inputs: {sum(len(inputs) for inputs in self.linkedin_inputs.values())}")
            
        except FileNotFoundError:
            print(f"❌ Configuration file '{self.config_file}' not found")
            self.indeed_inputs = {}
            self.linkedin_inputs = {}
            self.metadata = {}
        except Exception as e:
            print(f"❌ Error loading configurations: {str(e)}")
            self.indeed_inputs = {}
            self.linkedin_inputs = {}
            self.metadata = {}
    
    def create_s3_directory_path(self, platform: str, category: str) -> str:
        """
        Create S3 directory path for storing results.
        
        Args:
            platform: 'indeed' or 'linkedin'
            category: Category name
            
        Returns:
            str: S3 directory path
        """
        today = datetime.datetime.now().strftime("%Y-%m-%d")
        return f"{today}/{platform}/{category}/"
    
    def clean_inputs_for_platform(self, platform: str, inputs: List[Dict]) -> List[Dict]:
        """
        Clean inputs based on platform requirements.
        
        Args:
            platform: 'indeed' or 'linkedin'
            inputs: Raw input configurations
            
        Returns:
            List[Dict]: Cleaned inputs for the platform
        """
        cleaned_inputs = []
        
        for input_dict in inputs:
            cleaned_input = input_dict.copy()
            
            if platform.lower() == 'indeed':
                # Indeed uses different field structure
                # Keep keyword_search instead of keyword
                if 'keyword' in cleaned_input:
                    del cleaned_input['keyword']
                
                # Keep required Indeed fields
                allowed_fields = {
                    'keyword_search', 'location', 'country', 'domain',
                    'date_posted', 'posted_by', 'location_radius'
                }
                
                # Keep only allowed fields and remove empty values
                cleaned_input = {k: v for k, v in cleaned_input.items() 
                               if k in allowed_fields and v != ""}
                
            elif platform.lower() == 'linkedin':
                # LinkedIn allowed fields (keep as is, seems to work)
                allowed_fields = {
                    'keyword', 'location', 'country', 'time_range', 'job_type',
                    'experience_level', 'remote', 'company', 'location_radius'
                }
                
                # Keep only allowed fields
                cleaned_input = {k: v for k, v in cleaned_input.items() if k in allowed_fields}
            
            # Remove empty string values to clean up the input
            cleaned_input = {k: v for k, v in cleaned_input.items() if v != ""}
            
            cleaned_inputs.append(cleaned_input)
        
        return cleaned_inputs

    def trigger_single_category_scraping(self, platform: str, category: str, inputs: List[Dict]) -> Dict[str, Any]:
        """
        Trigger scraping for a single category (one API call).
        
        Args:
            platform: 'indeed' or 'linkedin'
            category: Category name
            inputs: List of input configurations for this category
            
        Returns:
            Dict: API response
        """
        if not inputs:
            return {"error": "No inputs provided"}
        
        # Clean inputs for the specific platform
        cleaned_inputs = self.clean_inputs_for_platform(platform, inputs)
        
        if not cleaned_inputs:
            return {"error": "No valid inputs after cleaning"}
        
        limit_per_input = self.limits[platform]
        directory = self.create_s3_directory_path(platform, category)
        
        # Show sample of cleaned input for debugging
        if cleaned_inputs:
            sample_input = cleaned_inputs[0]
            print(f"   🔍 Sample cleaned input: {sample_input}")
        
        # Prepare API request
        params = {
            "dataset_id": self.dataset_ids[platform],
            "include_errors": "true",
            "type": "discover_new",
            "discover_by": "keyword",
        }
        
        # Both Indeed and LinkedIn use the same delivery structure
        data = {
            "deliver": {
                "type": "s3",
                "filename": {"template": "{[snapshot_id]}", "extension": "csv"},
                "bucket": self.api_config["bucket"],
                "credentials": self.api_config["aws_credentials"],
                "directory": directory
            },
            "input": cleaned_inputs
        }
        
        try:
            # Make API request
            response = requests.post(
                self.api_config["url"],
                headers=self.api_config["headers"],
                params=params,
                json=data
            )
            
            result = response.json()
            
            # Log the request
            print(f"✅ {platform.upper()} - {category}")
            print(f"   📊 {len(inputs)} inputs × {limit_per_input} results = ~{len(inputs) * limit_per_input} total results")
            print(f"   📁 S3 Path: {directory}")
            print(f"   🆔 Snapshot ID: {result.get('snapshot_id', 'N/A')}")
            
            # Add metadata to result
            result['category'] = category
            result['platform'] = platform
            result['input_count'] = len(inputs)
            result['expected_results'] = len(inputs) * limit_per_input
            result['s3_directory'] = directory
            
            return result
            
        except Exception as e:
            error_result = {
                "error": str(e),
                "category": category,
                "platform": platform,
                "input_count": len(inputs)
            }
            print(f"❌ Error scraping {platform} - {category}: {str(e)}")
            return error_result
    
    def scrape_indeed_all_categories(self, delay_seconds: int = 2) -> Dict[str, Dict[str, Any]]:
        """
        Scrape all Indeed categories (separate API call for each category).
        
        Args:
            delay_seconds: Delay between API calls to avoid rate limiting
            
        Returns:
            Dict: Results for each category
        """
        print(f"🎯 STARTING INDEED SCRAPING - {len(self.indeed_inputs)} CATEGORIES")
        print("=" * 60)
        
        results = {}
        
        for i, (category, inputs) in enumerate(self.indeed_inputs.items(), 1):
            print(f"\n📋 Category {i}/{len(self.indeed_inputs)}: {category}")
            print("-" * 40)
            
            result = self.trigger_single_category_scraping('indeed', category, inputs)
            results[category] = result
            
            # Add delay between requests (except for the last one)
            if i < len(self.indeed_inputs) and delay_seconds > 0:
                print(f"⏳ Waiting {delay_seconds} seconds before next request...")
                time.sleep(delay_seconds)
        
        # Summary
        successful = sum(1 for r in results.values() if 'snapshot_id' in r)
        total_expected = sum(r.get('expected_results', 0) for r in results.values())
        
        print(f"\n📊 INDEED SCRAPING SUMMARY:")
        print(f"   • Total categories: {len(self.indeed_inputs)}")
        print(f"   • Successful API calls: {successful}")
        print(f"   • Failed API calls: {len(results) - successful}")
        print(f"   • Expected total results: ~{total_expected:,}")
        
        return results
    
    def scrape_linkedin_all_categories(self, delay_seconds: int = 2) -> Dict[str, Dict[str, Any]]:
        """
        Scrape all LinkedIn categories (separate API call for each category).
        
        Args:
            delay_seconds: Delay between API calls to avoid rate limiting
            
        Returns:
            Dict: Results for each category
        """
        print(f"🔗 STARTING LINKEDIN SCRAPING - {len(self.linkedin_inputs)} CATEGORIES")
        print("=" * 60)
        
        results = {}
        
        for i, (category, inputs) in enumerate(self.linkedin_inputs.items(), 1):
            print(f"\n📋 Category {i}/{len(self.linkedin_inputs)}: {category}")
            print("-" * 40)
            
            result = self.trigger_single_category_scraping('linkedin', category, inputs)
            results[category] = result
            
            # Add delay between requests (except for the last one)
            if i < len(self.linkedin_inputs) and delay_seconds > 0:
                print(f"⏳ Waiting {delay_seconds} seconds before next request...")
                time.sleep(delay_seconds)
        
        # Summary
        successful = sum(1 for r in results.values() if 'snapshot_id' in r)
        total_expected = sum(r.get('expected_results', 0) for r in results.values())
        
        print(f"\n📊 LINKEDIN SCRAPING SUMMARY:")
        print(f"   • Total categories: {len(self.linkedin_inputs)}")
        print(f"   • Successful API calls: {successful}")
        print(f"   • Failed API calls: {len(results) - successful}")
        print(f"   • Expected total results: ~{total_expected:,}")
        
        return results
    
    def scrape_specific_categories(self, platform: str, categories: List[str], delay_seconds: int = 2) -> Dict[str, Dict[str, Any]]:
        """
        Scrape specific categories from a platform.
        
        Args:
            platform: 'indeed' or 'linkedin'
            categories: List of category names to scrape
            delay_seconds: Delay between API calls
            
        Returns:
            Dict: Results for each category
        """
        print(f"🎯 SCRAPING SPECIFIC {platform.upper()} CATEGORIES")
        print(f"Categories: {categories}")
        print("=" * 50)
        
        if platform.lower() == 'indeed':
            available_inputs = self.indeed_inputs
        elif platform.lower() == 'linkedin':
            available_inputs = self.linkedin_inputs
        else:
            print(f"❌ Invalid platform: {platform}")
            return {}
        
        results = {}
        
        for i, category in enumerate(categories, 1):
            if category not in available_inputs:
                print(f"⚠️  Category '{category}' not found in {platform} configurations")
                results[category] = {"error": f"Category not found"}
                continue
            
            print(f"\n📋 Category {i}/{len(categories)}: {category}")
            print("-" * 40)
            
            inputs = available_inputs[category]
            result = self.trigger_single_category_scraping(platform, category, inputs)
            results[category] = result
            
            # Add delay between requests (except for the last one)
            if i < len(categories) and delay_seconds > 0:
                print(f"⏳ Waiting {delay_seconds} seconds before next request...")
                time.sleep(delay_seconds)
        
        return results
    
    def scrape_all_platforms(self, delay_seconds: int = 5) -> Dict[str, Dict[str, Dict[str, Any]]]:
        """
        Scrape all categories from both platforms.
        
        Args:
            delay_seconds: Delay between platform switches
            
        Returns:
            Dict: Results organized by platform and category
        """
        print("🚀 STARTING COMPLETE SCRAPING - ALL PLATFORMS")
        print("=" * 70)
        
        all_results = {}
        
        # Scrape Indeed
        if self.indeed_inputs:
            indeed_results = self.scrape_indeed_all_categories(delay_seconds=2)
            all_results['indeed'] = indeed_results
            
            if self.linkedin_inputs and delay_seconds > 0:
                print(f"\n⏳ Waiting {delay_seconds} seconds before switching to LinkedIn...")
                time.sleep(delay_seconds)
        
        # Scrape LinkedIn
        if self.linkedin_inputs:
            linkedin_results = self.scrape_linkedin_all_categories(delay_seconds=2)
            all_results['linkedin'] = linkedin_results
        
        # Overall summary
        total_categories = len(self.indeed_inputs) + len(self.linkedin_inputs)
        total_successful = 0
        total_expected_results = 0
        
        for platform_results in all_results.values():
            total_successful += sum(1 for r in platform_results.values() if 'snapshot_id' in r)
            total_expected_results += sum(r.get('expected_results', 0) for r in platform_results.values())
        
        print(f"\n🎉 COMPLETE SCRAPING FINISHED!")
        print(f"📊 OVERALL SUMMARY:")
        print(f"   • Total categories scraped: {total_categories}")
        print(f"   • Successful API calls: {total_successful}")
        print(f"   • Expected total results: ~{total_expected_results:,}")
        print(f"   • Data saved to S3 in date-organized folders")
        
        # Save results summary
        self.save_scraping_results(all_results)
        
        return all_results
    
    def save_scraping_results(self, results: Dict[str, Dict[str, Dict[str, Any]]]):
        """
        Save scraping results summary to JSON file.
        
        Args:
            results: Complete scraping results
        """
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"scraping_results_{timestamp}.json"
        
        summary_data = {
            "metadata": {
                "scraping_timestamp": datetime.datetime.now().isoformat(),
                "config_file_used": self.config_file,
                "total_api_calls": sum(len(platform_results) for platform_results in results.values()),
                "platforms_scraped": list(results.keys())
            },
            "results": results
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(summary_data, f, indent=2, ensure_ascii=False)
            print(f"💾 Scraping results saved to '{filename}'")
        except Exception as e:
            print(f"⚠️  Could not save results: {str(e)}")
    
    def get_scraping_status(self) -> Dict[str, Any]:
        """
        Get current scraping configuration status.
        
        Returns:
            Dict: Status information
        """
        return {
            "config_file": self.config_file,
            "indeed_categories": len(self.indeed_inputs),
            "linkedin_categories": len(self.linkedin_inputs),
            "total_indeed_inputs": sum(len(inputs) for inputs in self.indeed_inputs.values()),
            "total_linkedin_inputs": sum(len(inputs) for inputs in self.linkedin_inputs.values()),
            "expected_api_calls": len(self.indeed_inputs) + len(self.linkedin_inputs),
            "limits": self.limits,
            "dataset_ids": self.dataset_ids
        }

# Convenience functions for easy usage
def create_scraper(config_file: str = "scraping_inputs.json") -> BrightDataScraper:
    """Create and return a BrightDataScraper instance."""
    return BrightDataScraper(config_file)

def scrape_everything(config_file: str = "scraping_inputs.json", delay_seconds: int = 5) -> Dict[str, Dict[str, Dict[str, Any]]]:
    """
    One-command function to scrape everything.
    
    Args:
        config_file: Path to input configurations JSON
        delay_seconds: Delay between platform switches
        
    Returns:
        Dict: Complete scraping results
    """
    scraper = BrightDataScraper(config_file)
    return scraper.scrape_all_platforms(delay_seconds)

def scrape_platform(platform: str, config_file: str = "scraping_inputs.json", delay_seconds: int = 2) -> Dict[str, Dict[str, Any]]:
    """
    Scrape all categories from a specific platform.
    
    Args:
        platform: 'indeed' or 'linkedin'
        config_file: Path to input configurations JSON
        delay_seconds: Delay between API calls
        
    Returns:
        Dict: Platform scraping results
    """
    scraper = BrightDataScraper(config_file)
    
    if platform.lower() == 'indeed':
        return scraper.scrape_indeed_all_categories(delay_seconds)
    elif platform.lower() == 'linkedin':
        return scraper.scrape_linkedin_all_categories(delay_seconds)
    else:
        print(f"❌ Invalid platform: {platform}")
        return {}

if __name__ == "__main__":
    # Example usage
    print("🚀 BrightData Scraper - Example Usage")
    print("=" * 50)
    
    # Create scraper
    scraper = create_scraper()
    
    # Show status
    status = scraper.get_scraping_status()
    print(f"📊 Scraping Status:")
    for key, value in status.items():
        print(f"   {key}: {value}")
    
    # Uncomment to run scraping
    results = scrape_everything()
    print("\n💡 To start scraping, uncomment the last line or use:")
    print("   results = scrape_everything()")
def debug_input_cleaning(config_file: str = "scraping_inputs.json", platform: str = "indeed"):
    """
    Debug function to show how inputs are cleaned for a platform.
    
    Args:
        config_file: Path to input configurations JSON
        platform: 'indeed' or 'linkedin'
    """
    print(f"🔍 DEBUGGING INPUT CLEANING FOR {platform.upper()}")
    print("=" * 50)
    
    scraper = BrightDataScraper(config_file)
    
    if platform.lower() == 'indeed':
        inputs_dict = scraper.indeed_inputs
    elif platform.lower() == 'linkedin':
        inputs_dict = scraper.linkedin_inputs
    else:
        print(f"❌ Invalid platform: {platform}")
        return
    
    if not inputs_dict:
        print(f"⚠️  No {platform} inputs found")
        return
    
    for category, inputs in inputs_dict.items():
        print(f"\n📋 Category: {category}")
        print(f"   Original inputs: {len(inputs)}")
        
        if inputs:
            # Show original input
            original = inputs[0]
            print(f"   📄 Original sample: {original}")
            
            # Show cleaned input
            cleaned = scraper.clean_inputs_for_platform(platform, inputs)
            if cleaned:
                print(f"   ✨ Cleaned sample: {cleaned[0]}")
                
                # Show what was removed
                removed_fields = set(original.keys()) - set(cleaned[0].keys())
                if removed_fields:
                    print(f"   ❌ Removed fields: {list(removed_fields)}")
                else:
                    print(f"   ✅ No fields removed")
            else:
                print(f"   ⚠️  No valid inputs after cleaning")
        
        print("-" * 40)

def scrape_indeed_only(config_file: str = "scraping_inputs.json", delay_seconds: int = 3):
    """
    Convenience function to scrape Indeed only with proper error handling.
    
    Args:
        config_file: Path to input configurations JSON
        delay_seconds: Delay between API calls
        
    Returns:
        Dict: Indeed scraping results
    """
    print("🎯 SCRAPING INDEED ONLY WITH CLEANED INPUTS")
    print("=" * 50)
    
    # First, debug the input cleaning
    debug_input_cleaning(config_file, 'indeed')
    
    print("\n" + "=" * 50)
    print("🚀 STARTING INDEED SCRAPING...")
    print("=" * 50)
    
    # Now scrape
    results = scrape_platform('indeed', config_file, delay_seconds)
    
    return results